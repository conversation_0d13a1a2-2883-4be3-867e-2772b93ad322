"""
可视化数据生成模块

提供可视化JSON数据生成功能，支持柱状图、折线图、饼图、表格和文本五种显示类型。
"""

import logging
from typing import Dict, Any, Optional, List, Union


# ===== 图表类型常量 =====
class ChartType:
    """图表类型常量"""
    BAR = "bar"      # 柱状图
    STACKED_BAR = "stacked_bar"  # 堆叠柱状图
    LINE = "line"    # 趋势图/折线图
    PIE = "pie"      # 饼图
    TABLE = "table"  # 表格
    TEXT = "text"    # 文本


# ===== 服务名称常量 =====
IS_BILLING_CYCLE = "is_billing_cycle_service"
LATN_ID = "latn_id_service"
USER_PROFILE = "user_profile_service"
FLUX_OVERFLOW_FEE = "flux_overflow_fee_service"
UNLIMITED_PACKAGE = "unlimited_package_service"
REMAIN_ACCU = "remain_accu_service"
CHARGE_DETAIL = "charge_detail_service"
NON_UNLIMITED_FLUX_DISPUTE = "non_unlimited_flux_dispute_service"
USERREL_100800 = "userrel_100800_service"
PRIMARY_SECONDARY_OVERFLOW = "primary_secondary_card_overflow_usage"
PRIMARY_SECONDARY_CARD_USAGE = "primarySecondaryCardUsage"
MAIN_SECONDARY_CARD_USAGE = "main_secondary_card_usage"
QRY_JZRZ_DAY_FEE_BILL = "qryJzrzDayFeeBill"
INTERNET_DETAIL_SERVICE = "internet_detail_service"
BENEFITS_PACKAGE_FEE_RELATION = "benefits_package_fee_relation"
BENEFITS_PACKAGE_BILL_FEE = "benefits_package_bill_fee"


# ===== 可视化生成器 =====
class VisualizationGenerator:
    """可视化数据生成器"""
    
    # 预定义颜色方案
    COLORS = [
        "#3498db", "#2ecc71", "#e74c3c", "#f39c12", "#9b59b6",
        "#1abc9c", "#e67e22", "#34495e", "#16a085", "#27ae60"
    ]
    
    def create_bar_chart(self, title: str, labels: List[str], datasets: List[Dict],
                        x_axis_title: str = "", y_axis_title: str = "",
                        description: str = "", y_axis_ticks: Optional[List[Union[int, float]]] = None) -> Dict[str, Any]:
        """创建柱状图"""
        chart_data = {
            "chart_type": ChartType.BAR,
            "title": title,
            "description": description,
            "data": {
                "labels": labels,
                "datasets": datasets,
                "x_axis_title": x_axis_title,
                "y_axis_title": y_axis_title,
                "show_legend": True
            }
        }
        
        # 如果提供了Y轴刻度，添加到配置中
        if y_axis_ticks:
            chart_data["data"]["y_axis_ticks"] = y_axis_ticks
            
        return chart_data
    
    def create_stacked_bar_chart(self, title: str, labels: List[str], datasets: List[Dict],
                                x_axis_title: str = "", y_axis_title: str = "",
                                description: str = "", y_axis_ticks: Optional[List[Union[int, float]]] = None) -> Dict[str, Any]:
        """创建堆叠柱状图"""
        chart_data = {
            "chart_type": ChartType.STACKED_BAR,
            "title": title,
            "description": description,
            "data": {
                "labels": labels,
                "datasets": datasets,
                "x_axis_title": x_axis_title,
                "y_axis_title": y_axis_title,
                "show_legend": True,
                "stacked": True  # 堆叠柱状图的关键标识
            }
        }
        
        # 如果提供了Y轴刻度，添加到配置中
        if y_axis_ticks:
            chart_data["data"]["y_axis_ticks"] = y_axis_ticks
            
        return chart_data
    
    def create_line_chart(self, title: str, labels: List[str], datasets: List[Dict],
                         x_axis_title: str = "", y_axis_title: str = "",
                         description: str = "") -> Dict[str, Any]:
        """创建折线图"""
        return {
            "chart_type": ChartType.LINE,
            "title": title,
            "description": description,
            "data": {
                "labels": labels,
                "datasets": datasets,
                "x_axis_title": x_axis_title,
                "y_axis_title": y_axis_title,
                "show_legend": True
            }
        }
    
    def create_pie_chart(self, title: str, labels: List[str], data: List[float],
                        description: str = "") -> Dict[str, Any]:
        """创建饼图"""
        return {
            "chart_type": ChartType.PIE,
            "title": title,
            "description": description,
            "data": {
                "labels": labels,
                "datasets": [{
                    "data": data,
                    "backgroundColor": self.COLORS[:len(data)]
                }],
                "show_legend": True
            }
        }
    
    def create_table(self, title: str, headers: List[str], rows: List[List[str]],
                    description: str = "") -> Dict[str, Any]:
        """创建表格"""
        return {
            "chart_type": ChartType.TABLE,
            "title": title,
            "description": description,
            "data": {
                "headers": headers,
                "rows": rows
            }
        }
    
    def create_text(self, title: str, content: Union[str, List[str]],
                   description: str = "") -> Dict[str, Any]:
        """创建文本显示"""
        return {
            "chart_type": ChartType.TEXT,
            "title": title,
            "description": description,
            "data": {
                "content": content
            }
        }


# ===== 服务可视化处理器 =====
class ServiceVisualizationProcessor:
    """服务可视化数据处理器"""
    
    def __init__(self):
        self.generator = VisualizationGenerator()
    
    def process_by_service_type(self, service_type: str, data: Any,
                               custom_title: Optional[str] = None,
                               custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """根据服务类型处理数据并生成可视化"""
        
        processors = {
            USER_PROFILE: self._process_user_profile,
            FLUX_OVERFLOW_FEE: self._process_flux_overflow_fee,
            UNLIMITED_PACKAGE: self._process_unlimited_package,
            REMAIN_ACCU: self._process_remain_accu,
            CHARGE_DETAIL: self._process_charge_detail,
            IS_BILLING_CYCLE: self._process_text_data,
            LATN_ID: self._process_text_data,
            NON_UNLIMITED_FLUX_DISPUTE: self._process_dispute_data,
            USERREL_100800: self._process_userrel_100800,
            PRIMARY_SECONDARY_OVERFLOW: self._process_primary_secondary_overflow,
            PRIMARY_SECONDARY_CARD_USAGE: self._process_primary_secondary_card_usage,
            MAIN_SECONDARY_CARD_USAGE: self._process_main_secondary_card_usage,
            QRY_JZRZ_DAY_FEE_BILL: self._process_fee_bill_data,
            INTERNET_DETAIL_SERVICE: self._process_internet_detail_service,
            BENEFITS_PACKAGE_FEE_RELATION: self._process_benefits_package_fee_relation,
            BENEFITS_PACKAGE_BILL_FEE: self._process_benefits_package_bill_fee
        }
        
        processor = processors.get(service_type, self._process_text_data)
        return processor(data, custom_title, custom_description)
    
    def _process_user_profile(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                             custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理用户资料数据"""
        title = custom_title or "用户资料"
        
        # 检查是否有input_details字段 
        input_details = data.get("input_details", [])
        if input_details and isinstance(input_details, list):
            # 处理input_details数组
            all_rows = []
            ordered_fields = [
                ("billing_nb", "计费号码"),
                ("PROD_INST_ID", "用户实例"),
                ("ACCT_ID", "账户"),
                ("PROD_ID", "产品"),
                ("latn_id", "本地网"),
                ("billing_cycle", "账期"),
                ("disputeType", "争议类型"),
                ("charge", "用户投诉费用")
            ]
            
            headers = [display_name for _, display_name in ordered_fields]
            
            for detail in input_details:
                if isinstance(detail, dict):
                    # 检查是否有错误消息
                    if detail.get("Is_user_profile", 1) == 0:
                        message = detail.get("message", "查询失败")
                        return [self.generator.create_text(title, message, custom_description or "")]
                    
                    # 构建数据行
                    row = []
                    for key, _ in ordered_fields:
                        if key in detail and detail[key] is not None:
                            value = detail[key]
                            
                            # 格式化特殊字段
                            if key == "disputeType":
                                dispute_mapping = {
                                    "1": "流量费用争议",
                                    "2": "流量包功能费争议", 
                                    "3": "国际漫游流量费用争议"
                                }
                                formatted_value = dispute_mapping.get(str(value), f"争议类型{value}")
                            elif key == "PROD_ID":
                                # 产品ID格式化
                                if str(value) == "41010200":
                                    formatted_value = f"{value} (手机产品)"
                                else:
                                    formatted_value = f"{value} (非手机产品)"
                            else:
                                formatted_value = str(value)
                            
                            row.append(formatted_value)
                        else:
                            row.append("")
                    
                    all_rows.append(row)
            
            if all_rows:
                return [self.generator.create_table(title, headers, all_rows, custom_description or f"用户资料查询成功，共{len(all_rows)}条记录")]

    
    def _process_flux_overflow_fee(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                  custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理流量溢出费用数据"""
        title = custom_title or "流量溢出费用明细"
        
        # 检查是否有input_details字段 
        input_details = data.get("input_details", [])
        if input_details and isinstance(input_details, list):
            all_results = []
            
            for detail in input_details:
                if isinstance(detail, dict):
                    # 检查是否有错误消息
                    if detail.get("Is_flux_fee", 1) == 0:
                        message = detail.get("message", "查询失败")
                        all_results.append(self.generator.create_text(title, message, custom_description or ""))
                        continue
                    
                    # 处理fluxOfrList（disputeType=2 流量包功能费争议）
                    has_flux_ofr_list = detail.get("hasFluxOfrList", 0)
                    flux_ofr_list = detail.get("fluxOfrList", [])
                    if has_flux_ofr_list == 1 and flux_ofr_list:
                        flux_title = "流量包功能费明细"
                        headers = ["销售品", "销售品名称", "销售品实例", "订单号"]
                        rows = []
                        
                        for ofr_item in flux_ofr_list:
                            if isinstance(ofr_item, dict):
                                offer_id = ofr_item.get("OFFER_ID", "")
                                offer_name = ofr_item.get("OFFER_NAME", "")
                                offer_inst_id = ofr_item.get("offerInstId", "")
                                last_order_item_id = ofr_item.get("lastOrderItemId", "")
                                
                                rows.append([
                                    str(offer_id) if offer_id else "",
                                    str(offer_name) if offer_name else "",
                                    str(offer_inst_id) if offer_inst_id else "",
                                    str(last_order_item_id) if last_order_item_id else ""
                                ])
                        
                        if rows:
                            all_results.append(self.generator.create_table(flux_title, headers, rows, f"共{len(rows)}条流量包记录"))
                        continue
                    
                    # 处理普通的流量费用争议（列表形式的数据）
                    if isinstance(detail, list):
                        headers = ["销售品实例", "溢出流量", "费用", "账期项名称", "销售品", "销售品名称"]
                        rows = []
                        
                        for fee_item in detail:
                            if isinstance(fee_item, dict):
                                offer_inst_id = fee_item.get("offer_inst_id", "")
                                flux_over = fee_item.get("flux_over", 0)
                                charge = fee_item.get("charge", 0)
                                acct_item_name = fee_item.get("acct_item_name", "")
                                ofr_id = fee_item.get("ofr_id", "")
                                offer_name = fee_item.get("offer_name", "")
                                
                                # 格式化溢出流量，显示为整数
                                try:
                                    flux_over_str = str(int(float(flux_over))) if flux_over else "0"
                                except (ValueError, TypeError):
                                    flux_over_str = str(flux_over) if flux_over else "0"
                                
                                # 格式化费用，如果是数字则保留2位小数
                                try:
                                    charge_str = f"{float(charge):.2f}" if charge else "0.00"
                                except (ValueError, TypeError):
                                    charge_str = str(charge) if charge else "0.00"
                                
                                rows.append([
                                    str(offer_inst_id),
                                    flux_over_str,
                                    charge_str,
                                    str(acct_item_name),
                                    str(ofr_id),
                                    str(offer_name)
                                ])
                        
                        if rows:
                            all_results.append(self.generator.create_table(title, headers, rows, f"共{len(rows)}条费用记录"))
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到流量费用明细", custom_description or "")]
    
    def _process_unlimited_package(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                  custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理不限量套餐数据"""
        title = custom_title or "用户订购套餐明细"
        
        # 检查是否有valid_pkgs字段 
        valid_pkgs = data.get("valid_pkgs", [])
        if valid_pkgs and isinstance(valid_pkgs, list):
            headers = ["套餐实例", "套餐", "套餐名称", "套餐优先级", "量本类型", "是否不限量", "是否提速包", "生效时间", "失效时间", "封顶"]
            rows = []
            
            unlimited_count = 0
            speedup_count = 0
            
            for package in valid_pkgs:
                if isinstance(package, dict):
                    # 统计
                    is_unlimited = package.get("is_unlimited", 0)
                    if is_unlimited == 1:
                        unlimited_count += 1
                    
                    is_speedup = package.get("is_speedup", 0)
                    if is_speedup == 1:
                        speedup_count += 1
                    
                    # 格式化字段
                    unlimited_text = "是" if is_unlimited == 1 else "否"
                    speedup_text = "是" if is_speedup == 1 else "否"
                    
                    # 获取封顶字段
                    cap_limit_desc = package.get("cap_limit_desc", "")
                    
                    rows.append([
                        str(package.get("offer_inst_id", "")),
                        str(package.get("offer_id", "")),
                        str(package.get("offer_name", "")),
                        str(package.get("offer_priority", "")),
                        str(package.get("ACCU_TYPE_ID", "")),
                        unlimited_text,
                        speedup_text,
                        str(package.get("EFF_DATE", "")),
                        str(package.get("EXP_DATE", "")),
                        str(cap_limit_desc)
                    ])
            
            # 生成描述信息
            description = f"共{len(rows)}个套餐"
            if unlimited_count > 0:
                description += f"，其中{unlimited_count}个不限量套餐"
            if speedup_count > 0:
                description += f"，{speedup_count}个提速包"
            
            return [self.generator.create_table(title, headers, rows, custom_description or description)]

    
    def _process_remain_accu(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                             custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理剩余量数据"""
        title = custom_title or "流量剩余量分析"
        
        offer_details = data.get("offerAccuDetail", [])
        if not offer_details:
            message = data.get("message", "未查询到流量剩余量信息")
            return [self.generator.create_text(title, message, custom_description or "")]
        
        # DCTaskApi.py的数据结构是直接的量本列表，不是嵌套结构
        # 按accu_type_id分组收集数据
        accu_type_data = {}
        
        for accu in offer_details:
            if isinstance(accu, dict):
                accu_type_id = accu.get("accu_type_id") or "未知"
                accu_type_name = accu.get("accuTypeName")
                if not accu_type_name or accu_type_name.strip() == "":
                    accu_type_name = f"量本类型ID：{accu_type_id}"
                
                if accu_type_id not in accu_type_data:
                    accu_type_data[accu_type_id] = {
                        "name": accu_type_name,
                        "initial": 0,
                        "used": 0,
                        "remain": 0
                    }
                
                accu_type_data[accu_type_id]["initial"] += int(float(accu.get("INIT_VAL", 0) or 0))
                accu_type_data[accu_type_id]["used"] += int(float(accu.get("ACCU_USED_VAL", 0) or 0))
                accu_type_data[accu_type_id]["remain"] += int(float(accu.get("ACCU_VAL", 0) or 0))
        
        if not accu_type_data:
            return [self.generator.create_text(title, "未查询到有效的量本数据", custom_description or "")]
        
        # 构建图表数据
        labels = []
        initial_data = []
        used_data = []
        remain_data = []
        
        for accu_type_id, data_item in accu_type_data.items():
            labels.append(data_item["name"])
            initial_data.append(int(data_item["initial"]))
            used_data.append(int(data_item["used"]))
            remain_data.append(int(data_item["remain"]))
        
        datasets = [
            {
                "label": "初始量",
                "data": initial_data,
                "backgroundColor": "#3498db"
            },
            {
                "label": "已使用量",
                "data": used_data,
                "backgroundColor": "#e74c3c"
            },
            {
                "label": "剩余量",
                "data": remain_data,
                "backgroundColor": "#2ecc71"
            }
        ]
        
        total_types = len(accu_type_data)
        total_remain = sum(data_item["remain"] for data_item in accu_type_data.values())
        description = f"共{total_types}种量本类型，总剩余{int(total_remain)}"
        
        # 计算合适的Y轴刻度，基于初始值（最大值）
        if initial_data and any(v > 0 for v in initial_data):
            max_initial = max(initial_data)
            
            # 根据最大初始值确定刻度间隔
            if max_initial <= 100:
                step = 20
            elif max_initial <= 1000:
                step = 200
            elif max_initial <= 10000:
                step = 2000
            elif max_initial <= 100000:
                step = 20000
            else:
                step = 200000
            
            # 生成Y轴刻度，限制最多8个刻度点，使用整数
            y_ticks = [0]
            current = step
            max_ticks = 8  # 除了0之外最多8个刻度
            while current <= max_initial * 1.1 and len(y_ticks) < max_ticks:
                y_ticks.append(int(current))
                current += step
            
            # 确保最后一个刻度覆盖最大值
            if y_ticks[-1] < max_initial:
                y_ticks.append(int(max_initial * 1.1))
        else:
            y_ticks = [0, 100, 500, 1000, 5000]
        
        return [self.generator.create_bar_chart(title, labels, datasets, "量本类型", "流量(KB)", custom_description or description, y_ticks)]
    
    def _process_charge_detail(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                              custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理收费清单数据"""
        title = custom_title or "收费清单明细"
        
        # 检查是否有BillItemsDetail字段 
        bill_items_detail = data.get("BillItemsDetail", [])
        if bill_items_detail and isinstance(bill_items_detail, list):
            all_results = []
            
            for bill_group in bill_items_detail:
                if isinstance(bill_group, dict):
                    accnun_detail = bill_group.get("accNun_detail", [])
                    if accnun_detail and isinstance(accnun_detail, list):
                        headers = ["计费号码", "销售品", "流量总量", "费用总计", "单价"]
                        rows = []
                        
                        total_flow = 0
                        total_fee = 0
                        
                        for item in accnun_detail:
                            if isinstance(item, dict):
                                billing_nb = item.get("billing_nb", "")
                                offer_id = item.get("offer_id", "")
                                sum_flow = item.get("sum_flow", 0)
                                sum_fee = item.get("sum_fee", 0)
                                price = item.get("price", 0)
                                
                                # 累计统计
                                try:
                                    total_flow += float(sum_flow) if sum_flow else 0
                                    total_fee += float(sum_fee) if sum_fee else 0
                                except (ValueError, TypeError):
                                    pass
                                
                                # 格式化数据
                                try:
                                    flow_str = f"{float(sum_flow):.2f}" if sum_flow else "0.00"
                                except (ValueError, TypeError):
                                    flow_str = str(sum_flow) if sum_flow else "0.00"
                                
                                try:
                                    fee_str = f"{float(sum_fee):.2f}" if sum_fee else "0.00"
                                except (ValueError, TypeError):
                                    fee_str = str(sum_fee) if sum_fee else "0.00"
                                
                                try:
                                    price_str = f"{float(price):.4f}" if price else "0.0000"
                                except (ValueError, TypeError):
                                    price_str = str(price) if price else "0.0000"
                                
                                rows.append([
                                    str(billing_nb),
                                    str(offer_id),
                                    flow_str,
                                    fee_str,
                                    price_str
                                ])
                        
                        if rows:
                            description = f"共{len(rows)}条记录，总流量{total_flow:.2f}，总费用{total_fee:.2f}元"
                            all_results.append(self.generator.create_table(title, headers, rows, description))
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到收费清单明细", custom_description or "")]

    
    def _process_text_data(self, data: Any, custom_title: Optional[str] = None,
                          custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理文本数据"""
        title = custom_title or "查询结果"
        
        if isinstance(data, dict):
            # 只显示message和result字段，过滤掉其他技术性字段
            text_parts = []
            
            # 优先显示result字段
            if "result" in data and data["result"] is not None:
                text_parts.append(str(data["result"]))
            
            # 其次显示message字段
            if "message" in data and data["message"] is not None:
                text_parts.append(str(data["message"]))
            
            # 如果都没有，显示默认信息
            if not text_parts:
                text_parts = ["查询完成"]
            
            content = text_parts
        else:
            content = str(data)
        
        return [self.generator.create_text(title, content, custom_description or "")]
    
    def _process_dispute_data(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                              custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理争议诊断数据"""
        title = custom_title or "流量争议诊断结果"
        
        if isinstance(data, dict) and "dispute_details" in data:
            details = data["dispute_details"]
            if isinstance(details, list) and details:
                headers = ["时间", "业务类型", "流量", "费用", "诊断结果"]
                rows = []
                
                for detail in details:
                    if isinstance(detail, dict):
                        rows.append([
                            detail.get("time", ""),
                            detail.get("service_type", ""),
                            str(detail.get("traffic", 0)),
                            str(detail.get("fee", 0)),
                            detail.get("diagnosis", "")
                        ])
                
                return [self.generator.create_table(title, headers, rows, custom_description or f"共{len(rows)}条诊断记录")]
        
        return self._process_text_data(data, title, custom_description)
    
    def _process_userrel_100800(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理主副卡关系数据"""
        title = custom_title or "主副卡关系"
        
        # 检查是否有accNum_detail_10080字段 
        accnum_detail = data.get("accNum_detail_10080", [])
        if accnum_detail and isinstance(accnum_detail, list):
            headers = ["主卡号码", "副卡号码", "生效时间", "失效时间"]
            rows = []
            
            for detail in accnum_detail:
                if isinstance(detail, dict):
                    main_card = detail.get("主卡号码", "")
                    sub_card = detail.get("副卡号码", "")
                    eff_date = detail.get("生效时间", "")
                    exp_date = detail.get("失效时间", "")
                    
                    # 格式化时间字段
                    if hasattr(eff_date, 'strftime'):
                        eff_date_str = eff_date.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        eff_date_str = str(eff_date)
                    
                    if hasattr(exp_date, 'strftime'):
                        exp_date_str = exp_date.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        exp_date_str = str(exp_date)
                    
                    rows.append([
                        str(main_card),
                        str(sub_card),
                        eff_date_str,
                        exp_date_str
                    ])
            
            if rows:
                return [self.generator.create_table(title, headers, rows, custom_description or f"共{len(rows)}条主副卡关系记录")]
        
        return self._process_text_data(data, title, custom_description)
    
    def _process_primary_secondary_overflow(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                          custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理主副卡套外使用量数据"""
        title = custom_title or "主副卡套外使用量明细"
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        if input_details and isinstance(input_details, list):
            all_results = []
            
            for detail in input_details:
                if isinstance(detail, dict):
                    # 检查是否有错误消息
                    if detail.get("status") == "error":
                        message = detail.get("message", "查询失败")
                        all_results.append(self.generator.create_text(title, message, custom_description or ""))
                        continue
                    
                    billing_nb = detail.get("billing_nb", "")
                    billing_cycle = detail.get("billing_cycle", "")
                    overflow_usage_details = detail.get("overflowUsageDetails", [])
                    
                    # 如果有套外使用量数据，创建堆叠柱状图
                    if overflow_usage_details and isinstance(overflow_usage_details, list):
                        current_title = f"{title} - 号码{billing_nb} ({billing_cycle})"
                        
                        # 收集数据：按账号(acc_nbr)分组，按量本类型名称(accu_type_name)堆叠
                        account_usage_data = {}  # {acc_nbr: {accu_type_name: usage_val}}
                        all_accu_types = set()  # 所有量本类型名称集合
                        
                        for usage_detail in overflow_usage_details:
                            if isinstance(usage_detail, dict):
                                acc_nbr = usage_detail.get("accNbr", "")
                                accu_qry_list = usage_detail.get("accuQryList", [])
                                
                                # 处理每个量本记录
                                for accu_info in accu_qry_list:
                                    if isinstance(accu_info, dict):
                                        accu_type_name = accu_info.get("accuTypeName", "")
                                        usage_val = accu_info.get("usageVal", 0)
                                        
                                        # 如果量本类型名称为空，使用默认名称
                                        if not accu_type_name or accu_type_name.strip() == "":
                                            accu_type_id = accu_info.get("accuTypeId", "")
                                            accu_type_name = f"量本类型{accu_type_id}" if accu_type_id else "未知类型"
                                        
                                        # 初始化账号数据结构
                                        if acc_nbr not in account_usage_data:
                                            account_usage_data[acc_nbr] = {}
                                        
                                        # 累计相同账号和量本类型的使用量
                                        try:
                                            usage_val_float = float(usage_val) if usage_val else 0
                                            if accu_type_name in account_usage_data[acc_nbr]:
                                                account_usage_data[acc_nbr][accu_type_name] += usage_val_float
                                            else:
                                                account_usage_data[acc_nbr][accu_type_name] = usage_val_float
                                        except (ValueError, TypeError):
                                            if accu_type_name not in account_usage_data[acc_nbr]:
                                                account_usage_data[acc_nbr][accu_type_name] = 0
                                        
                                        # 记录所有量本类型
                                        all_accu_types.add(accu_type_name)
                        
                        if account_usage_data and all_accu_types:
                            # 准备图表数据
                            labels = list(account_usage_data.keys())  # x轴：账号列表
                            accu_types_list = sorted(list(all_accu_types))  # 量本类型列表（排序以保持一致性）
                            
                            # 为每个量本类型创建数据集
                            datasets = []
                            all_data_values = []  # 收集所有数据值用于计算Y轴范围
                            
                            for i, accu_type in enumerate(accu_types_list):
                                data_values = []
                                for acc_nbr in labels:
                                    # 获取该账号该量本类型的使用量，转换为GB单位
                                    usage_kb = account_usage_data[acc_nbr].get(accu_type, 0)
                                    # 将KB转换为GB：KB / 1024 / 1024，保留4位小数
                                    usage_gb = round(usage_kb / 1024 / 1024, 4)
                                    data_values.append(usage_gb)
                                    all_data_values.append(usage_gb)
                                
                                # 为每个量本类型分配颜色
                                color = self.generator.COLORS[i % len(self.generator.COLORS)]
                                
                                datasets.append({
                                    "label": accu_type,
                                    "data": data_values,
                                    "backgroundColor": color
                                })
                            
                            # 计算堆叠后的最大值（每个账号的各类型使用量总和）
                            stacked_totals = []
                            for acc_nbr in labels:
                                total_for_account = sum(
                                    round(account_usage_data[acc_nbr].get(accu_type, 0) / 1024 / 1024, 4)
                                    for accu_type in accu_types_list
                                )
                                stacked_totals.append(total_for_account)
                            
                            # 根据堆叠后的最大值计算Y轴刻度
                            max_stacked_value = max(stacked_totals) if stacked_totals else 0
                            y_axis_ticks = self._calculate_y_axis_ticks(max_stacked_value)
                            
                            # 计算总使用量用于描述
                            total_usage = sum(
                                sum(type_usage.values()) for type_usage in account_usage_data.values()
                            )
                            total_usage_str = format_data_size(total_usage)
                            
                            description = f"共{len(labels)}个账号，{len(accu_types_list)}种量本类型，总使用量{total_usage_str}"
                            
                            # 创建堆叠柱状图
                            all_results.append(self.generator.create_stacked_bar_chart(
                                title=current_title,
                                labels=labels,
                                datasets=datasets,
                                x_axis_title="账号",
                                y_axis_title="使用量(GB)",
                                description=description,
                                y_axis_ticks=y_axis_ticks
                            ))
                        else:
                            # 没有有效的套外使用量数据
                            no_data_title = f"号码{billing_nb} ({billing_cycle})"
                            all_results.append(self.generator.create_text(no_data_title, "未查询到有效的套外使用量数据", ""))
                    else:
                        # 没有套外使用量数据
                        no_data_title = f"号码{billing_nb} ({billing_cycle})"
                        all_results.append(self.generator.create_text(no_data_title, "未查询到套外使用量数据", ""))
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到任何套外使用量数据", custom_description or "")]
        
        return self._process_text_data(data, title, custom_description)
    
    def _calculate_y_axis_ticks(self, max_value: float) -> List[float]:
        """
        根据最大值动态计算Y轴刻度
        
        Args:
            max_value: 数据的最大值
            
        Returns:
            Y轴刻度列表
        """
        if max_value <= 0:
            return [0, 0.1, 0.5, 1.0, 5.0]
        
        # 确定合适的刻度间隔
        if max_value <= 0.1:
            step = 0.01
        elif max_value <= 0.5:
            step = 0.05
        elif max_value <= 1.0:
            step = 0.1
        elif max_value <= 5.0:
            step = 0.5
        elif max_value <= 10.0:
            step = 1.0
        elif max_value <= 50.0:
            step = 5.0
        elif max_value <= 100.0:
            step = 10.0
        elif max_value <= 500.0:
            step = 50.0
        else:
            step = 100.0
        
        # 生成刻度，从0开始，最多生成10个刻度点
        ticks = [0]
        current = step
        max_ticks = 10
        
        # 计算上限，确保覆盖最大值
        upper_limit = max_value * 1.1  # 上限为最大值的110%
        
        while current <= upper_limit and len(ticks) < max_ticks:
            ticks.append(round(current, 4))
            current += step
        
        # 确保最后一个刻度能覆盖最大值的110%
        if ticks[-1] < upper_limit:
            ticks.append(round(upper_limit, 4))
        
        return ticks
    
    def _process_primary_secondary_card_usage(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                             custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理主副卡套餐使用量数据"""
        title = custom_title or "主副卡套餐使用量明细"
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        
        if input_details and isinstance(input_details, list):
            all_results = []
            
            for detail in input_details:
                if isinstance(detail, dict):
                    billing_nb = detail.get("billing_nb", "")
                    billing_cycle = detail.get("billingCycle", "")
                    prod_inst_id = detail.get("PROD_INST_ID", "")
                    usage_details = detail.get("usageDetails", [])
                    
                    # 检查是否有usageDetails数据
                    if usage_details and isinstance(usage_details, list):
                        current_title = f"{title} - 号码{billing_nb} ({billing_cycle})"
                        headers = ["销售品ID", "销售品实例ID", "账号", "使用量", "量本类型ID", "量本类型属性"]
                        rows = []
                        
                        total_usage = 0  # 统计总使用量
                        
                        for usage_detail in usage_details:
                            if isinstance(usage_detail, dict):
                                offer_id = usage_detail.get("offerId", "")
                                offer_inst_id = usage_detail.get("offerInstId", "")
                                user_usage_list = usage_detail.get("userUsageList", [])
                                
                                # 处理每个用户使用记录
                                for user_usage in user_usage_list:
                                    if isinstance(user_usage, dict):
                                        acc_nbr = user_usage.get("accNbr", "")
                                        usage_amount = user_usage.get("usageAmount", 0)
                                        accu_type_id = user_usage.get("accuTypeId", "")
                                        accu_type_attr = user_usage.get("accuTypeAttr", "")
                                        
                                        # 累计使用量
                                        try:
                                            total_usage += float(usage_amount) if usage_amount else 0
                                        except (ValueError, TypeError):
                                            pass
                                        
                                        # 格式化使用量显示
                                        usage_str = format_data_size(usage_amount)
                                        
                                        rows.append([
                                            str(offer_id),
                                            str(offer_inst_id),
                                            str(acc_nbr),
                                            usage_str,
                                            str(accu_type_id),
                                            str(accu_type_attr)
                                        ])
                        
                        if rows:
                            # 格式化总使用量
                            total_usage_str = format_data_size(total_usage)
                            description = f"产品实例ID: {prod_inst_id}, 共{len(rows)}条使用记录，总使用量{total_usage_str}"
                            all_results.append(self.generator.create_table(current_title, headers, rows, description))
                        else:
                            # 没有使用量数据
                            no_data_title = f"号码{billing_nb} ({billing_cycle})"
                            all_results.append(self.generator.create_text(no_data_title, "未查询到套餐使用量数据", ""))
                    else:
                        # 没有使用量数据
                        no_data_title = f"号码{billing_nb} ({billing_cycle})"
                        all_results.append(self.generator.create_text(no_data_title, "未查询到套餐使用量数据", ""))
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到任何套餐使用量数据", custom_description or "")]
        
        return self._process_text_data(data, title, custom_description)
    
    def _process_main_secondary_card_usage(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                          custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理主副卡使用量数据"""
        title = custom_title or "主副卡使用量明细"
        
        logging.info(f"[主副卡使用量可视化] 开始处理数据，自定义标题: {custom_title}")
        logging.debug(f"[主副卡使用量可视化] 输入数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        logging.info(f"[主副卡使用量可视化] input_details检查 - 存在: {bool(input_details)}, 类型: {type(input_details)}, 长度: {len(input_details) if isinstance(input_details, list) else 'N/A'}")
        
        if input_details and isinstance(input_details, list):
            all_results = []
            logging.info(f"[主副卡使用量可视化] 开始处理{len(input_details)}个详情项")
            
            for i, detail in enumerate(input_details):
                logging.info(f"[主副卡使用量可视化] 处理第{i+1}个详情项，类型: {type(detail)}")
                
                if isinstance(detail, dict):
                    # 检查是否有错误消息
                    status = detail.get("status")
                    logging.debug(f"[主副卡使用量可视化] 详情项{i+1} - 状态: {status}")
                    
                    if detail.get("status") == "error":
                        message = detail.get("message", "查询失败")
                        logging.warning(f"[主副卡使用量可视化] 详情项{i+1}发现错误状态: {message}")
                        all_results.append(self.generator.create_text(title, message, custom_description or ""))
                        continue
                    
                    billing_nb = detail.get("billing_nb", "")
                    billing_cycle = detail.get("billing_cycle", "")
                    usage_data = detail.get("usage_data", [])
                    
                    logging.info(f"[主副卡使用量可视化] 详情项{i+1} - 号码: {billing_nb}, 账期: {billing_cycle}, 使用量数据条数: {len(usage_data) if isinstance(usage_data, list) else 'N/A'}")
                    
                    # 只有当usage_data有值才生成可视化数据
                    if usage_data and isinstance(usage_data, list):
                        logging.info(f"[主副卡使用量可视化] 详情项{i+1}进入数据聚合处理，usage_data长度: {len(usage_data)}")
                        current_title = f"{title} - 号码{billing_nb} ({billing_cycle})"
                        
                        # 收集数据：按账号(accNbr)分组，按量本类型名称(accuTypeName)堆叠
                        account_usage_data = {}  # {accNbr: {accuTypeName: usageAmount}}
                        all_accu_types = set()  # 所有量本类型名称集合
                        
                        logging.debug(f"[主副卡使用量可视化] 开始遍历{len(usage_data)}条使用量记录")
                        
                        for j, usage_item in enumerate(usage_data):
                            if isinstance(usage_item, dict):
                                acc_nbr = usage_item.get("accNbr", "")
                                accu_type_name = usage_item.get("accuTypeName", "")
                                usage_amount = usage_item.get("usageAmount", 0)
                                
                                logging.debug(f"[主副卡使用量可视化] 使用量记录{j+1}: accNbr={acc_nbr}, accuTypeName='{accu_type_name}', usageAmount={usage_amount}")
                                
                                # 如果量本类型名称为空，使用默认名称
                                if not accu_type_name or accu_type_name.strip() == "":
                                    accu_type_id = usage_item.get("accuTypeId", "")
                                    accu_type_name = f"量本类型{accu_type_id}" if accu_type_id else "未知类型"
                                    logging.debug(f"[主副卡使用量可视化] 量本类型名称为空，使用默认名称: {accu_type_name}")
                                
                                # 初始化账号数据结构
                                if acc_nbr not in account_usage_data:
                                    account_usage_data[acc_nbr] = {}
                                
                                # 累计相同账号和量本类型的使用量
                                try:
                                    usage_amount_float = float(usage_amount) if usage_amount else 0
                                    if accu_type_name in account_usage_data[acc_nbr]:
                                        account_usage_data[acc_nbr][accu_type_name] += usage_amount_float
                                    else:
                                        account_usage_data[acc_nbr][accu_type_name] = usage_amount_float
                                except (ValueError, TypeError):
                                    if accu_type_name not in account_usage_data[acc_nbr]:
                                        account_usage_data[acc_nbr][accu_type_name] = 0
                                
                                # 记录所有量本类型
                                all_accu_types.add(accu_type_name)
                        
                        logging.info(f"[主副卡使用量可视化] 数据聚合完成 - 账号数: {len(account_usage_data)}, 量本类型数: {len(all_accu_types)}")
                        logging.debug(f"[主副卡使用量可视化] 聚合结果 - 账号列表: {list(account_usage_data.keys())}, 量本类型: {sorted(all_accu_types)}")
                        
                        if account_usage_data and all_accu_types:
                            logging.info(f"[主副卡使用量可视化] 条件检查通过，开始生成堆叠柱状图")
                            # 准备图表数据
                            labels = list(account_usage_data.keys())  # x轴：账号列表
                            accu_types_list = sorted(list(all_accu_types))  # 量本类型列表（排序以保持一致性）
                            
                            # 为每个量本类型创建数据集
                            datasets = []
                            all_data_values = []  # 收集所有数据值用于计算Y轴范围
                            
                            for i, accu_type in enumerate(accu_types_list):
                                data_values = []
                                for acc_nbr in labels:
                                    # 获取该账号该量本类型的使用量，转换为GB单位
                                    usage_kb = account_usage_data[acc_nbr].get(accu_type, 0)
                                    # 将KB转换为GB：KB / 1024 / 1024，保留4位小数
                                    usage_gb = round(usage_kb / 1024 / 1024, 4)
                                    data_values.append(usage_gb)
                                    all_data_values.append(usage_gb)
                                
                                # 为每个量本类型分配颜色
                                color = self.generator.COLORS[i % len(self.generator.COLORS)]
                                
                                datasets.append({
                                    "label": accu_type,
                                    "data": data_values,
                                    "backgroundColor": color
                                })
                            
                            # 计算堆叠后的最大值（每个账号的各类型使用量总和）
                            stacked_totals = []
                            for acc_nbr in labels:
                                total_for_account = sum(
                                    round(account_usage_data[acc_nbr].get(accu_type, 0) / 1024 / 1024, 4)
                                    for accu_type in accu_types_list
                                )
                                stacked_totals.append(total_for_account)
                            
                            # 根据堆叠后的最大值计算Y轴刻度
                            max_stacked_value = max(stacked_totals) if stacked_totals else 0
                            y_axis_ticks = self._calculate_y_axis_ticks(max_stacked_value)
                            
                            # 计算总使用量用于描述
                            total_usage = sum(
                                sum(type_usage.values()) for type_usage in account_usage_data.values()
                            )
                            total_usage_str = format_data_size(total_usage)
                            
                            description = f"共{len(labels)}个账号，{len(accu_types_list)}种量本类型，总使用量{total_usage_str}"
                            
                            # 创建堆叠柱状图
                            logging.info(f"[主副卡使用量可视化] 成功生成堆叠柱状图 - 标题: {current_title}, 账号数: {len(labels)}, 数据集数: {len(datasets)}")
                            all_results.append(self.generator.create_stacked_bar_chart(
                                title=current_title,
                                labels=labels,
                                datasets=datasets,
                                x_axis_title="账号",
                                y_axis_title="使用量(GB)",
                                description=description,
                                y_axis_ticks=y_axis_ticks
                            ))
                        else:
                            # 没有有效的使用量数据
                            logging.warning(f"[主副卡使用量可视化] 详情项{i+1}条件检查失败 - account_usage_data为空: {not account_usage_data}, all_accu_types为空: {not all_accu_types}")
                            no_data_title = f"号码{billing_nb} ({billing_cycle})"
                            all_results.append(self.generator.create_text(no_data_title, "未查询到有效的使用量数据", ""))
                    else:
                        # 没有usage_data数据
                        logging.warning(f"[主副卡使用量可视化] 详情项{i+1}没有usage_data - 存在: {bool(usage_data)}, 类型: {type(usage_data)}")
                        no_data_title = f"号码{billing_nb} ({billing_cycle})"
                        all_results.append(self.generator.create_text(no_data_title, "未查询到使用量数据", ""))
            
            result_count = len(all_results)
            logging.info(f"[主副卡使用量可视化] 所有详情项处理完成，生成了{result_count}个可视化结果")
            
            if all_results:
                for k, result in enumerate(all_results):
                    chart_type = result.get("chart_type", "unknown")
                    result_title = result.get("title", "untitled")
                    logging.debug(f"[主副卡使用量可视化] 结果{k+1}: 类型={chart_type}, 标题={result_title}")
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到任何使用量数据", custom_description or "")]
        
        logging.warning(f"[主副卡使用量可视化] input_details检查失败，回退到文本处理")
        return self._process_text_data(data, title, custom_description)
    
    def _process_fee_bill_data(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                              custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理账单费用数据"""
        title = custom_title or "账单费用明细"
        
        # 检查是否有feeInfoList字段
        fee_info_list = data.get("feeInfoList", [])
        acct_name = data.get("ACCT_NAME", "")
        total_count = data.get("totalCount", "0")
        
        if fee_info_list and isinstance(fee_info_list, list):
            headers = ["计费号码", "产品实例ID", "账期项类型名称", "费用金额", "销售品ID", "销售品名称", "账期"]
            rows = []
            
            total_fee = 0  # 统计总费用
            total_calls = 0  # 统计总通话次数
            
            for fee_item in fee_info_list:
                if isinstance(fee_item, dict):
                    # 提取各个字段 
                    prod_inst_id = fee_item.get("PROD_INST_ID", "")
                    
                    # 账期项类型名称 
                    acct_item_type_name = (fee_item.get("ACCT_ITEM_TYPE_NAME 费用项") or 
                                         fee_item.get("ACCT_ITEM_TYPE_NAME") or "")
                    
                    # 费用金额 
                    charge = fee_item.get("AMOUNT", 0)
                    
                    # 通话次数 - 不在表格中显示但保留统计
                    calls = fee_item.get("CALLS", 0)
                    
                    # 套外流量 - 不在表格中显示
                    flux = fee_item.get("FLUX 套外流量 ", "") or fee_item.get("FLUX", "")
                    
                    # 销售品ID和名称
                    offer_id = (fee_item.get("OFFER_ID 套餐ID") or 
                               fee_item.get("OFFER_ID") or "")
                    offer_name = (fee_item.get("OFFER_NAME 套餐名称") or 
                                 fee_item.get("OFFER_NAME") or "")
                    
                    # 计费号码
                    acc_num = fee_item.get("ACC_NUM 计费号码", "") or fee_item.get("ACC_NUM", "")
                    
                    # 账期
                    billing_cycle = fee_item.get("BILLING_CYCLE_ID_2 账期", "") or fee_item.get("BILLING_CYCLE_ID_2", "")
                    
                    # 累计统计
                    try:
                        total_fee += float(charge) if charge else 0
                        total_calls += int(calls) if calls else 0
                    except (ValueError, TypeError):
                        pass
                    
                    # 格式化费用显示
                    try:
                        charge_str = f"{float(charge):.2f}" if charge else "0.00"
                    except (ValueError, TypeError):
                        charge_str = str(charge) if charge else "0.00"
                    
                    # 格式化通话次数 - 保留代码但不添加到表格
                    try:
                        calls_str = str(int(calls)) if calls else "0"
                    except (ValueError, TypeError):
                        calls_str = str(calls) if calls else "0"
                    
                    # 格式化套外流量显示 - 保留代码但不添加到表格
                    flux_str = str(flux) if flux else ""
                    
                    # 屏蔽通话次数和套外流量列，只添加其他字段到表格行
                    rows.append([
                        str(acc_num),
                        str(prod_inst_id),
                        str(acct_item_type_name),
                        charge_str,
                        str(offer_id),
                        str(offer_name),
                        str(billing_cycle)
                    ])
            
            if rows:
                # 构建描述信息
                description_parts = [f"共{len(rows)}条费用记录"]
                if acct_name:
                    description_parts.append(f"账户: {acct_name}")
                description_parts.append(f"总费用: {total_fee:.2f}元")
                
                description = ", ".join(description_parts)
                return [self.generator.create_table(title, headers, rows, description)]
            else:
                return [self.generator.create_text(title, "费用信息列表为空", custom_description or "")]
        else:
            # 如果没有费用数据，显示消息
            message = data.get("message", "未查询到账单费用数据")
            return [self.generator.create_text(title, message, custom_description or "")]
    
    def _process_benefits_package_fee_relation(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                             custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理权益包订购与费用关系数据"""
        title = custom_title or "权益包订购与费用关系"
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        if not input_details or not isinstance(input_details, list):
            return [self.generator.create_text(title, "未查询到权益包费用关系数据", custom_description or "")]
        
        all_results = []
        
        for detail in input_details:
            if isinstance(detail, dict) and detail.get("status") == "success":
                billing_nb = detail.get("billing_nb", "")
                billing_cycle = detail.get("billing_cycle", "")
                acct_name = detail.get("ACCT_NAME", "")
                integrated_data = detail.get("integratedData", [])
                
                if not integrated_data:
                    # 如果没有整合数据，显示提示信息
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    all_results.append(self.generator.create_text(detail_title, "未查询到权益包费用关系数据", ""))
                    continue
                
                # 构建表格数据
                headers = [
                    "号码",
                    "销售品ID", 
                    "销售品实例ID",
                    "销售品名称",
                    "定价名称",
                    "生效时间",
                    "失效时间"
                ]
                rows = []
                
                for integrated_item in integrated_data:
                    if isinstance(integrated_item, dict):
                        fee_info = integrated_item.get("feeInfo", {})
                        pricing_plan = integrated_item.get("pricingPlan", {})
                        
                        # 从feeInfo中提取数据
                        acc_num = fee_info.get("ACC_NUM", "")
                        offer_id = fee_info.get("OFFER_ID", "")
                        offer_inst_id = fee_info.get("OFFER_INST_ID", "")
                        offer_name = fee_info.get("OFFER_NAME", "")
                        
                        # 从pricingPlan中提取数据
                        if pricing_plan:
                            pricing_plan_name = pricing_plan.get("pricingPlanName", "")
                            eff_date = pricing_plan.get("effDate", "")
                            exp_date = pricing_plan.get("expDate", "")
                        else:
                            # 如果没有匹配的定价信息
                            pricing_plan_name = "无匹配定价信息"
                            eff_date = ""
                            exp_date = ""
                        
                        rows.append([
                            str(acc_num),
                            str(offer_id),
                            str(offer_inst_id),
                            str(offer_name),
                            str(pricing_plan_name),
                            str(eff_date),
                            str(exp_date)
                        ])
                
                if rows:
                    # 构建表格标题和描述
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    if acct_name:
                        detail_title += f" - {acct_name}"
                    
                    description = f"共{len(rows)}条权益包费用关系记录"
                    
                    all_results.append(self.generator.create_table(detail_title, headers, rows, description))
                else:
                    # 如果没有有效的行数据
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    all_results.append(self.generator.create_text(detail_title, "权益包费用关系数据为空", ""))
            else:
                # 处理错误情况
                billing_nb = detail.get("billing_nb", "未知号码")
                billing_cycle = detail.get("billing_cycle", "未知账期")
                error_message = detail.get("message", "查询失败")
                
                detail_title = f"号码{billing_nb} ({billing_cycle})"
                all_results.append(self.generator.create_text(detail_title, error_message, ""))
        
        return all_results if all_results else [self.generator.create_text(title, "未查询到任何权益包费用关系数据", custom_description or "")]
    
    def _process_benefits_package_bill_fee(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                         custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理权益包账单费用数据 - 使用堆叠柱状图展示"""
        title = custom_title or "权益包账单费用"
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        if not input_details or not isinstance(input_details, list):
            return [self.generator.create_text(title, "未查询到权益包账单费用数据", custom_description or "")]
        
        all_results = []
        
        for detail in input_details:
            if isinstance(detail, dict) and detail.get("status") == "success":
                billing_nb = detail.get("billing_nb", "")
                billing_cycle = detail.get("billing_cycle", "")
                acct_name = detail.get("ACCT_NAME", "")
                fee_info_list = detail.get("feeInfoList", [])
                
                if not fee_info_list:
                    # 如果没有费用数据，显示提示信息
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    if acct_name:
                        detail_title += f" - {acct_name}"
                    all_results.append(self.generator.create_text(detail_title, "未查询到权益包账单费用数据", ""))
                    continue
                
                # 构建堆叠柱状图数据
                # x轴：ACC_NUM（号码），堆叠类型：ACCT_ITEM_TYPE_NAME，y轴：AMOUNT（费用）
                
                # 收集数据：按号码分组，按账期项类型名称堆叠
                acc_num_data = {}  # {acc_num: {acct_item_type_name: amount}}
                all_acct_item_types = set()  # 所有账期项类型名称
                
                for fee_item in fee_info_list:
                    if isinstance(fee_item, dict):
                        acc_num = fee_item.get("ACC_NUM", "")
                        acct_item_type_name = fee_item.get("ACCT_ITEM_TYPE_NAME", "")
                        amount = fee_item.get("AMOUNT", "")
                        
                        # 如果账期项类型名称为空，使用默认名称
                        if not acct_item_type_name or acct_item_type_name.strip() == "":
                            acct_item_type_name = "未知费用类型"
                        
                        # 初始化号码数据结构
                        if acc_num not in acc_num_data:
                            acc_num_data[acc_num] = {}
                        
                        # 累计相同号码和账期项类型的费用
                        try:
                            amount_float = float(amount) if amount else 0.0
                            if acct_item_type_name in acc_num_data[acc_num]:
                                acc_num_data[acc_num][acct_item_type_name] += amount_float
                            else:
                                acc_num_data[acc_num][acct_item_type_name] = amount_float
                        except (ValueError, TypeError):
                            if acct_item_type_name not in acc_num_data[acc_num]:
                                acc_num_data[acc_num][acct_item_type_name] = 0.0
                        
                        # 记录所有账期项类型
                        all_acct_item_types.add(acct_item_type_name)
                
                if acc_num_data and all_acct_item_types:
                    # 准备图表数据
                    labels = list(acc_num_data.keys())  # x轴：号码列表
                    acct_item_types_list = sorted(list(all_acct_item_types))  # 账期项类型列表（排序以保持一致性）
                    
                    # 为每个账期项类型创建数据集
                    datasets = []
                    all_amount_values = []  # 收集所有费用值用于计算Y轴范围
                    
                    for i, acct_item_type in enumerate(acct_item_types_list):
                        amount_values = []
                        for acc_num in labels:
                            # 获取该号码该账期项类型的费用金额
                            amount = acc_num_data[acc_num].get(acct_item_type, 0.0)
                            amount_values.append(amount)
                            all_amount_values.append(amount)
                        
                        # 为每个账期项类型分配颜色
                        color = self.generator.COLORS[i % len(self.generator.COLORS)]
                        
                        datasets.append({
                            "label": acct_item_type,
                            "data": amount_values,
                            "backgroundColor": color
                        })
                    
                    # 计算堆叠后的最大值（每个号码的各类型费用总和）
                    stacked_totals = []
                    for acc_num in labels:
                        total_for_acc_num = sum(
                            acc_num_data[acc_num].get(acct_item_type, 0.0)
                            for acct_item_type in acct_item_types_list
                        )
                        stacked_totals.append(total_for_acc_num)
                    
                    # 根据堆叠后的最大值计算Y轴刻度
                    max_stacked_value = max(stacked_totals) if stacked_totals else 0
                    y_axis_ticks = self._calculate_y_axis_ticks(max_stacked_value)
                    
                    # 计算总费用用于描述
                    total_amount = sum(
                        sum(type_amount.values()) for type_amount in acc_num_data.values()
                    )
                    
                    # 构建图表标题和描述
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    if acct_name:
                        detail_title += f" - {acct_name}"
                    
                    description = f"共{len(labels)}个号码，{len(acct_item_types_list)}种费用类型，总费用：{total_amount:.2f}元"
                    
                    # 创建堆叠柱状图
                    all_results.append(self.generator.create_stacked_bar_chart(
                        title=detail_title,
                        labels=labels,
                        datasets=datasets,
                        x_axis_title="号码",
                        y_axis_title="费用(元)",
                        description=description,
                        y_axis_ticks=y_axis_ticks
                    ))
                else:
                    # 如果没有有效的数据
                    detail_title = f"号码{billing_nb} ({billing_cycle})"
                    if acct_name:
                        detail_title += f" - {acct_name}"
                    all_results.append(self.generator.create_text(detail_title, "权益包账单费用数据为空", ""))
            else:
                # 处理错误情况
                billing_nb = detail.get("billing_nb", "未知号码")
                billing_cycle = detail.get("billing_cycle", "未知账期")
                error_message = detail.get("message", "查询失败")
                
                detail_title = f"号码{billing_nb} ({billing_cycle})"
                all_results.append(self.generator.create_text(detail_title, error_message, ""))
        
        return all_results if all_results else [self.generator.create_text(title, "未查询到任何权益包账单费用数据", custom_description or "")]
    
    def _process_internet_detail_service(self, data: Dict[str, Any], custom_title: Optional[str] = None,
                                        custom_description: Optional[str] = None) -> List[Dict[str, Any]]:
        """处理上网详单服务数据"""
        title = custom_title or "上网详单按日分析"
        
        # 检查是否有input_details字段
        input_details = data.get("input_details", [])
        if input_details and isinstance(input_details, list):
            all_results = []
            
            for detail in input_details:
                if isinstance(detail, dict):
                    # 检查是否有错误状态
                    if detail.get("status") == "error":
                        message = detail.get("message", "查询失败")
                        all_results.append(self.generator.create_text(title, message, custom_description or ""))
                        continue
                    
                    billing_nb = detail.get("billing_nb", "")
                    billing_cycle = detail.get("billing_cycle", "")
                    daysum_data = detail.get("Daysum", [])
                    
                    # 如果有Daysum数据，创建堆叠柱状图
                    if daysum_data and isinstance(daysum_data, list):
                        current_title = f"{title} - 号码{billing_nb} ({billing_cycle})"
                        
                        # 准备数据：x轴为Daytime（日期），堆叠类型为fee（费用）和volume（使用量）
                        labels = []  # x轴：日期列表
                        fee_data = []  # 费用数据
                        volume_data = []  # 使用量数据
                        
                        total_fee = 0
                        total_volume = 0
                        
                        for day_item in daysum_data:
                            if isinstance(day_item, dict):
                                daytime = day_item.get("Daytime", "")
                                fee = day_item.get("fee", 0)
                                volume = day_item.get("volume", 0)
                                
                                # 格式化日期显示（从YYYYMMDD转换为MM-DD格式）
                                if len(daytime) == 8:
                                    formatted_date = f"{daytime[4:6]}-{daytime[6:8]}"
                                else:
                                    formatted_date = daytime
                                
                                labels.append(formatted_date)
                                
                                # 处理费用数据（单位转换：分转换为元）
                                try:
                                    fee_yuan = float(fee) / 100.0  # 分转换为元
                                    fee_data.append(fee_yuan)
                                    total_fee += fee_yuan
                                except (ValueError, TypeError):
                                    fee_data.append(0)
                                
                                # 处理使用量数据（保持KB单位，但转换为整数以便计算）
                                try:
                                    volume_kb = int(float(volume))
                                    volume_data.append(volume_kb)
                                    total_volume += volume_kb
                                except (ValueError, TypeError):
                                    volume_data.append(0)
                        
                        if labels and (any(f > 0 for f in fee_data) or any(v > 0 for v in volume_data)):
                            # 将使用量从KB转换为GB显示
                            volume_gb_data = []
                            for v in volume_data:
                                # KB转换为GB: KB / 1024 / 1024
                                gb_value = round(v / 1024 / 1024, 4)
                                volume_gb_data.append(gb_value)
                            
                            # 创建数据集
                            datasets = [
                                {
                                    "label": "费用(元)",
                                    "data": fee_data,
                                    "backgroundColor": "#e74c3c"  # 红色表示费用
                                },
                                {
                                    "label": "使用量(GB)",
                                    "data": volume_gb_data,
                                    "backgroundColor": "#3498db"  # 蓝色表示使用量
                                }
                            ]
                            
                            # 计算Y轴刻度范围（分别计算费用和使用量的最大值，避免单位混合）
                            max_fee = max(fee_data) if fee_data else 0
                            max_volume = max(volume_gb_data) if volume_gb_data else 0
                            # 使用较大的那个作为Y轴基准，确保两种数据都能完整显示
                            max_value = max(max_fee, max_volume)
                            y_axis_ticks = self._calculate_y_axis_ticks(max_value)
                            
                            # 格式化总量显示
                            total_volume_str = format_data_size(total_volume)
                            description = f"共{len(labels)}天数据，总费用{total_fee:.2f}元，总使用量{total_volume_str}"
                            
                            # 创建堆叠柱状图
                            all_results.append(self.generator.create_stacked_bar_chart(
                                title=current_title,
                                labels=labels,
                                datasets=datasets,
                                x_axis_title="日期",
                                y_axis_title="费用(元) / 使用量(GB)",
                                description=description,
                                y_axis_ticks=y_axis_ticks
                            ))
                        else:
                            # 没有有效的数据
                            no_data_title = f"号码{billing_nb} ({billing_cycle})"
                            all_results.append(self.generator.create_text(no_data_title, "未查询到有效的上网详单数据", ""))
                    else:
                        # 没有Daysum数据
                        no_data_title = f"号码{billing_nb} ({billing_cycle})"
                        all_results.append(self.generator.create_text(no_data_title, "未查询到上网详单数据", ""))
            
            return all_results if all_results else [self.generator.create_text(title, "未查询到任何上网详单数据", custom_description or "")]
        
        return self._process_text_data(data, title, custom_description)
    
    def _get_field_display_name(self, field_name: str) -> str:
        """获取字段显示名称"""
        mapping = {
            "billing_nb": "计费号码",
            "PROD_INST_ID": "产品实例ID",
            "ACCT_ID": "账户ID",
            "CUST_ID": "客户ID",
            "latn_id": "本地网ID",
            "latn_name": "本地网名称",
            "billing_cycle": "账期",
            "message": "结果",
            "result": "结果"
        }
        return mapping.get(field_name, field_name)
    
def format_data_size(kb_value: Union[int, float, str]) -> str:
    """
    智能格式化数据大小单位
    
    Args:
        kb_value: KB为单位的数值
        
    Returns:
        格式化后的字符串，自动选择MB或GB单位
    """
    try:
        kb_float = float(kb_value) if kb_value else 0
        
        # 转换为MB
        mb_value = kb_float / 1024
        
        # 如果小于1GB (1024MB)，使用MB显示
        if mb_value < 1024:
            return f"{mb_value:.2f} MB"
        else:
            # 转换为GB
            gb_value = mb_value / 1024
            return f"{gb_value:.2f} GB"
    except (ValueError, TypeError):
        return str(kb_value) if kb_value else "0 KB"

# ===== API调用可视化JSON数据 =====
def add_visualization_data(service_type: str, response_data: Dict[str, Any], 
                         custom_title: Optional[str] = None, 
                         custom_description: Optional[str] = None) -> Dict[str, Any]:
    """
    为API响应添加可视化JSON串数据
    
    Args:
        service_type: 服务类型（使用ServiceNames常量或直接使用字符串）
        response_data: 原始API响应数据
        custom_title: 自定义图表标题
        custom_description: 自定义图表描述
        
    Returns:
        包含visualization字段的响应数据，visualization为图表对象数组
    """
    try:
        processor = ServiceVisualizationProcessor()
        visualization_result = processor.process_by_service_type(service_type, response_data, custom_title, custom_description)
        
        # 返回包含可视化数据的响应
        result = response_data.copy()
        
        # 返回数组格式，支持单图表和多图表
        result["visualization"] = visualization_result
        
        return result
        
    except Exception as e:
        logging.error(f"生成可视化数据失败: {str(e)}")
        # 失败时返回原始数据，不影响功能
        return response_data 