#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI入口文件 - 用于生产环境部署
支持Gunicorn、uWSGI、Waitress等WSGI服务器
"""

import sys
import os
import signal
from DBPyMgr import DBPyMgr
import logging2
from DCConfigParser import DCConfigParser
import traceback

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 全局变量
db_manager_instance = None
configObj_instance = None
application = None


def cleanup_werkzeug_env():
    """清理所有 Werkzeug 相关的环境变量"""
    werkzeug_vars = [
        'WERKZEUG_SERVER_FD',
        'WERKZEUG_RUN_MAIN'
    ]
    for var in werkzeug_vars:
        if var in os.environ:
            try:
                del os.environ[var]
            except KeyError:
                pass


def setup_production_environment():
    """设置生产环境"""
    cleanup_werkzeug_env()
    os.environ['FLASK_ENV'] = 'production'
    os.environ.pop('FLASK_DEBUG', None)


def signal_handler(sig, frame):
    """信号处理器"""
    print('\nReceived signal! Shutting down gracefully...')
    cleanup_resources()
    sys.exit(0)


def cleanup_resources():
    """清理资源"""
    global db_manager_instance
    try:
        if db_manager_instance:
            db_manager_instance.stop()
        logging2.stop()
        cleanup_werkzeug_env()
    except Exception as e:
        print(f"清理资源时出错: {e}")


def create_application(config_file=None):
    """创建Flask应用实例"""
    global db_manager_instance, configObj_instance, application
    
    try:
        # 设置生产环境
        setup_production_environment()
        
        # 如果没有提供配置文件，使用默认配置
        if config_file is None:
            config_file = os.path.join(os.path.dirname(current_dir), 'config', 'FluxDisputeAgent.jx.cfg.xml')
        
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        # 解析xml配置文件
        configObj_instance = DCConfigParser(config_file)

        # 初始化全局配置解析器实例
        from api_services.config.globals import init_config_parser
        init_config_parser(configObj_instance)

        # 初始化日志模块
        logpath = configObj_instance.get_log_path()
        logmodule = configObj_instance.get_log_module()
        loglevel = configObj_instance.get_log_level()
        logging2.start(logpath, logmodule, loglevel)

        # 数据库初始化
        sqlxmlfile = configObj_instance.get_sql_file()
        from api_services.config.globals import init_db_manager
        db_manager = DBPyMgr(sqlxmlfile)
        db_manager.init()
        init_db_manager(db_manager)
        db_manager_instance = db_manager

        # 创建Flask应用
        from api_services import create_app
        application = create_app()
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        logging2.info("Flask应用初始化完成，准备用于生产环境")
        return application
        
    except Exception as e:
        error_msg = f"应用初始化失败: {str(e)}"
        print(error_msg)
        if 'logging2' in globals():
            logging2.error(error_msg)
            logging2.error(f"{traceback.format_exc()}")
        cleanup_resources()
        raise


# 创建应用实例（WSGI服务器会调用这个变量）
try:
    app = create_application()
except Exception as e:
    print(f"WSGI应用创建失败: {e}")
    app = None

# 兼容性别名
application = app

if __name__ == "__main__":
    # 如果直接运行此文件，仍然使用开发服务器（仅用于测试）
    if app:
        config_file = sys.argv[1] if len(sys.argv) > 1 else None
        if config_file:
            app = create_application(config_file)
        
        flaskIP = configObj_instance.get_flask_ip() if configObj_instance else '127.0.0.1'
        flaskPort = int(configObj_instance.get_flask_port()) if configObj_instance else 5000
        
        print("警告: 正在使用开发服务器，生产环境请使用WSGI服务器")
        app.run(host=flaskIP, port=flaskPort, debug=False)
