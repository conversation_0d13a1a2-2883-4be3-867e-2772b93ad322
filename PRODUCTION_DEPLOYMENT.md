# FluxDisputeAgent 生产环境部署指南

## 概述

本文档描述如何将FluxDisputeAgent从开发环境迁移到生产环境，解决Flask开发服务器警告问题。

## 问题分析

### 当前问题
- 使用Flask内置开发服务器（`app.run()`）
- 出现警告：`WARNING: This is a development server. Do not use it in a production deployment.`
- 性能和安全性不适合生产环境

### 解决方案
使用专业的WSGI服务器替代Flask开发服务器。

## 支持的WSGI服务器

### 1. Gunicorn（推荐）
- **优点**：配置简单，性能优秀，社区活跃
- **适用**：Linux/Unix系统
- **特性**：支持多进程、多线程，内存占用低

### 2. uWSGI
- **优点**：功能强大，高度可配置
- **适用**：Linux/Unix系统
- **特性**：支持多种协议，内置缓存

### 3. Waitress
- **优点**：纯Python实现，跨平台
- **适用**：Windows/Linux/Unix
- **特性**：无需编译，易于部署

## 部署步骤

### 1. 安装依赖

```bash
# 安装生产环境依赖
pip install -r requirements.txt

# 或单独安装WSGI服务器
pip install gunicorn  # 推荐
pip install uwsgi     # 功能强大
pip install waitress  # 跨平台
```

### 2. 使用启动脚本

```bash
# 给脚本执行权限
chmod +x start_production.sh

# 使用Gunicorn启动（推荐）
./start_production.sh start gunicorn

# 使用uWSGI启动
./start_production.sh start uwsgi

# 使用Waitress启动
./start_production.sh start waitress

# 检查状态
./start_production.sh status

# 停止服务
./start_production.sh stop

# 重启服务
./start_production.sh restart gunicorn
```

### 3. 手动启动命令

#### Gunicorn
```bash
cd src
gunicorn --config ../gunicorn.conf.py wsgi:app
```

#### uWSGI
```bash
cd src
uwsgi --http ************:1888 --module wsgi:app --processes 4 --threads 2
```

#### Waitress
```bash
cd src
waitress-serve --host=************ --port=1888 wsgi:app
```

## 配置说明

### Gunicorn配置（gunicorn.conf.py）
- **workers**: 工作进程数，建议 CPU核心数 × 2 + 1
- **timeout**: 请求超时时间，默认30秒
- **bind**: 绑定地址和端口
- **preload_app**: 预加载应用，提高性能

### 关键配置项
```python
# 服务器绑定
bind = "************:1888"

# 工作进程数
workers = multiprocessing.cpu_count() * 2 + 1

# 超时设置
timeout = 30
graceful_timeout = 30

# 日志配置
loglevel = "info"
accesslog = "/data/FluxDisputeAgent/log/gunicorn_access.log"
errorlog = "/data/FluxDisputeAgent/log/gunicorn_error.log"
```

## 性能优化建议

### 1. 进程和线程配置
- **CPU密集型**：workers = CPU核心数 × 2 + 1
- **IO密集型**：可以增加worker数量
- **内存限制**：减少worker数量，增加threads

### 2. 超时设置
- **timeout**: 根据业务需求调整，数据库查询较多建议30-60秒
- **keepalive**: 保持连接，减少连接开销

### 3. 日志管理
- 定期轮转日志文件
- 监控错误日志
- 设置合适的日志级别

## 监控和维护

### 1. 健康检查
```bash
# 检查进程状态
./start_production.sh status

# 检查端口监听
netstat -tuln | grep :1888

# 简单HTTP检查
curl -I http://************:1888/
```

### 2. 日志监控
```bash
# 查看访问日志
tail -f /data/FluxDisputeAgent/log/gunicorn_access.log

# 查看错误日志
tail -f /data/FluxDisputeAgent/log/gunicorn_error.log

# 查看应用日志
tail -f /data/FluxDisputeAgent/log/FluxDisputeAgent.log
```

### 3. 性能监控
- 监控CPU和内存使用率
- 监控响应时间
- 监控并发连接数
- 监控数据库连接池状态

## 故障排除

### 1. 常见问题

#### 端口被占用
```bash
# 查找占用端口的进程
lsof -i :1888
netstat -tuln | grep :1888

# 停止占用进程
./start_production.sh stop
```

#### 权限问题
```bash
# 确保日志目录权限
mkdir -p /data/FluxDisputeAgent/log
chmod 755 /data/FluxDisputeAgent/log
```

#### 数据库连接问题
- 检查数据库配置文件
- 验证数据库连接参数
- 检查网络连通性

### 2. 调试模式
```bash
# 临时启用调试模式（仅用于排错）
cd src
python3 wsgi.py ../config/FluxDisputeAgent.jx.cfg.xml
```

## 安全建议

### 1. 网络安全
- 使用防火墙限制访问
- 配置反向代理（Nginx/Apache）
- 启用HTTPS（如需要）

### 2. 应用安全
- 定期更新依赖包
- 监控安全漏洞
- 限制文件权限

### 3. 数据安全
- 加密敏感配置
- 定期备份数据
- 监控异常访问

## 迁移检查清单

- [ ] 安装WSGI服务器依赖
- [ ] 创建wsgi.py入口文件
- [ ] 配置WSGI服务器参数
- [ ] 测试应用启动
- [ ] 验证API功能正常
- [ ] 配置日志轮转
- [ ] 设置监控告警
- [ ] 更新部署文档
- [ ] 培训运维人员

## 回滚方案

如果生产环境部署出现问题，可以快速回滚到原有方式：

```bash
# 停止WSGI服务器
./start_production.sh stop

# 使用原有启动方式
cd src
nohup python3 ./main.py ../config/FluxDisputeAgent.jx.cfg.xml > /dev/null &
```

## 联系信息

如有问题，请联系开发团队或查看相关文档。
