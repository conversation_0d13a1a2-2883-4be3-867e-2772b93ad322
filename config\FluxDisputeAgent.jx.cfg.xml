<FluxDisputeAgent>
    <log>
        <param name="logpath">/data/FluxDisputeAgent/log</param>
        <param name="logmodule">FluxDisputeAgent</param><!--日志模块-->
        <param name="loglevel">DEBUG</param><!--DEBUG, INFO, WARNING, ERROR-->
    </log>    

    <sql>
        <param name='sql_file'>../cfg/FluxDisputeAgent.jx.sql.xml</param>
        <param name='db_name'>JXtest</param><!--SQL在sql_file配置的db_name-->
    </sql>

    <flask>
        <param name="host">************</param>
        <param name="port">1888</param>
        <param name="debug">false</param>
    </flask>

    <http_config>
        <param name="config_file">../cfg/config.json</param><!--HTTP接口配置文件路径-->
    </http_config>

    <charge_filter>
        <param name="CHARGE_TYPE_ID">5,7</param><!--接口提取用的收费类型fee_type-->
    </charge_filter>

    <!--仅注释查看，程序不适用配置项api_list，例如全服务名称为http://**************:1888/query/acct_query_service-->
    <api_list>
        <!-- 数据分析服务 (analysis_services.py,1个接口) -->
        <param name="用户热点事件查询">/query/user_trending_events</param>

        <!-- 计费相关服务 (billing_services.py,4个接口) -->
        <param name="判断账期1+1接口">/query/is_billing_cycle_service</param>
        <param name="查询流量溢出费用账单服务">/query/flux_overflow_fee_service</param>
        <param name="查询收费清单服务">/query/charge_detail_service</param>
        <param name="查询账单费用接口">/query/qryJzrzDayFeeBill</param>

        <!-- 争议诊断服务 (dispute_services.py,2个接口) -->
        <param name="非不限量流量争议诊断服务">/query/non_unlimited_flux_dispute_service</param>
        <param name="上网详单查询接口">/query/internet_detail_service</param>

        <!-- 套餐管理服务 (package_services.py,7个接口) -->
        <param name="查询用户订购套外资费销售品信息">/query/unlimited_package_service</param>
        <param name="查询是否有剩余量本服务">/query/remain_accu_service</param>
        <param name="用户资源详细量本查询">/query/userResourceQueryDetailBon</param>
        <param name="查实时费用表、账单表判断资费销售品接口">/query/billing_offer_ids_service</param>
        <param name="查询号码加入共享流量包的时间接口">/query/shared_flow_package_time</param>
        <param name="定向-定价协议查询">/query/userDirectionalOfferPricing</param>
        <param name="套餐使用明细和账单信息查询服务">/query/package_usage_billing_service</param>

        <!-- 关系管理服务 (relation_services.py,3个接口) -->
        <param name="查询主副卡所有号码的套餐使用量查询接口">/query/primarySecondaryCardUsage</param>
        <param name="查询主副卡使用量接口">/query/main_secondary_card_usage</param>
        <param name="主副卡套外使用量接口">/query/primary_secondary_card_overflow_usage</param>

        <!-- 用户信息服务 (user_services.py,4个接口) -->
        <param name="号段表本地网查询接口">/query/latn_id_service</param>
        <param name="查询用户资料服务">/query/user_profile_service</param>
        <param name="用户信息查询">/query/user_info</param>
        <param name="查询主副卡关系">/query/userrel_100800_service</param>

        <!-- 定价协议服务 (pricing_services.py,2个接口) -->
        <param name="乡镇、校园-定价协议查询">/query/userTownFlowSchoolCell</param>
        <param name="话单基站是否属于校园基站或乡镇基站">/query/userTownFlowSchoolCellRtBillItem</param>

        <!-- 权益包服务 (benefits_services.py,2个接口) -->
        <param name="权益包订购与费用关系接口">/query/benefits_package_fee_relation</param>
        <param name="权益包账单费用接口">/query/benefits_package_bill_fee</param>
    </api_list>
</FluxDisputeAgent>
