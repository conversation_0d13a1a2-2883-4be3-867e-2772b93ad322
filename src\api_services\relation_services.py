import json
import logging2
import traceback
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from api_services.common.utils import get_latn_id_if_missing
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data

# 创建关系管理服务蓝图
relation_bp = Blueprint('relation_services', __name__)

# 查询主副卡所有号码的套餐使用量查询接口
@relation_bp.route('/query/primarySecondaryCardUsage', methods=['POST'])
def primary_secondary_card_usage():
    """
    查询主副卡所有号码的套餐使用量查询接口
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询主副卡套餐使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数 - 根据要求只校验PROD_INST_ID和billing_nb
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 可选参数

            if not prod_inst_id or not billing_nb or not billing_cycle:
                logging2.warning("缺少必要参数 PROD_INST_ID 或 billing_nb 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 PROD_INST_ID 或 billing_nb 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 如果没有提供latn_id，尝试通过billing_nb查询
            if not latn_id:
                latn_id, error_msg = get_latn_id_if_missing(billing_nb, None)
                if not latn_id:
                    logging2.error(f"无法获取latn_id: {error_msg}")
                    response_list.append({
                        "status": "error",
                        "message": f"无法获取本地网ID: {error_msg}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue
                logging2.info(f"通过billing_nb查询到latn_id: {latn_id}")

            logging2.info(f"开始查询主副卡套餐使用量: PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billingCycle={billing_cycle}, latn_id={latn_id}")

            # 通过QueryOfferProdInstRelHisA1Info查询获得ext_offer_inst_id
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            
            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
                logging2.debug(f"查询销售品关系历史信息成功，结果数: {len(a1_rows)}")
            except Exception as e:
                logging2.error(f"查询销售品关系历史信息失败: {e}")
                response_list.append({
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if not a1_rows:
                logging2.warning(f"未找到套餐实例信息: PROD_INST_ID={prod_inst_id}")
                response_list.append({
                    "status": "error",
                    "message": "未找到套餐实例信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 提取所有ext_offer_inst_id
            ext_offer_inst_ids = []
            for db_row in a1_rows:
                # QueryOfferProdInstRelHisA1Info返回: offer_inst_id, prod_offer_id, EFF_DATE, EXP_DATE, update_date, OBJ_ID, OBJ_TYPE, ext_offer_inst_id
                if len(db_row) >= 8:
                    ext_offer_inst_id = db_row[7]  # ext_offer_inst_id是第8个字段
                    if ext_offer_inst_id:
                        ext_offer_inst_ids.append(ext_offer_inst_id)

            if not ext_offer_inst_ids:
                logging2.warning("未找到有效的ext_offer_inst_id")
                response_list.append({
                    "status": "error",
                    "message": "未找到有效的套餐实例ID",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"找到ext_offer_inst_ids: {ext_offer_inst_ids}")

            # 调用openapi接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("userResourceQueryDetailBon")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到userResourceQueryDetailBon接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            all_usage_details = []

            for ext_offer_inst_id in ext_offer_inst_ids:
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 11111111111
                    },
                    "svcObjectStruct": {
                        "objType": "3",
                        "objValue": billing_nb,
                        "objAttr": "2",
                        "dataArea": "2"
                    },
                    "queryFlag": "3",
                    "billingCycle": int(billing_cycle),
                    "offerInstId": str(ext_offer_inst_id)
                }

                logging2.info(f"primarySecondaryCardUsage data:{request_payload}")

                # 发送请求并处理结果
                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"primarySecondaryCardUsage result:{result}")

                    if result:
                        all_usage_details.append(result)
                except Exception as req_ex:
                    logging2.error(f"请求套餐实例ID {ext_offer_inst_id} 时出错: {str(req_ex)}")
                    # 继续处理其他套餐实例，不中断整个流程

            # 添加单个用户的查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billingCycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "usageDetails": all_usage_details
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "主副卡套餐使用量查询完成",
            "input_details": response_list
        }
        
        # 检查是否有usageDetails数据，如果有则添加可视化
        has_usage_data = False
        for item in response_list:
            if isinstance(item, dict) and item.get("usageDetails"):
                has_usage_data = True
                break
        
        if has_usage_data:
            logging2.info("检测到主副卡套餐使用量数据，添加可视化渲染")
            final_response = add_visualization_data("primarySecondaryCardUsage", final_response, "主副卡套餐使用量查询", "主副卡套餐使用量详细信息")
        
        logging2.info(f"主副卡套餐使用量查询完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error", 
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理主副卡套餐使用量查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error", 
            "message": "服务器内部错误"
        }), 500

# 查询主副卡使用量接口
@relation_bp.route('/query/main_secondary_card_usage', methods=['POST'])
def main_secondary_card_usage():
    """
    查询主副卡使用量接口
    根据主副卡关系查询AccuUseQryCenter和userResourceQueryDetailBon接口，返回指定格式数据
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询主副卡使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 可选参数

            if not billing_nb or not prod_inst_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 如果没有提供latn_id，尝试通过billing_nb查询
            if not latn_id:
                latn_id, error_msg = get_latn_id_if_missing(billing_nb, None)
                if not latn_id:
                    logging2.error(f"无法获取latn_id: {error_msg}")
                    response_list.append({
                        "status": "error",
                        "message": f"无法获取本地网ID: {error_msg}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue
                logging2.info(f"通过billing_nb查询到latn_id: {latn_id}")

            logging2.info(f"开始查询主副卡使用量: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, billing_cycle={billing_cycle}, latn_id={latn_id}")

            # 查询主副卡关系，分别收集主卡和副卡的产品实例ID和号码
            main_card_data = []  # 主卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            sub_card_data = []   # 副卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            replacements = [['##LATNID##', str(latn_id)]]
            
            try:
                # 查询QueryProdInstRel_a_100800获取a_prod_inst_id
                params = [prod_inst_id, prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )
                
                if a_rows:
                    for row in a_rows:
                        a_prod_inst_id = row[0] if row else None
                        if a_prod_inst_id:
                            # 查询QueryProdInstRel_100800获取主副卡关系
                            params2 = [a_prod_inst_id]
                            a1_rows = db_manager_instance.excute_sql(
                                sql_name='QueryProdInstRel_100800',
                                params=params2,
                                lst_replace_code_value=replacements
                            )
                            
                            if a1_rows:
                                # 收集主副卡的产品实例ID
                                for a1_row in a1_rows:
                                    if len(a1_row) >= 2:
                                        main_prod_inst_id = a1_row[0]  # a_prod_inst_id (主卡)
                                        sub_prod_inst_id = a1_row[1]   # z_prod_inst_id (副卡)
                                        
                                        # 查询主卡号码
                                        if main_prod_inst_id:
                                            params3 = [main_prod_inst_id]
                                            main_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params3,
                                                lst_replace_code_value=replacements
                                            )
                                            if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                                main_card_data.append({
                                                    "prod_inst_id": str(main_prod_inst_id),
                                                    "acc_nbr": main_acc_rows[0][0]
                                                })
                                        
                                        # 查询副卡号码
                                        if sub_prod_inst_id:
                                            params4 = [sub_prod_inst_id]
                                            sub_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params4,
                                                lst_replace_code_value=replacements
                                            )
                                            if sub_acc_rows and sub_acc_rows[0] and sub_acc_rows[0][0]:
                                                sub_card_data.append({
                                                    "prod_inst_id": str(sub_prod_inst_id),
                                                    "acc_nbr": sub_acc_rows[0][0]
                                                })
                            else:
                                # 如果没有主副卡关系，当前产品实例作为主卡处理
                                params3 = [a_prod_inst_id]
                                main_acc_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAccNumByProdInstId',
                                    params=params3,
                                    lst_replace_code_value=replacements
                                )
                                if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                    main_card_data.append({
                                        "prod_inst_id": str(a_prod_inst_id),
                                        "acc_nbr": main_acc_rows[0][0]
                                    })
                else:
                    # 如果没有查到主副卡关系，直接使用传入的prod_inst_id和billing_nb作为主卡
                    main_card_data.append({
                        "prod_inst_id": str(prod_inst_id),
                        "acc_nbr": billing_nb
                    })
                
                # 对主卡和副卡数据进行去重处理：使用字典来去重，以prod_inst_id和acc_nbr的组合作为唯一键
                main_card_unique = {}
                for card in main_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    main_card_unique[key] = card
                main_card_data = list(main_card_unique.values())
                
                sub_card_unique = {}
                for card in sub_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    sub_card_unique[key] = card
                sub_card_data = list(sub_card_unique.values())
                    
                logging2.info(f"查询到主卡数据(去重后): {main_card_data}")
                logging2.info(f"查询到副卡数据(去重后): {sub_card_data}")
                    
            except Exception as e:
                logging2.error(f"查询主副卡关系时出错: {str(e)}")
                # 如果查询出错，则只使用传入的数据作为主卡
                main_card_data.append({
                    "prod_inst_id": str(prod_inst_id),
                    "acc_nbr": billing_nb
                })

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            accu_use_url = config_loader.get_url("AccuUseQryCenter")
            user_resource_url = config_loader.get_url("userResourceQueryDetailBon")
            
            if not accu_use_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            if not user_resource_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到userResourceQueryDetailBon接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 存储所有查询结果
            all_usage_data = []
            
            # 用于去重userResourceQueryDetailBon接口调用的集合
            queried_user_resource_keys = set()
            
            # 处理主卡数据
            for card in main_card_data:
                acc_nbr = card["acc_nbr"]
                
                # 构建AccuUseQryCenter请求数据
                accu_request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询主卡号码: {acc_nbr}，请求参数: {accu_request_payload}")

                try:
                    accu_result = UnitTool.send_post_request(accu_use_url, accu_request_payload)
                    logging2.info(f"AccuUseQryCenter接口返回结果(主卡): {accu_result}")

                    if accu_result and accu_result.get("resultCode") == "0":
                        offer_inst_infos = accu_result.get("offerInstInfo", [])
                        
                        for offer_info in offer_inst_infos:
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            accu_qry_list = offer_info.get("accuQryList", [])
                            
                            # 处理offerId=620001的数据
                            if offer_id == 620001:
                                for accu_item in accu_qry_list:
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    all_usage_data.append({
                                        "accNbr": acc_nbr,
                                        "offerInstId": offer_inst_id,
                                        "offerId": offer_id,
                                        "accuTypeId": accu_type_id,
                                        "accuTypeAttr": accu_type_attr,
                                        "usageAmount": usage_val,
                                        "accuTypeName": accu_type_name
                                    })
                            
                            # 处理unitTypeId=3的数据
                            for accu_item in accu_qry_list:
                                unit_type_id = accu_item.get("unitTypeId")
                                if str(unit_type_id) == "3":
                                    owner_type = accu_item.get("ownerType")
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    if owner_type == 2:
                                        # 创建去重键：accNbr + offerInstId
                                        dedup_key = f"{acc_nbr}_{offer_inst_id}"
                                        
                                        # 检查是否已经查询过相同的accNbr和offerInstId组合
                                        if dedup_key not in queried_user_resource_keys:
                                            queried_user_resource_keys.add(dedup_key)
                                            
                                            # ownerType=2，调用userResourceQueryDetailBon接口
                                            user_resource_payload = {
                                                "operAttrStruct": {
                                                    "lanId": 0,
                                                    "operOrgId": 0,
                                                    "operPost": 0,
                                                    "operServiceId": "string",
                                                    "operTime": "string",
                                                    "staffId": 11111111111
                                                },
                                                "svcObjectStruct": {
                                                    "objType": "3",
                                                    "objValue": acc_nbr,
                                                    "objAttr": "2",
                                                    "dataArea": "2"
                                                },
                                                "queryFlag": "3",
                                                "billingCycle": int(billing_cycle),
                                                "offerInstId": str(offer_inst_id)
                                            }
                                            
                                            logging2.info(f"调用userResourceQueryDetailBon接口，主卡号码: {acc_nbr}，offerInstId: {offer_inst_id}")
                                            
                                            try:
                                                user_resource_result = UnitTool.send_post_request(user_resource_url, user_resource_payload)
                                                logging2.info(f"userResourceQueryDetailBon接口返回结果(主卡): {user_resource_result}")
                                                
                                                if user_resource_result and user_resource_result.get("resultCode") == "0":
                                                    detail_offer_inst_infos = user_resource_result.get("detailOfferInstInfo", [])
                                                    
                                                    for detail_offer_info in detail_offer_inst_infos:
                                                        detail_offer_id = detail_offer_info.get("offerId")
                                                        detail_offer_inst_id = detail_offer_info.get("offerInstId")
                                                        accu_qry_user_list = detail_offer_info.get("accuQryUserList", [])
                                                        
                                                        for user_info in accu_qry_user_list:
                                                            user_unit_type_id = user_info.get("unitTypeId")
                                                            if str(user_unit_type_id) == "3":
                                                                user_acc_nbr = user_info.get("accNbr")
                                                                user_accu_type_id = user_info.get("accuTypeId")
                                                                user_accu_type_attr = user_info.get("accuTypeAttr")
                                                                user_usage_amount = user_info.get("usageAmount")
                                                                
                                                                all_usage_data.append({
                                                                    "accNbr": user_acc_nbr,
                                                                    "offerInstId": detail_offer_inst_id,
                                                                    "offerId": detail_offer_id,
                                                                    "accuTypeId": user_accu_type_id,
                                                                    "accuTypeAttr": user_accu_type_attr,
                                                                    "usageAmount": user_usage_amount,
                                                                    "accuTypeName": accu_type_name  # 使用第一个接口的accuTypeName
                                                                })
                                            except Exception as user_resource_ex:
                                                logging2.error(f"调用userResourceQueryDetailBon接口失败(主卡): {str(user_resource_ex)}")
                                        else:
                                            logging2.info(f"跳过重复查询userResourceQueryDetailBon接口，主卡号码: {acc_nbr}，offerInstId: {offer_inst_id}")
                                    
                                    elif owner_type == 1:
                                        # ownerType=1，直接从原接口返回数据
                                        all_usage_data.append({
                                            "accNbr": acc_nbr,
                                            "offerInstId": offer_inst_id,
                                            "offerId": offer_id,
                                            "accuTypeId": accu_type_id,
                                            "accuTypeAttr": accu_type_attr,
                                            "usageAmount": usage_val,
                                            "accuTypeName": accu_type_name
                                        })
                        
                except Exception as accu_ex:
                    logging2.error(f"调用AccuUseQryCenter接口失败(主卡): {str(accu_ex)}")
            
            # 处理副卡数据（不包含ownerType=2的特殊处理）
            for card in sub_card_data:
                acc_nbr = card["acc_nbr"]
                
                # 构建AccuUseQryCenter请求数据
                accu_request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询副卡号码: {acc_nbr}，请求参数: {accu_request_payload}")

                try:
                    accu_result = UnitTool.send_post_request(accu_use_url, accu_request_payload)
                    logging2.info(f"AccuUseQryCenter接口返回结果(副卡): {accu_result}")

                    if accu_result and accu_result.get("resultCode") == "0":
                        offer_inst_infos = accu_result.get("offerInstInfo", [])
                        
                        for offer_info in offer_inst_infos:
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            accu_qry_list = offer_info.get("accuQryList", [])
                            
                            # 处理offerId=620001的数据
                            if offer_id == 620001:
                                for accu_item in accu_qry_list:
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    all_usage_data.append({
                                        "accNbr": acc_nbr,
                                        "offerInstId": offer_inst_id,
                                        "offerId": offer_id,
                                        "accuTypeId": accu_type_id,
                                        "accuTypeAttr": accu_type_attr,
                                        "usageAmount": usage_val,
                                        "accuTypeName": accu_type_name
                                    })
                            
                            # 处理unitTypeId=3的数据（副卡只处理ownerType=1的情况）
                            for accu_item in accu_qry_list:
                                unit_type_id = accu_item.get("unitTypeId")
                                if str(unit_type_id) == "3":
                                    owner_type = accu_item.get("ownerType")
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    if owner_type == 1:
                                        # ownerType=1，直接从原接口返回数据
                                        all_usage_data.append({
                                            "accNbr": acc_nbr,
                                            "offerInstId": offer_inst_id,
                                            "offerId": offer_id,
                                            "accuTypeId": accu_type_id,
                                            "accuTypeAttr": accu_type_attr,
                                            "usageAmount": usage_val,
                                            "accuTypeName": accu_type_name
                                        })
                                    # 副卡不处理ownerType=2的情况
                        
                except Exception as accu_ex:
                    logging2.error(f"调用AccuUseQryCenter接口失败(副卡): {str(accu_ex)}")

            # 添加查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "usage_data": all_usage_data
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询主副卡使用量完成",
            "input_details": response_list
        }
        
        # 检查是否有usage_data，有值才添加可视化数据
        has_usage_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                usage_data = detail.get("usage_data", [])
                if usage_data and len(usage_data) > 0:
                    has_usage_data = True
                    break
        
        logging2.info(f"查询主副卡使用量完成，返回结果: {final_response}")
        
        # 根据是否有usage_data决定是否添加可视化数据
        if has_usage_data:
            logging2.info("检测到使用量数据，添加可视化数据")
            return jsonify(add_visualization_data("main_secondary_card_usage", final_response, "主副卡使用量分析", ""))
        else:
            logging2.info("未检测到使用量数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询主副卡使用量请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500

# 主副卡套外使用量接口
@relation_bp.route('/query/primary_secondary_card_overflow_usage', methods=['POST'])
def primary_secondary_card_overflow_usage():
    """
    主副卡套外使用量查询接口
    查询主副卡关系，分别查询主卡和副卡的套外使用量，并合并去重
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"主副卡套外使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []

        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')

            if not billing_nb or not prod_inst_id or not latn_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始查询主副卡套外使用量: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, latn_id={latn_id}, billing_cycle={billing_cycle}")

            # 查询主副卡关系，分别收集主卡和副卡的产品实例ID和号码
            main_card_data = []  # 主卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            sub_card_data = []   # 副卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            replacements = [['##LATNID##', str(latn_id)]]

            try:
                # 查询QueryProdInstRel_a_100800获取a_prod_inst_id
                params = [prod_inst_id, prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )

                if a_rows:
                    for row in a_rows:
                        a_prod_inst_id = row[0] if row else None
                        if a_prod_inst_id:
                            # 查询QueryProdInstRel_100800获取主副卡关系
                            params2 = [a_prod_inst_id]
                            a1_rows = db_manager_instance.excute_sql(
                                sql_name='QueryProdInstRel_100800',
                                params=params2,
                                lst_replace_code_value=replacements
                            )

                            if a1_rows:
                                # 收集主副卡的产品实例ID
                                for a1_row in a1_rows:
                                    if len(a1_row) >= 2:
                                        main_prod_inst_id = a1_row[0]  # a_prod_inst_id (主卡)
                                        sub_prod_inst_id = a1_row[1]   # z_prod_inst_id (副卡)

                                        # 查询主卡号码
                                        if main_prod_inst_id:
                                            params3 = [main_prod_inst_id]
                                            main_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params3,
                                                lst_replace_code_value=replacements
                                            )
                                            if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                                main_card_data.append({
                                                    "prod_inst_id": str(main_prod_inst_id),
                                                    "acc_nbr": main_acc_rows[0][0]
                                                })

                                        # 查询副卡号码
                                        if sub_prod_inst_id:
                                            params4 = [sub_prod_inst_id]
                                            sub_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params4,
                                                lst_replace_code_value=replacements
                                            )
                                            if sub_acc_rows and sub_acc_rows[0] and sub_acc_rows[0][0]:
                                                sub_card_data.append({
                                                    "prod_inst_id": str(sub_prod_inst_id),
                                                    "acc_nbr": sub_acc_rows[0][0]
                                                })
                            else:
                                # 如果没有主副卡关系，当前产品实例作为主卡处理
                                params3 = [a_prod_inst_id]
                                main_acc_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAccNumByProdInstId',
                                    params=params3,
                                    lst_replace_code_value=replacements
                                )
                                if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                    main_card_data.append({
                                        "prod_inst_id": str(a_prod_inst_id),
                                        "acc_nbr": main_acc_rows[0][0]
                                    })
                else:
                    # 如果没有查到主副卡关系，直接使用传入的prod_inst_id和billing_nb作为主卡
                    main_card_data.append({
                        "prod_inst_id": str(prod_inst_id),
                        "acc_nbr": billing_nb
                    })

                # 对主卡和副卡数据进行去重处理：使用字典来去重，以prod_inst_id和acc_nbr的组合作为唯一键
                main_card_unique = {}
                for card in main_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    main_card_unique[key] = card
                main_card_data = list(main_card_unique.values())

                sub_card_unique = {}
                for card in sub_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    sub_card_unique[key] = card
                sub_card_data = list(sub_card_unique.values())

                logging2.info(f"查询到主卡数据(去重后): {main_card_data}")
                logging2.info(f"查询到副卡数据(去重后): {sub_card_data}")

            except Exception as e:
                logging2.error(f"查询主副卡关系时出错: {str(e)}")
                # 如果查询出错，则只使用传入的数据作为主卡
                main_card_data.append({
                    "prod_inst_id": str(prod_inst_id),
                    "acc_nbr": billing_nb
                })

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("AccuUseQryCenter")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 存储所有查询结果，用于最后去重
            all_offer_info = {}  # 使用字典存储，key为(offerId, offerInstId)，value为offer_info

            # 查询主卡数据：只提取unitTypeId=3的数据
            logging2.info("开始查询主卡数据...")
            for main_card in main_card_data:
                acc_nbr = main_card["acc_nbr"]

                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询主卡，号码: {acc_nbr}，请求参数: {request_payload}")

                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"主卡AccuUseQryCenter接口返回结果: {result}")

                    if result and result.get("resultCode") == "0":
                        # 提取主卡数据，只要unitTypeId=3的数据
                        for offer_info in result.get("offerInstInfo", []):
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            eff_date = offer_info.get("EFF_DATE")
                            exp_date = offer_info.get("EXP_DATE")
                            calc_priority = offer_info.get("CALC_PRIORITY")

                            # 过滤accuQryList中unitTypeId为3的数据
                            filtered_accu_list = []
                            for accu_info in offer_info.get("accuQryList", []):
                                unit_type_id = str(accu_info.get("unitTypeId", ""))
                                if unit_type_id == "3":
                                    filtered_accu_list.append({
                                        "accuTypeId": accu_info.get("accuTypeId"),
                                        "accuTypeName": accu_info.get("accuTypeName"),
                                        "beginTime": accu_info.get("beginTime"),
                                        "endTime": accu_info.get("endTime"),
                                        "usageVal": accu_info.get("usageVal"),
                                        "unitTypeId": accu_info.get("unitTypeId")
                                    })

                            # 只有当过滤后的列表不为空时才添加
                            if filtered_accu_list:
                                key = (offer_id, offer_inst_id)
                                all_offer_info[key] = {
                                    "accNbr": acc_nbr,
                                    "offerId": offer_id,
                                    "offerInstId": offer_inst_id,
                                    "EFF_DATE": eff_date,
                                    "EXP_DATE": exp_date,
                                    "CALC_PRIORITY": calc_priority,
                                    "accuQryList": filtered_accu_list,
                                    "cardType": "主卡"
                                }
                                logging2.info(f"添加主卡数据: offerId={offer_id}, offerInstId={offer_inst_id}")
                    else:
                        logging2.warning(f"主卡AccuUseQryCenter接口调用失败，号码: {acc_nbr}，错误信息: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")

                except Exception as req_ex:
                    logging2.error(f"请求主卡号码 {acc_nbr} 时出错: {str(req_ex)}")

            # 查询副卡数据：提取offerId=620001和unitTypeId=3的数据
            logging2.info("开始查询副卡数据...")
            for sub_card in sub_card_data:
                acc_nbr = sub_card["acc_nbr"]

                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询副卡，号码: {acc_nbr}，请求参数: {request_payload}")

                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"副卡AccuUseQryCenter接口返回结果: {result}")

                    if result and result.get("resultCode") == "0":
                        # 提取副卡数据，要offerId=620001或unitTypeId=3的数据
                        for offer_info in result.get("offerInstInfo", []):
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            eff_date = offer_info.get("EFF_DATE")
                            exp_date = offer_info.get("EXP_DATE")
                            calc_priority = offer_info.get("CALC_PRIORITY")

                            # 检查是否满足副卡条件：offerId=620001 或 包含unitTypeId=3的数据
                            should_include = False
                            filtered_accu_list = []

                            if str(offer_id) == "620001":
                                # offerId=620001的数据，提取所有accuQryList
                                should_include = True
                                for accu_info in offer_info.get("accuQryList", []):
                                    filtered_accu_list.append({
                                        "accuTypeId": accu_info.get("accuTypeId"),
                                        "accuTypeName": accu_info.get("accuTypeName"),
                                        "beginTime": accu_info.get("beginTime"),
                                        "endTime": accu_info.get("endTime"),
                                        "usageVal": accu_info.get("usageVal"),
                                        "unitTypeId": accu_info.get("unitTypeId")
                                    })
                            else:
                                # 其他offerId，只提取unitTypeId=3的数据
                                for accu_info in offer_info.get("accuQryList", []):
                                    unit_type_id = str(accu_info.get("unitTypeId", ""))
                                    if unit_type_id == "3":
                                        should_include = True
                                        filtered_accu_list.append({
                                            "accuTypeId": accu_info.get("accuTypeId"),
                                            "accuTypeName": accu_info.get("accuTypeName"),
                                            "beginTime": accu_info.get("beginTime"),
                                            "endTime": accu_info.get("endTime"),
                                            "usageVal": accu_info.get("usageVal"),
                                            "unitTypeId": accu_info.get("unitTypeId")
                                        })

                            # 只有当满足条件且过滤后的列表不为空时才添加
                            if should_include and filtered_accu_list:
                                if str(offer_id) == "620001":
                                    # offerId=620001的数据：副卡独立保留，不合并
                                    key = (acc_nbr, offer_id, offer_inst_id)
                                    all_offer_info[key] = {
                                        "accNbr": acc_nbr,
                                        "offerId": offer_id,
                                        "offerInstId": offer_inst_id,
                                        "EFF_DATE": eff_date,
                                        "EXP_DATE": exp_date,
                                        "CALC_PRIORITY": calc_priority,
                                        "accuQryList": filtered_accu_list,
                                        "cardType": "副卡"
                                    }
                                    logging2.info(f"副卡独立保留offerId=620001数据: 号码={acc_nbr}, offerId={offer_id}, offerInstId={offer_inst_id}")
                                else:
                                    # 其他offerId：检查能否合并到主卡
                                    merge_key = (offer_id, offer_inst_id)

                                    if merge_key in all_offer_info:
                                        # 能合并到主卡：进行去重处理
                                        existing_accu_list = all_offer_info[merge_key]["accuQryList"]
                                        existing_accu_keys = set()
                                        for accu in existing_accu_list:
                                            accu_key = (accu.get("accuTypeId"), accu.get("beginTime"), accu.get("endTime"))
                                            existing_accu_keys.add(accu_key)

                                        # 添加新的不重复的accu数据
                                        for new_accu in filtered_accu_list:
                                            new_accu_key = (new_accu.get("accuTypeId"), new_accu.get("beginTime"), new_accu.get("endTime"))
                                            if new_accu_key not in existing_accu_keys:
                                                existing_accu_list.append(new_accu)
                                                existing_accu_keys.add(new_accu_key)

                                        logging2.info(f"副卡数据合并到主卡（去重）: offerId={offer_id}, offerInstId={offer_inst_id}")
                                    else:
                                        # 不能合并到主卡：副卡独立保留
                                        sub_key = (acc_nbr, offer_id, offer_inst_id)
                                        all_offer_info[sub_key] = {
                                            "accNbr": acc_nbr,
                                            "offerId": offer_id,
                                            "offerInstId": offer_inst_id,
                                            "EFF_DATE": eff_date,
                                            "EXP_DATE": exp_date,
                                            "CALC_PRIORITY": calc_priority,
                                            "accuQryList": filtered_accu_list,
                                            "cardType": "副卡"
                                        }
                                        logging2.info(f"副卡独立保留无法合并的数据: 号码={acc_nbr}, offerId={offer_id}, offerInstId={offer_inst_id}")
                    else:
                        logging2.warning(f"副卡AccuUseQryCenter接口调用失败，号码: {acc_nbr}，错误信息: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")

                except Exception as req_ex:
                    logging2.error(f"请求副卡号码 {acc_nbr} 时出错: {str(req_ex)}")

            # 将去重后的结果转换为列表
            final_offer_list = list(all_offer_info.values())
            
            logging2.info(f"合并去重后的结果数量: {len(final_offer_list)}")

            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "latn_id": latn_id,
                "overflowUsageDetails": final_offer_list
            })

        # 返回最终结果
        final_response = {
            "status": "success",
            "message": "主副卡套外使用量查询完成",
            "input_details": response_list
        }
        
        logging2.info(f"主副卡套外使用量查询完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理主副卡套外使用量查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500