import json
import logging2
import traceback
import pandas as pd
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from UnitTool import UnitTool
from configLoader import ConfigLoader
from httpClient import HTTPClient
from Visualization import add_visualization_data

# 创建定价协议服务蓝图
pricing_bp = Blueprint('pricing_services', __name__)

#乡镇、校园-定价协议查询
@pricing_bp.route('/query/userTownFlowSchoolCell', methods=['POST'])
def userTownFlowSchoolCell():
    """
    乡镇、校园-定价协议查询
    """
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")
        
        # 初始化响应列表
        response_list = []
        
        # 处理每个输入项
        for input_row in request_data.get('input_details', []):
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')
            
            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            a1_rows = db_manager_instance.excute_sql(
                sql_name='QueryOfferProdInstRelHisA1Info',
                params=params,
                lst_replace_code_value=replacements
            )
            
            # 初始化销售品详情列表
            offer_detail = []
            
            # 处理每个销售品实例
            for offer_row in a1_rows:
                (offer_inst_id, prod_offer_id, eff_date,
                 exp_date, update_date, obj_id, obj_type, ext_offer_inst_id) = offer_row
                
                # 查询乡镇基站信息
                town_flow_cell_id = _get_town_flow_cell_id(prod_offer_id, offer_inst_id)
                
                # 查询校园基站信息
                school_flow_flag = _check_school_flow(prod_offer_id)
                
                # 构建销售品详情
                offer_info = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": prod_offer_id,
                    "eff_date": eff_date.strftime('%Y-%m-%d %H:%M:%S') if eff_date else None,
                    "exp_date": exp_date.strftime('%Y-%m-%d %H:%M:%S') if exp_date else None,
                    "offer_townFlow": town_flow_cell_id,
                    "offer_school": school_flow_flag
                }
                offer_detail.append(offer_info)
                
            # 构建单条响应数据
            response_item = {
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)
            
        logging2.info(f"response_list:{response_list}")
        # 返回完整响应
        return jsonify(response_list)
        
    except Exception as e:
        logging2.error(f"处理请求时发生错误: {str(e)}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500

#话单基站是否属于校园基站或乡镇基站
@pricing_bp.route('/query/userTownFlowSchoolCellRtBillItem', methods=['POST'])
def userTownFlowSchoolCellRtBillItem():
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")

        # 验证请求数据结构
        if 'input_details' not in request_data or not isinstance(request_data['input_details'], list):
            raise ValueError("请求数据缺少input_details列表")

        # 初始化响应列表
        response_list = []
        bsid_summary_json = {}  # 存储解析后的BSID汇总数据

        # 处理每个输入项
        for input_row in request_data['input_details']:
            # 获取必要参数
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')

            # 参数验证
            if not all([billing_nb, prod_inst_id, latn_id, billing_cycle]):
                logging2.warning(f"输入项缺少必要参数: {input_row}")
                response_item = {
                    "status": "error",
                    "message": "缺少必要参数",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 构建HTTP请求数据
            request_date = {
                "endDate": billing_cycle + "31",
                "operAttrStruct": {
                    "operServiceId": "202501140913056692116205",
                    "lanId": latn_id,
                    "operPost": 1,
                    "operOrgId": 0,
                    "staffId": 0,
                    "operTime": "20250114091305"
                },
                "isDesensitive": "0",
                "svcObjectStruct": {
                    "objValue": billing_nb,
                    "objAttr": "2",
                    "objType": "3",
                    "dataArea": "2"
                },
                "qryType": "3",
                "startDate": billing_cycle + "01"
            }

            # 发送HTTP请求
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            http_client = HTTPClient(ConfigLoader(config_path))
            http_response = http_client.send_post_request(
                payload=request_date,
                url_key="RtBillItem"
            )

            # 处理HTTP响应
            if not http_response:
                logging2.error(f"HTTP请求失败: billing_nb={billing_nb}")
                response_item = {
                    "status": "error",
                    "message": "获取数据失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 解析流量数据并存储
            bsid_summary_json = _parse_traffic_data(http_response)
            bsid_data = json.loads(bsid_summary_json) if isinstance(bsid_summary_json, str) else {}

            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]

            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
            except Exception as e:
                logging2.error(f"SQL查询失败: {e}, params={params}")
                response_item = {
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 初始化销售品详情列表
            offer_detail = []

            # 处理每个销售品实例
            for offer_row in a1_rows:
                (offer_inst_id, prod_offer_id, eff_date,
                 exp_date, update_date, obj_id, obj_type, ext_offer_inst_id) = offer_row

                # 查询乡镇基站信息
                town_flow_cell_id = _get_town_flow_cell_id(prod_offer_id, offer_inst_id)

                # 查询校园基站信息
                school_flow_flag = _check_school_flow(prod_offer_id)

                # 构建销售品详情
                offer_info = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": prod_offer_id,
                    "eff_date": eff_date.strftime('%Y-%m-%d %H:%M:%S') if eff_date else None,
                    "exp_date": exp_date.strftime('%Y-%m-%d %H:%M:%S') if exp_date else None,
                    "offer_townFlow": town_flow_cell_id,
                    "offer_school": school_flow_flag
                }
                offer_detail.append(offer_info)

            # 构建单条响应数据
            response_item = {
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)

        # ============== 添加用户要求的逻辑 ==============
        # 提取非空的offer_townFlow和offer_school
        offer_town_flow_list = []
        school_flow_flag_list = []

        for item in response_list:
            if item.get("status") == "success":
                for offer in item.get("offer_detail", []):
                    town_flow = offer.get("offer_townFlow")
                    school_flag = offer.get("offer_school")
                    if town_flow:
                        offer_town_flow_list.append(town_flow)
                    if school_flag is not None:  # 检查是否非空
                        school_flow_flag_list.append(school_flag)

        # 去重处理
        offer_town_flow_list = list(set(offer_town_flow_list))
        school_flow_flag_list = list(set(school_flow_flag_list))

        # 存储查询结果
        offer_town_flow_list_ids = []
        school_flow_flag_list_ids = []

        # 遍历乡镇基站列表查询数据库
        for town_flow in offer_town_flow_list:
            if town_flow:
                try:
                    # 假设查询数据库的函数，实际需根据数据库结构调整
                    result = db_manager_instance.excute_sql(
                        sql_name='QueryCellId',
                        params=[town_flow]
                    )
                    if result:
                        offer_town_flow_list_ids.extend([row[0] for row in result])
                except Exception as e:
                    logging2.error(f"查询乡镇基站ID失败: {e}, town_flow={town_flow}")

        # 遍历校园基站列表查询数据库
        for school_flag in school_flow_flag_list:
            if school_flag is not None:
                try:
                    # 假设查询数据库的函数，实际需根据数据库结构调整
                    result = db_manager_instance.excute_sql(
                        sql_name='QueryCellId',
                        params=[53]
                    )
                    if result:
                        school_flow_flag_list_ids.extend([row[0] for row in result])
                except Exception as e:
                    logging2.error(f"查询校园基站ID失败: {e}, school_flag={school_flag}")

        # 在bsidSummaryJson中匹配数据
        bsid_data = json.loads(bsid_summary_json) if isinstance(bsid_summary_json, str) else {}
        town_bsids = []
        school_bsids = []

        for bsid_info in bsid_data.get("bsidSummary", []):
            bsid = bsid_info.get("bsid")
            if bsid in offer_town_flow_list_ids:
                town_bsids.append(bsid)
            if bsid in school_flow_flag_list_ids:
                school_bsids.append(bsid)

        # 将结果添加到响应中
        final_response = {
            "status": "success",
            "message": "所有数据处理完成",
            "original_response": response_list,
            "town_bsids": town_bsids,
            "school_bsids": school_bsids,
            "bsid_summary": bsid_data
        }

        return jsonify(final_response)

    except Exception as e:
        logging2.error(f"处理请求时发生未知错误: {e}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500


def _get_town_flow_cell_id(prod_offer_id, offer_inst_id):
    """查询乡镇基站组ID的辅助函数"""
    # 查询订购销售品是否有乡镇基站销售品
    town_flow_rows = db_manager_instance.excute_sql(
        sql_name='QueryTownFlowCellAttr',
        params=[prod_offer_id],
        lst_replace_code_value=[]
    )
    if not town_flow_rows:
        return ""
    # 获取属性定义ID
    property_define_id = town_flow_rows[0][1]  # 假设第二列是property_define_id
    # 查询乡镇基站组ID
    town_flow_cell = db_manager_instance.excute_sql(
        sql_name='QueryTownFlowAttrValue',
        params=[offer_inst_id, property_define_id],
        lst_replace_code_value=[]
    )
    return town_flow_cell[0][0] if town_flow_cell else ""


def _check_school_flow(prod_offer_id):
    """检查是否为校园流量销售品的辅助函数"""
    school_offer_result = db_manager_instance.excute_sql(
        'QuerySchoolFlowOffer',
        params=[prod_offer_id]
    )
    return 53 if school_offer_result else ""


# 提取清单报文中的bsid，汇总Volume、Fee以及最早时间StartTime
def _parse_traffic_data(json_data):
    try:
        # 解析JSON数据
        data = json.loads(json_data)
        # 提取数据项列表
        items = data.get("dataBillItems", [])
        if not items:
            return json.dumps({
                "status": "error",
                "message": "没有找到数据项"
            })

        # 转换为DataFrame以便处理
        df = pd.DataFrame(items)
        # 处理可能的空值或非数值类型
        df["volume"] = pd.to_numeric(df["volume"], errors="coerce").fillna(0).astype(int)
        df["fee"] = pd.to_numeric(df["fee"], errors="coerce").fillna(0).astype(int)
        # 转换时间格式
        df["startTime"] = pd.to_datetime(df["startTime"], format="%Y%m%d%H%M%S")
        # 按bsid分组，计算volume和fee的总和，以及最早的startTime
        grouped = df.groupby("bsid").agg({
            "volume": "sum",
            "fee": "sum",
            "startTime": "min"
        }).reset_index()
        # 准备结果JSON
        result = {
            "bsidSummary": [
                {
                    "bsid": str(row["bsid"]),  # 确保BSID为字符串类型
                    "totalVolume": row["volume"],
                    "totalFee": row["fee"],
                    "earliestStartTime": row["startTime"].strftime("%Y%m%d%H%M%S")
                }
                for _, row in grouped.iterrows()
            ]
        }
        return json.dumps(result, ensure_ascii=False)
    except Exception as e:
        logging2.error(f"解析流量数据失败: {e}")
        return json.dumps({
            "status": "error",
            "message": f"解析流量数据失败: {str(e)}"
        })