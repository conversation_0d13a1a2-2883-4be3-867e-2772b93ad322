import json
from collections import defaultdict


def parse_international_roaming_data(json_data):
    """
    解析报文数据，筛选国际漫游上网记录并按日期和地区汇总流量和费用

    参数:
    json_data (dict): 包含通信记录的JSON数据

    返回:
    list: 按指定格式汇总的结果列表
    """
    # 用于存储汇总结果的嵌套字典
    summary = defaultdict(lambda: defaultdict(lambda: {"volume": 0, "fee": 0}))

    # 遍历数据中的每条记录
    for item in json_data.get("dataBillItems", []):
        # 筛选业务类型为"国际漫游上网"的记录
        if item.get("businessType") == "国际漫游上网":
            # 提取日期（startTime前8位）
            date = item.get("startTime", "")[:8]
            # 提取主叫地区
            calling_area = item.get("callingArea", "")
            # 提取流量和费用，确保为数值类型
            volume = int(item.get("volume", 0))
            fee = int(item.get("fee", 0))

            # 按日期和地区汇总
            summary[date][calling_area]["volume"] += volume
            summary[date][calling_area]["fee"] += fee

    # 转换为指定格式的列表
    result_list = []
    for date, areas in summary.items():
        for area, stats in areas.items():
            result_list.append({
                "startTime": date,
                "callingArea": area,
                "volume": stats["volume"],
                "fee": stats["fee"]
            })

    return result_list


def main():
    # 示例JSON数据
    json_data = {
        "resultCode": "0",
        "resultMsg": "操作成功",
        "svcObjectStruct": {
            "objType": "3",
            "objValue": "13317916920",
            "objAttr": "2",
            "dataArea": "2"
        },
        "operAttrStruct": {
            "staffId": 0,
            "operOrgId": 0,
            "operTime": "20250114091305",
            "operPost": 1,
            "operServiceId": "202501140913056692116205",
            "lanId": 791
        },
        "startDate": 20250601,
        "endDate": 20250631,
        "provinceCode": "791",
        "qryType": "3",
        "totalRecord": 731,
        "totalVolume": 33480059,
        "totalFee": 5000,
        "dataBillItems": [
            {
                "startTime": "20250625084030",
                "callingArea": "南昌",
                "rg": "3000000000",
                "listType": "5G",
                "duration": 3595,
                "volume": 90955,
                "businessType": "普通上网",
                "fee": 0,
                "bsid": "***********",
                "acctItemTypeName": "国内流量费(状态:掌中宽带本地)",
                "imei": "3574532762180814",
                "AccuInfoStruct": [
                    {
                        "offerInstId": "100069795243",
                        "offerName": "5G升级会员20G包29元201910",
                        "Amount": "90955",
                        "unit": "3"
                    }
                ]
            },
            {
                "startTime": "20250601000159",
                "callingArea": "法国",
                "rg": "",
                "listType": "",
                "duration": 42,
                "volume": 8862,
                "businessType": "国际漫游上网",
                "fee": 0,
                "bsid": "",
                "acctItemTypeName": "",
                "imei": "",
                "AccuInfoStruct": []
            },
            {
                "startTime": "20250601000010",
                "callingArea": "法国",
                "rg": "",
                "listType": "",
                "duration": 109,
                "volume": 49901,
                "businessType": "国际漫游上网",
                "fee": 0,
                "bsid": "",
                "acctItemTypeName": "",
                "imei": "",
                "AccuInfoStruct": []
            }
        ]
    }

    # 解析数据
    result = parse_international_roaming_data(json_data)

    # 转换为JSON格式并打印
    json_result = json.dumps(result, ensure_ascii=False, indent=2)
    print("国际漫游上网数据汇总结果（JSON格式）：")
    print(json_result)


if __name__ == "__main__":
    main()