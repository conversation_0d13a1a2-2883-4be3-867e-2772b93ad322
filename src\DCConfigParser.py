import xml.etree.ElementTree as ET

class DCConfigParser:
    def __init__(self, config_file):
        self.tree = ET.parse(config_file)
        self.root = self.tree.getroot()

    def get_log_path(self):
        element = self.root.find('.//log/param[@name="logpath"]')
        return element.text if element is not None else None

    def get_log_module(self):
        element = self.root.find('.//log/param[@name="logmodule"]')
        return element.text if element is not None else None

    def get_log_level(self):
        element = self.root.find('.//log/param[@name="loglevel"]')
        return element.text if element is not None else None

    def get_sql_file(self):
        element = self.root.find('.//sql/param[@name="sql_file"]')
        return element.text if element is not None else None

    def get_flask_ip(self):
        element = self.root.find('.//flask/param[@name="host"]')
        return element.text if element is not None else None
    
    def get_flask_port(self):
        element = self.root.find('.//flask/param[@name="port"]')
        return element.text if element is not None else None
    
    def get_flask_debug(self):
        element = self.root.find('.//flask/param[@name="debug"]')
        if element is not None and element.text:
            return element.text.lower() in ['true', '1', 'yes', 'on']
        return False

    def get_http_config_file(self):
        element = self.root.find('.//http_config/param[@name="config_file"]')
        return element.text if element is not None else None

    def get_charge_type_id(self):
        element = self.root.find('.//charge_filter/param[@name="CHARGE_TYPE_ID"]')
        return element.text if element is not None else None

    def get_config_value(self, path):
        element = self.root.find(path)
        return element.text if element is not None else None