        <param name="查询非不限量流量争议诊断服务">/query/non_unlimited_flux_dispute_service</param>
        <param name="查询收费清单服务">/query/charge_detail_service</param>
        <param name="查询是否有剩余量本服务">/query/remain_accu_service</param>
        <param name="查询不限量套餐服务">/query/unlimited_package_service</param>
        <param name="查询流量溢出费用账单服务">/query/flux_overflow_fee_service</param>
        <param name="查询用户资料服务">/query/user_profile_service</param>
        <param name="号段表本地网查询">/query/latn_id_service</param>
        <param name="账期1+1判断查询">/query/is_billing_cycle_service</param>

# <param name="账期1+1判断查询">/query/is_billing_cycle_service</param>
文本显示

# <param name="号段表本地网查询">/query/latn_id_service</param>
文本显示

# <param name="查询用户资料服务">/query/user_profile_service</param>
表格显示

# <param name="查询流量溢出费用账单服务">/query/flux_overflow_fee_service</param>
表格显示

# <param name="查询不限量套餐服务">/query/unlimited_package_service</param>
表格显示

# <param name="查询是否有剩余量本服务">/query/remain_accu_service</param>
柱状图展示：初始值|使用值|剩余值

# <param name="查询收费清单服务">/query/charge_detail_service</param>
折线图：x轴按天，Y轴按金额，唯独按RG

# <param name="查询非不限量流量争议诊断服务">/query/non_unlimited_flux_dispute_service</param>
