import json
import logging2
import traceback
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path, get_charge_type_id_list
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data

# 创建权益包服务蓝图
benefits_bp = Blueprint('benefits_services', __name__)

def _process_fee_bill_data_without_card_relation(api_result, acct_type):
    """
    处理账单费用数据 - 根据ACCT_TYPE过滤数据
    """
    try:
        # 获取原始数据
        acct_name = api_result.get("ACCT_NAME", "")
        fee_info_list = api_result.get("feeInfoList", [])
        
        if not fee_info_list:
            return {
                "status": "success",
                "message": "未查询到账单费用数据",
                "ACCT_NAME": acct_name,
                "feeInfoList": [],
                "totalCount": "0"
            }

        # 根据ACCT_TYPE进行数据过滤
        filtered_fee_list = []
        
        for fee_item in fee_info_list:
            acct_item_type_name = fee_item.get("ACCT_ITEM_TYPE_NAME", "")

            # 先过滤AMOUNT为0的记录
            amount = fee_item.get("AMOUNT", "0")
            # 使用字符串比较判断AMOUNT为0的情况，包括"0"和"0.00"
            if amount in ["0", "0.0", "0.00"] or not amount or amount.strip() == "":
                continue  # 跳过AMOUNT为0的记录

            # 根据ACCT_TYPE进行过滤
            should_include = False
            
            if acct_type == "1":  # 流量
                if "流量" in acct_item_type_name:
                    should_include = True
            elif acct_type == "2":  # 语音
                if "语音" in acct_item_type_name:
                    should_include = True
            elif acct_type == "3":  # 短信
                if "短信" in acct_item_type_name:
                    should_include = True
            elif acct_type == "4":  # 功能费
                try:
                    # 使用配置的CHARGE_TYPE_ID进行过滤
                    charge_type_ids = get_charge_type_id_list()
                    if charge_type_ids:
                        # 从fee_item中获取CHARGE_TYPE_ID字段
                        charge_type_id = fee_item.get("CHARGE_TYPE_ID")
                        if charge_type_id:
                            charge_type_id_int = int(charge_type_id)
                            if charge_type_id_int in charge_type_ids:
                                should_include = True
                    # 如果没有配置CHARGE_TYPE_ID，则全部过滤，不包含任何功能费记录
                except (ValueError, TypeError):
                    # 如果CHARGE_TYPE_ID不能转换为整数，则过滤掉
                    should_include = False
            elif acct_type == "5":  # 全部
                should_include = True
            
            if should_include:
                filtered_fee_list.append(fee_item)

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "ACCT_NAME": acct_name,
            "feeInfoList": filtered_fee_list,
            "totalCount": str(len(filtered_fee_list))
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理账单费用数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"处理数据时出错: {str(e)}",
            "ACCT_NAME": "",
            "feeInfoList": [],
            "totalCount": "0"
        }


@benefits_bp.route('/query/benefits_package_fee_relation', methods=['POST'])
def benefits_package_fee_relation():
    """
    权益包订购与费用关系接口
    调用qryJzrzDayFeeBill和PricingQuery接口，整合返回数据
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"权益包订购与费用关系接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            acct_id = row.get('ACCT_ID')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('LATN_ID')
            acct_type = row.get('ACCT_TYPE')

            # 基础参数校验
            if not acct_id or not prod_inst_id or not billing_nb or not billing_cycle or not latn_id or not acct_type:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 ACCT_ID、PROD_INST_ID、billing_nb、billing_cycle、LATN_ID 或 ACCT_TYPE",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 验证ACCT_TYPE参数
            if acct_type not in ['1', '2', '3', '4', '5']:
                response_list.append({
                    "status": "error",
                    "message": "无效的 ACCT_TYPE，应为1-5之间的数字",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始权益包订购与费用关系查询: ACCT_ID={acct_id}, PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}")

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            qry_fee_url = config_loader.get_url("qryJzrzDayFeeBill")
            pricing_query_url = config_loader.get_url("PricingQuery")
            
            if not qry_fee_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到qryJzrzDayFeeBill接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            if not pricing_query_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到PricingQuery接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 调用qryJzrzDayFeeBill接口
            qry_fee_payload = {
                "ACCT_ID": str(acct_id),
                "BILLING_CYCLE_ID": str(billing_cycle),
                "LATN_ID": str(latn_id),
                "QRY_TYPE": "0",
                "QUWERY_KIND_TYPE": "1",
                "DHZ": "",
                "IS_GROUP": 0,
                "QUERY_TYPE": "0",
                "ACCT_LATN_ID": str(latn_id),
                "QRYZERO": 0,
                "QRY_INVALID_FLAG": "0"
            }

            logging2.info(f"调用qryJzrzDayFeeBill接口，请求参数: {qry_fee_payload}")

            try:
                qry_fee_result = UnitTool.send_post_request(qry_fee_url, qry_fee_payload)
                logging2.info(f"qryJzrzDayFeeBill接口返回结果: {qry_fee_result}")

                if not qry_fee_result or qry_fee_result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"qryJzrzDayFeeBill接口调用失败: {qry_fee_result.get('resultMsg', '未知错误') if qry_fee_result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理qryJzrzDayFeeBill返回数据
                fee_bill_data = _process_fee_bill_data_without_card_relation(qry_fee_result, acct_type)
                
                if fee_bill_data.get("status") != "success":
                    response_list.append({
                        "status": "error",
                        "message": f"处理账单费用数据失败: {fee_bill_data.get('message', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                fee_info_list = fee_bill_data.get("feeInfoList", [])
                
                # 调用PricingQuery接口
                pricing_query_payload = {
                    "destinationAttr": "2",
                    "accNbr": billing_nb
                }

                logging2.info(f"调用PricingQuery接口，请求参数: {pricing_query_payload}")

                try:
                    pricing_query_result = UnitTool.send_post_request(pricing_query_url, pricing_query_payload)
                    logging2.info(f"PricingQuery接口返回结果: {pricing_query_result}")

                    if not pricing_query_result or pricing_query_result.get("resultCode") != "0":
                        logging2.warning(f"PricingQuery接口调用失败: {pricing_query_result.get('resultMsg', '未知错误') if pricing_query_result else '接口无响应'}")
                        pricing_plan_info = []
                    else:
                        pricing_plan_info = pricing_query_result.get("pricingPlanInfo", [])

                except Exception as pricing_ex:
                    logging2.error(f"调用PricingQuery接口时出错: {str(pricing_ex)}")
                    pricing_plan_info = []

                # 整合两个接口的数据
                integrated_data = []
                
                # 遍历feeInfoList，与pricingPlanInfo进行匹配
                for fee_item in fee_info_list:
                    acc_num = fee_item.get("ACC_NUM")
                    offer_inst_id = fee_item.get("OFFER_INST_ID")
                    
                    # 查找匹配的pricingPlanInfo
                    matched_pricing_plan = None
                    for pricing_item in pricing_plan_info:
                        service_nbr = pricing_item.get("ServiceNbr")
                        pricing_offer_inst_id = pricing_item.get("offerInstId")
                        
                        # 匹配条件：ACC_NUM与ServiceNbr相等，OFFER_INST_ID与offerInstId相等
                        if (str(acc_num) == str(service_nbr) and 
                            str(offer_inst_id) == str(pricing_offer_inst_id)):
                            matched_pricing_plan = pricing_item
                            break
                    
                    # 整合数据
                    if matched_pricing_plan:
                        # 创建pricing_plan的副本，移除pricingDesc字段
                        filtered_pricing_plan = {k: v for k, v in matched_pricing_plan.items() if k != "pricingDesc"}
                    else:
                        filtered_pricing_plan = matched_pricing_plan
                    
                    integrated_item = {
                        "feeInfo": fee_item,
                        "pricingPlan": filtered_pricing_plan
                    }
                    integrated_data.append(integrated_item)

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "ACCT_ID": acct_id,
                    "PROD_INST_ID": prod_inst_id,
                    "LATN_ID": latn_id,
                    "ACCT_TYPE": acct_type,
                    "ACCT_NAME": fee_bill_data.get("ACCT_NAME", ""),
                    "integratedData": integrated_data,
                    "totalCount": str(len(integrated_data))
                })

            except Exception as fee_ex:
                logging2.error(f"调用qryJzrzDayFeeBill接口时出错: {str(fee_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用qryJzrzDayFeeBill接口异常: {str(fee_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "权益包订购与费用关系查询完成",
            "input_details": response_list
        }
        
        # 检查是否有integratedData，有值才添加可视化数据
        has_integrated_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                integrated_data = detail.get("integratedData", [])
                if integrated_data and len(integrated_data) > 0:
                    has_integrated_data = True
                    break
        
        logging2.info(f"权益包订购与费用关系查询完成，返回结果: {final_response}")
        
        # 根据是否有integratedData决定是否添加可视化数据
        if has_integrated_data:
            logging2.info("检测到权益包费用关系数据，添加可视化数据")
            return jsonify(add_visualization_data("benefits_package_fee_relation", final_response, "权益包订购与费用关系", "权益包费用关系分析"))
        else:
            logging2.info("未检测到权益包费用关系数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理权益包订购与费用关系查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


@benefits_bp.route('/query/benefits_package_bill_fee', methods=['POST'])
def benefits_package_bill_fee():
    """
    权益包账单费用接口
    调用qryJzrzDayFeeBill接口，根据ACCT_TYPE过滤数据（不验证主副卡关系）
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"权益包账单费用接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            acct_id = row.get('ACCT_ID')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('LATN_ID')
            acct_type = row.get('ACCT_TYPE')

            # 基础参数校验
            if not acct_id or not prod_inst_id or not billing_nb or not billing_cycle or not latn_id or not acct_type:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 ACCT_ID、PROD_INST_ID、billing_nb、billing_cycle、LATN_ID 或 ACCT_TYPE",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 验证ACCT_TYPE参数
            if acct_type not in ['1', '2', '3', '4', '5']:
                response_list.append({
                    "status": "error",
                    "message": "无效的 ACCT_TYPE，应为1-5之间的数字",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始权益包账单费用查询: ACCT_ID={acct_id}, PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}")

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            qry_fee_url = config_loader.get_url("qryJzrzDayFeeBill")
            
            if not qry_fee_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到qryJzrzDayFeeBill接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 调用qryJzrzDayFeeBill接口
            qry_fee_payload = {
                "ACCT_ID": str(acct_id),
                "BILLING_CYCLE_ID": str(billing_cycle),
                "LATN_ID": str(latn_id),
                "QRY_TYPE": "0",
                "QUWERY_KIND_TYPE": "1",
                "DHZ": "",
                "IS_GROUP": 0,
                "QUERY_TYPE": "0",
                "ACCT_LATN_ID": str(latn_id),
                "QRYZERO": 0,
                "QRY_INVALID_FLAG": "0"
            }

            logging2.info(f"调用qryJzrzDayFeeBill接口，请求参数: {qry_fee_payload}")

            try:
                qry_fee_result = UnitTool.send_post_request(qry_fee_url, qry_fee_payload)
                logging2.info(f"qryJzrzDayFeeBill接口返回结果: {qry_fee_result}")

                if not qry_fee_result or qry_fee_result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"qryJzrzDayFeeBill接口调用失败: {qry_fee_result.get('resultMsg', '未知错误') if qry_fee_result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理qryJzrzDayFeeBill返回数据
                fee_bill_data = _process_fee_bill_data_without_card_relation(qry_fee_result, acct_type)
                
                if fee_bill_data.get("status") != "success":
                    response_list.append({
                        "status": "error",
                        "message": f"处理账单费用数据失败: {fee_bill_data.get('message', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "ACCT_ID": acct_id,
                    "PROD_INST_ID": prod_inst_id,
                    "LATN_ID": latn_id,
                    "ACCT_TYPE": acct_type,
                    "ACCT_NAME": fee_bill_data.get("ACCT_NAME", ""),
                    "feeInfoList": fee_bill_data.get("feeInfoList", []),
                    "totalCount": fee_bill_data.get("totalCount", "0")
                })

            except Exception as fee_ex:
                logging2.error(f"调用qryJzrzDayFeeBill接口时出错: {str(fee_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用qryJzrzDayFeeBill接口异常: {str(fee_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "权益包账单费用查询完成",
            "input_details": response_list
        }
        
        # 检查是否有feeInfoList数据，有值才添加可视化数据
        has_fee_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                fee_info_list = detail.get("feeInfoList", [])
                if fee_info_list and len(fee_info_list) > 0:
                    has_fee_data = True
                    break
        
        logging2.info(f"权益包账单费用查询完成，返回结果: {final_response}")
        
        # 根据是否有feeInfoList决定是否添加可视化数据
        if has_fee_data:
            logging2.info("检测到权益包账单费用数据，添加可视化数据")
            return jsonify(add_visualization_data("benefits_package_bill_fee", final_response, "权益包账单费用", "权益包账单费用明细"))
        else:
            logging2.info("未检测到权益包账单费用数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理权益包账单费用查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500