from DBPyMgr import DBPyMgr
import logging2
from DCConfigParser import DCConfigParser
import traceback
import signal
import sys
import os


global db_manager_instance
db_manager_instance = None
global configObj_instance
configObj_instance = None


def signal_handler(sig, frame):
    print('\nReceived Ctrl+C! Exiting gracefully...')
    # 清理Werkzeug环境变量
    cleanup_werkzeug_env()
    logging2.stop()
    sys.exit(0)


def cleanup_werkzeug_env():
    """清理所有 Werkzeug 相关的环境变量"""
    werkzeug_vars = [
        'WERKZEUG_SERVER_FD',
        'WERKZEUG_RUN_MAIN'
    ]
    for var in werkzeug_vars:
        if var in os.environ:
            try:
                del os.environ[var]
            except KeyError:
                pass  # 忽略已经被删除的变量


def setup_flask_environment():
    """设置 Flask 运行环境，避免 Werkzeug 冲突"""
    # 清理可能存在的 Werkzeug 环境变量
    cleanup_werkzeug_env()
    
    # 设置生产环境相关的环境变量
    os.environ['FLASK_ENV'] = 'production'
    
    # 确保不使用 reloader
    os.environ.pop('FLASK_DEBUG', None)


if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    if len(sys.argv) != 2:
        print("Usage: main <xmlfile>")
        sys.exit()

    try:
        # 设置 Flask 环境
        setup_flask_environment()
        
        # 解析xml配置文件
        xmlfile = sys.argv[1]
        configObj_instance = DCConfigParser(xmlfile)

        # 初始化全局配置解析器实例
        from api_services.config.globals import init_config_parser
        init_config_parser(configObj_instance)

        # 初始化日志模块
        logpath = configObj_instance.get_log_path()
        logmodule = configObj_instance.get_log_module()
        loglevel = configObj_instance.get_log_level()
        logging2.start(logpath, logmodule, loglevel)

        # 数据库初始化
        sqlxmlfile = configObj_instance.get_sql_file()
        from api_services.config.globals import init_db_manager
        db_manager = DBPyMgr(sqlxmlfile)
        db_manager.init()
        init_db_manager(db_manager)

        # 在所有全局变量初始化完成后，创建Flask应用
        from api_services import create_app
        app = create_app()

        flaskIP = configObj_instance.get_flask_ip()
        flaskPort = configObj_instance.get_flask_port()
        debugFlag = configObj_instance.get_flask_debug()

        # 使用更安全的方式启动 Flask
        try:
            app.run(
                host=flaskIP, 
                port=flaskPort, 
                debug=debugFlag, 
                threaded=True, 
                use_reloader=False,
                use_debugger=False,
                passthrough_errors=False
            )
        except Exception as flask_error:
            logging2.error(f"Flask 启动错误: {str(flask_error)}")
            # 尝试清理环境并重试一次
            cleanup_werkzeug_env()
            raise

        # 结束数据库连接
        db_manager.stop()
        # 停止日志模块
        logging2.stop()
    except OSError as e:
        error_msg = str(e)
        if "WERKZEUG_SERVER_FD" in error_msg or "werkzeug" in error_msg.lower():
            logging2.error("Werkzeug server file descriptor error. This usually means the port is already in use or there's a conflicting Flask process.")
        else:
            logging2.error(f"操作系统错误: {error_msg}")
        logging2.error(f"{traceback.format_exc()}")
    except Exception as e:
        logging2.error(f"应用启动失败: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        # 确保清理环境变量
        cleanup_werkzeug_env()
    finally:
        # 最终清理
        cleanup_werkzeug_env()