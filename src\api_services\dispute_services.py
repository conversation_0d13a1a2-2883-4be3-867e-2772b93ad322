import json
import logging2
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from api_services.common.utils import _process_internet_detail_data
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data

# 创建争议诊断服务蓝图
dispute_bp = Blueprint('dispute_services', __name__)

# 非不限量流量争议诊断服务
# curl -X POST http://192.168.161.27:1888/query/non_unlimited_flux_dispute_service -H "Content-Type: application/json" -d '{...}'
@dispute_bp.route('/query/non_unlimited_flux_dispute_service', methods=['POST'])
def non_unlimited_flux_dispute_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        charge_details = json_dict.get('chargeDetails', [])
        offer_accu_detail = json_dict.get('offerAccuDetail', [])
        latn_id = json_dict.get('latn_id')  # 获取latn_id用于后续查询

        # 只返回一条诊断结果，按优先级判断
        final_result = None
        for detail in charge_details:
            start_time_str = detail.get('start_time')
            start_time = datetime.strptime(start_time_str, '%Y%m%d%H%M%S') if start_time_str else None
            valid_accu_found = False
            # 定向流量相关变量
            directional_offer_ids = set()
            directional_accu_found = False
            # 共享包相关统计变量
            total_shared_pkg = 0  # 共享包总数
            invalid_shared_pkg = 0  # 不可用的共享包数
            shared_pkg_msg = ''
            # 校园流量相关变量
            school_flow_found = False
            # 乡镇流量相关变量
            town_flow_attr_values = {}  # 存储乡镇流量包基站组
            town_flow_found = False
            # 本地/省内流量相关变量
            local_province_flow_found = False
            local_province_flow_types = {}  # 存储本地/省内流量类型
            # 查找所有有量本且ACCU_VAL>0的offerinstid
            for offer in offer_accu_detail:
                offer_inst_id = offer.get('offer_inst_id')
                eff_date = offer.get('EFF_DATE')
                exp_date = offer.get('EXP_DATE')
                shared_flag = offer.get('shared_flag', 0)
                update_date = offer.get('update_date')
                is_unlimited = offer.get('is_unlimited', 0)
                offer_id = offer.get('offer_id')
                # 只处理非不限量套餐
                if is_unlimited:
                    continue
                # 校验套餐生失效
                offer_eff = datetime.strptime(eff_date, '%Y-%m-%d %H:%M:%S') if eff_date else None
                offer_exp = datetime.strptime(exp_date, '%Y-%m-%d %H:%M:%S') if exp_date else None
                if not offer_eff or not offer_exp:
                    continue
                # 套餐实例有效期覆盖话单时间
                logging2.debug(f"套餐实例有效期判断: offer_eff={offer_eff}, start_time={start_time}, offer_exp={offer_exp}")
                if offer_eff <= start_time <= offer_exp:
                    # 有量本且ACCU_VAL>0
                    for accu in offer.get('accu_details', []):
                        accu_val = accu.get('ACCU_VAL', 0)
                        accu_eff = accu.get('EFF_DATE')
                        accu_exp = accu.get('EXP_DATE')
                        # 量本ACCU_VAL>0且生失效有效
                        if accu_val > 0 and accu_eff and accu_exp:
                            accu_eff_dt = datetime.strptime(accu_eff, '%Y-%m-%d %H:%M:%S')
                            accu_exp_dt = datetime.strptime(accu_exp, '%Y-%m-%d %H:%M:%S')
                            logging2.debug(f"量本有效期判断: accu_eff_dt={accu_eff_dt}, start_time={start_time}, accu_exp_dt={accu_exp_dt}")
                            if accu_eff_dt <= start_time <= accu_exp_dt:
                                valid_accu_found = True
                                # 定向流量判断，offer_id存在且有效量本
                                if offer_id:
                                    db_result = db_manager_instance.excute_sql('QueryOfferPricingRelMainTag5', params=[offer_id])
                                    if db_result and len(db_result) > 0:
                                        directional_offer_ids.add(offer_id)
                                        directional_accu_found = True
                                # 校园流量判断，offer_id存在且有效量本
                                if offer_id:
                                    school_offer_result = db_manager_instance.excute_sql('QuerySchoolFlowOffer', params=[offer_id])
                                    if school_offer_result and len(school_offer_result) > 0:
                                        school_flow_found = True
                                # 乡镇流量判断
                                if latn_id and offer_inst_id:
                                    # 查询乡镇流量属性
                                    replacements = [['##LATNID##', str(latn_id)]]
                                    town_flow_result = db_manager_instance.excute_sql(
                                        sql_name='QueryTownFlowAttr', 
                                        params=[offer_inst_id], 
                                        lst_replace_code_value=replacements
                                    )
                                    if town_flow_result and len(town_flow_result) > 0:
                                        attr_value = town_flow_result[0][0]
                                        town_flow_attr_values[str(offer_inst_id)] = attr_value
                                        town_flow_found = True
                                # 本地/省内流量判断
                                if offer_id:
                                    local_province_result = db_manager_instance.excute_sql('QueryLocalProvinceFlowOffer', params=[offer_id])
                                    if local_province_result and len(local_province_result) > 0 and local_province_result[0][0] is not None:
                                        local_or_province = local_province_result[0][0]
                                        local_province_flow_types[str(offer_id)] = local_or_province
                                        local_province_flow_found = True
                                # 判断共享包可用性
                                if shared_flag == 1:
                                    total_shared_pkg += 1
                                    # 判断update_date
                                    if update_date:
                                        update_dt = datetime.strptime(update_date, '%Y-%m-%d %H:%M:%S')
                                        logging2.debug(f"共享包可用性判断: start_time={start_time}, update_dt={update_dt}")
                                        if start_time > update_dt:
                                            pass  # 该共享包可用，不计入不可用
                                        else:
                                            invalid_shared_pkg += 1
                                            shared_pkg_msg = '批价正常，共享包有剩余量但不可用'
                                    else:
                                        invalid_shared_pkg += 1
                                        shared_pkg_msg = '批价正常，共享包有剩余量但不可用'
                                # 非共享包不参与共享包判断
                                
            # 按优先级判断结果：1、是否有效套餐实例判断，2、是否共享量本判断，3、是否定向流量判断，4、是否校园流量判断，5、是否乡镇流量判断，6、是否本地/省内流量判断，7、判断是否跨月清单判断
            if not valid_accu_found:
                # 是否有效套餐实例判断：没有找到任何有效量本
                final_result = {'result': '批价正常，流量费用话单产生时，有剩余量的销售品不在有效期内，无法使用剩余量本。'}
                break
            # 是否共享量本判断：只有所有共享包都不可用才返回该结果
            elif total_shared_pkg > 0 and invalid_shared_pkg == total_shared_pkg:
                final_result = {'result': shared_pkg_msg}
                break
            # 是否定向流量判断
            elif directional_accu_found:
                # 用所有定向流量 offer_id 和 chargeDetails 的 rating_group 组合去查SQL
                for offer_id in directional_offer_ids:
                    rating_group = detail.get('rating_group')
                    if offer_id and rating_group:
                        directional_sql_result = db_manager_instance.excute_sql('QueryOfferPricingRelForDirectional', params=[offer_id, rating_group])
                        if directional_sql_result and len(directional_sql_result) > 0:
                            final_result = {'result': '未走上定向流量，人工待查'}
                            break
                if final_result:
                    break
            # 是否校园流量判断
            elif school_flow_found:
                cell_id = detail.get('cell_id')
                if cell_id:
                    school_cell_result = db_manager_instance.excute_sql('QuerySchoolFlowCell', params=[cell_id])
                    if school_cell_result and len(school_cell_result) > 0:
                        final_result = {'result': '未走上校园流量，人工待查'}
                        break
            # 是否乡镇流量判断
            elif town_flow_found:
                cell_id = detail.get('cell_id')
                if cell_id:
                    for offer_inst_id, attr_value in town_flow_attr_values.items():
                        # 查询基站是否在乡镇流量基站组中
                        town_cell_result = db_manager_instance.excute_sql('QueryTownFlowCell', params=[attr_value, cell_id])
                        if town_cell_result and len(town_cell_result) > 0:
                            final_result = {'result': '未走上乡镇流量，人工待查'}
                            break
                if final_result:
                    break
            # 是否本地/省内流量判断
            elif local_province_flow_found:
                roam_flag = detail.get('roam_flag')
                if roam_flag:
                    for offer_id, local_or_province in local_province_flow_types.items():
                        # 根据local_or_province和roam_flag判断
                        if local_or_province == 1 and roam_flag != '0':
                            # 本地流量套餐，但不是本地使用
                            final_result = {'result': '未走上本地流量，人工待查'}
                            break
                        elif local_or_province == 2 and roam_flag not in ['0', '1']:
                            # 省内流量套餐，但不是本地或省内使用
                            final_result = {'result': '未走上省内流量，人工待查'}
                            break
                if final_result:
                    break
            else:
                # 判断是否跨月清单
                billing_cycle = json_dict.get('billing_cycle')
                if start_time and billing_cycle:
                    # 从start_time中提取年月
                    start_time_ym = start_time.strftime('%Y%m')
                    # 判断是否跨月
                    if start_time_ym != billing_cycle:
                        final_result = {'result': '批价正常，可能为跨月清单导致，请核实'}
                        break
        # 循环结束后，若不是以上判断结果，返回默认结果
        if not final_result:
            final_result = {'result': '批价正常，收费时，无可用的免费量，各量本使用情况、溢出费用与溢出流量见详情。'}
            # 需要透传 offerAccuDetail
            return jsonify({
                'status': 'success',
                **final_result,
                'offerAccuDetail': offer_accu_detail,
                'hasOfferAccuDetail': 1  # 标识有offerAccuDetail
            })
        else:
            return jsonify({
                'status': 'success',
                **final_result,
                'hasOfferAccuDetail': 0  # 标识没有offerAccuDetail
            })
    except Exception as e:
        logging2.error(f"非不限量流量争议诊断服务处理请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({'status': 'error', 'message': f'服务器错误: {str(e)}'}), 500


# 上网详单查询接口
@dispute_bp.route('/query/internet_detail_service', methods=['POST'])
def internet_detail_service():
    """
    上网详单查询接口
    调用OPENAPI的清单查询（稽核）接口，返回基站、RG、上网地、开始时间等关键信息
    """
    try:
        # 解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"上网详单查询接口请求参数: {data}")
        json_dict = json.loads(data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')

            # 基础参数校验
            if not billing_nb or not billing_cycle or not latn_id:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、billing_cycle 或 latn_id",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始上网详单查询: billing_nb={billing_nb}, billing_cycle={billing_cycle}, latn_id={latn_id}")

            # 生成startDate和endDate
            start_date = int(billing_cycle + "01")  # 月初第一天
            end_date = int(UnitTool.get_last_day_of_month(billing_cycle))  # 月末最后一天

            # 构建OPENAPI请求参数
            request_payload = {
                "endDate": end_date,
                "operAttrStruct": {
                    "operServiceId": "string",
                    "lanId": int(latn_id),
                    "operPost": 1,
                    "operOrgId": 0,
                    "staffId": 0,
                    "operTime": "string"
                },
                "isDesensitive": "0",
                "svcObjectStruct": {
                    "objValue": billing_nb,
                    "objAttr": "2",
                    "objType": "3",
                    "dataArea": "2"
                },
                "qryType": "3",
                "startDate": start_date
            }

            logging2.info(f"调用OPENAPI清单查询接口，请求参数: {request_payload}")

            # 调用OPENAPI接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("RtBillItem")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到RtBillItem接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            
            try:
                result = UnitTool.send_post_request(url, request_payload)
                logging2.info(f"OPENAPI接口返回结果: {result}")

                if not result:
                    response_list.append({
                        "status": "error",
                        "message": "调用OPENAPI接口失败",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 检查返回结果
                if result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"OPENAPI接口返回错误: {result.get('resultMsg', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理返回数据
                response_data = _process_internet_detail_data(result, billing_nb, billing_cycle)
                response_list.append(response_data)

            except Exception as api_ex:
                logging2.error(f"调用OPENAPI接口时出错: {str(api_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用OPENAPI接口异常: {str(api_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "上网详单查询完成",
            "input_details": response_list
        }
        
        # 检查是否有任何包含Daysum数据的记录，如果有则添加可视化
        has_daysum_data = False
        for response_item in response_list:
            if isinstance(response_item, dict) and response_item.get("status") == "success":
                daysum_data = response_item.get("Daysum", [])
                if daysum_data and len(daysum_data) > 0:
                    has_daysum_data = True
                    break
        
        if has_daysum_data:
            logging2.info("检测到上网详单Daysum数据，添加可视化渲染")
            final_response = add_visualization_data("internet_detail_service", final_response, "上网详单查询", "上网详单按日分析")
        
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理上网详单查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500