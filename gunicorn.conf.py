# -*- coding: utf-8 -*-
"""
Gunicorn配置文件
用于生产环境部署FluxDisputeAgent
"""

import os
import multiprocessing

# 服务器套接字
bind = "************:1888"
backlog = 2048

# 工作进程
workers = multiprocessing.cpu_count() * 2 + 1  # 推荐的worker数量
worker_class = "sync"  # 同步worker类型，适合CPU密集型任务
worker_connections = 1000
max_requests = 1000  # 每个worker处理的最大请求数，防止内存泄漏
max_requests_jitter = 50  # 随机化max_requests，避免所有worker同时重启

# 超时设置
timeout = 30  # worker超时时间
keepalive = 2  # keep-alive连接超时时间
graceful_timeout = 30  # 优雅关闭超时时间

# 用户和组（如果需要）
# user = "www-data"
# group = "www-data"

# 日志
loglevel = "info"
accesslog = "/data/FluxDisputeAgent/log/gunicorn_access.log"
errorlog = "/data/FluxDisputeAgent/log/gunicorn_error.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程命名
proc_name = "FluxDisputeAgent"

# PID文件
pidfile = "/data/FluxDisputeAgent/log/gunicorn.pid"

# 守护进程模式
daemon = False  # 设置为True可以后台运行

# 预加载应用
preload_app = True  # 提高性能，但需要注意共享资源

# 临时目录
tmp_upload_dir = None

# SSL配置（如果需要HTTPS）
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("FluxDisputeAgent服务器正在启动...")

def on_reload(server):
    """重新加载时调用"""
    server.log.info("FluxDisputeAgent服务器正在重新加载...")

def when_ready(server):
    """服务器准备就绪时调用"""
    server.log.info("FluxDisputeAgent服务器已准备就绪")

def worker_int(worker):
    """worker接收到SIGINT信号时调用"""
    worker.log.info("Worker收到SIGINT信号，正在关闭...")

def pre_fork(server, worker):
    """fork worker之前调用"""
    server.log.info(f"Worker {worker.pid} 即将启动")

def post_fork(server, worker):
    """fork worker之后调用"""
    server.log.info(f"Worker {worker.pid} 已启动")

def post_worker_init(worker):
    """worker初始化完成后调用"""
    worker.log.info(f"Worker {worker.pid} 初始化完成")

def worker_abort(worker):
    """worker异常退出时调用"""
    worker.log.error(f"Worker {worker.pid} 异常退出")

def pre_exec(server):
    """重新执行前调用"""
    server.log.info("服务器即将重新执行")

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("FluxDisputeAgent服务器正在关闭...")
