BIN_NAME = FluxDisputeAgent
MAIN_SCRIPT = src/main.py
DIST_DIR = bin
BUILD_DIR = build
SRC_DIR = src

# 加载Python模块
PYTHON_FILES := $(shell find $(SRC_DIR) -name "*.py" -not -path "*/__pycache__/*" -not -name "*test*" -not -name "*debug*")
PYTHON_MODULES := $(patsubst $(SRC_DIR)/%.py,%,$(PYTHON_FILES))
PYTHON_MODULES := $(subst /,.,$(PYTHON_MODULES))
HIDDEN_IMPORTS := $(addprefix --hidden-import=,$(PYTHON_MODULES))

# 加载目录
PACKAGE_DIRS := $(shell find $(SRC_DIR) -name "__init__.py" -exec dirname {} \;)
PACKAGE_NAMES := $(patsubst $(SRC_DIR)/%,%,$(PACKAGE_DIRS))
PACKAGE_NAMES := $(subst /,.,$(PACKAGE_NAMES))
PACKAGE_IMPORTS := $(addprefix --hidden-import=,$(PACKAGE_NAMES))

all:
	@echo "Building $(BIN_NAME)"
	@echo "加载 $(words $(PYTHON_MODULES)) 个模块, $(words $(PACKAGE_NAMES)) 个包"
	pyinstaller --onefile \
				--name $(BIN_NAME) \
				--distpath $(DIST_DIR) \
				--workpath $(BUILD_DIR) \
				--paths=$(SRC_DIR) \
				--add-data="$(SRC_DIR):." \
				$(HIDDEN_IMPORTS) \
				$(PACKAGE_IMPORTS) \
				--hidden-import=flask \
				--hidden-import=xml.etree.ElementTree \
				--hidden-import=xml \
				--hidden-import=json \
				--hidden-import=datetime \
				--hidden-import=traceback \
				--hidden-import=signal \
				--hidden-import=sys \
				--hidden-import=os \
				--hidden-import=threading \
				--hidden-import=queue \
				--hidden-import=time \
				--hidden-import=collections \
				--collect-submodules=api_services \
				$(MAIN_SCRIPT) 
	@echo "Build complete. Executable in $(DIST_DIR)/"

clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(DIST_DIR) $(BUILD_DIR) dist build *.spec
	@echo "Clean complete."

.PHONY: all clean