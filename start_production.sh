#!/bin/bash
# -*- coding: utf-8 -*-
"""
FluxDisputeAgent生产环境启动脚本
支持多种WSGI服务器：Gunicorn、uWSGI、Waitress
"""

# 配置变量
APP_NAME="FluxDisputeAgent"
APP_DIR="/d/工作/天源迪科/SVN_2023/Cloud-Billing-5G-no-oracle/tools/FluxDisputeAgent-chaifen-bianji"
SRC_DIR="$APP_DIR/src"
CONFIG_FILE="$APP_DIR/config/FluxDisputeAgent.jx.cfg.xml"
LOG_DIR="/data/FluxDisputeAgent/log"
PID_FILE="$LOG_DIR/app.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log_info "依赖检查完成"
}

# 停止现有进程
stop_app() {
    log_info "停止现有的$APP_NAME进程..."
    
    # 停止通过PID文件记录的进程
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "停止进程 PID: $PID"
            kill -TERM $PID
            sleep 5
            if ps -p $PID > /dev/null 2>&1; then
                log_warn "进程未正常退出，强制终止"
                kill -KILL $PID
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # 停止所有相关进程
    pkill -f "main.py.*FluxDisputeAgent" 2>/dev/null || true
    pkill -f "gunicorn.*wsgi:app" 2>/dev/null || true
    pkill -f "uwsgi.*wsgi:app" 2>/dev/null || true
    pkill -f "waitress.*wsgi:app" 2>/dev/null || true
    
    log_info "进程停止完成"
}

# 使用Gunicorn启动
start_with_gunicorn() {
    log_info "使用Gunicorn启动$APP_NAME..."
    
    cd "$SRC_DIR"
    
    # 检查Gunicorn是否安装
    if ! python3 -c "import gunicorn" 2>/dev/null; then
        log_error "Gunicorn未安装，请运行: pip install gunicorn"
        exit 1
    fi
    
    # 启动Gunicorn
    nohup python3 -m gunicorn \
        --config ../gunicorn.conf.py \
        --chdir "$SRC_DIR" \
        wsgi:app > "$LOG_DIR/gunicorn_startup.log" 2>&1 &
    
    echo $! > "$PID_FILE"
    log_info "Gunicorn启动完成，PID: $(cat $PID_FILE)"
}

# 使用uWSGI启动
start_with_uwsgi() {
    log_info "使用uWSGI启动$APP_NAME..."
    
    cd "$SRC_DIR"
    
    # 检查uWSGI是否安装
    if ! python3 -c "import uwsgi" 2>/dev/null; then
        log_error "uWSGI未安装，请运行: pip install uwsgi"
        exit 1
    fi
    
    # 启动uWSGI
    nohup python3 -m uwsgi \
        --http ************:1888 \
        --module wsgi:app \
        --processes 4 \
        --threads 2 \
        --master \
        --pidfile "$PID_FILE" \
        --daemonize "$LOG_DIR/uwsgi.log" \
        --chdir "$SRC_DIR" &
    
    log_info "uWSGI启动完成"
}

# 使用Waitress启动
start_with_waitress() {
    log_info "使用Waitress启动$APP_NAME..."
    
    cd "$SRC_DIR"
    
    # 检查Waitress是否安装
    if ! python3 -c "import waitress" 2>/dev/null; then
        log_error "Waitress未安装，请运行: pip install waitress"
        exit 1
    fi
    
    # 启动Waitress
    nohup python3 -m waitress \
        --host=************ \
        --port=1888 \
        --threads=8 \
        wsgi:app > "$LOG_DIR/waitress.log" 2>&1 &
    
    echo $! > "$PID_FILE"
    log_info "Waitress启动完成，PID: $(cat $PID_FILE)"
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "服务正在运行，PID: $PID"
            
            # 检查端口是否监听
            if netstat -tuln | grep -q ":1888 "; then
                log_info "端口1888正在监听"
                
                # 简单的健康检查
                if curl -s -o /dev/null -w "%{http_code}" http://************:1888/ | grep -q "200\|404"; then
                    log_info "服务健康检查通过"
                else
                    log_warn "服务可能存在问题，请检查日志"
                fi
            else
                log_warn "端口1888未在监听"
            fi
        else
            log_error "PID文件存在但进程未运行"
            rm -f "$PID_FILE"
        fi
    else
        log_warn "PID文件不存在，服务可能未启动"
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            check_dependencies
            stop_app
            
            # 根据第二个参数选择WSGI服务器
            case "$2" in
                gunicorn|"")
                    start_with_gunicorn
                    ;;
                uwsgi)
                    start_with_uwsgi
                    ;;
                waitress)
                    start_with_waitress
                    ;;
                *)
                    log_error "不支持的WSGI服务器: $2"
                    log_info "支持的选项: gunicorn, uwsgi, waitress"
                    exit 1
                    ;;
            esac
            
            sleep 3
            check_status
            ;;
        stop)
            stop_app
            ;;
        restart)
            stop_app
            sleep 2
            main start "$2"
            ;;
        status)
            check_status
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status} [gunicorn|uwsgi|waitress]"
            echo "示例:"
            echo "  $0 start gunicorn    # 使用Gunicorn启动"
            echo "  $0 start uwsgi       # 使用uWSGI启动"
            echo "  $0 start waitress    # 使用Waitress启动"
            echo "  $0 stop              # 停止服务"
            echo "  $0 restart gunicorn  # 重启服务"
            echo "  $0 status            # 检查状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
