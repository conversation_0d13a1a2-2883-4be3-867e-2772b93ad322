<?xml version="1.0" standalone="no"?>

<!-- 
该XML文件用于配置数据库连接及相关SQL语句。整体分为两大部分：数据库集配置（<db_set>）和SQL语句集配置（<sql_set>）。

1. **数据库集配置（<db_set>）**：
    - **<db>元素**：每个 <db> 元素代表一个数据库配置，包含数据库名称（通过name属性指定）和数据库类型（通过type属性指定，如'mysql'、'pgsql'、'doris'）。
    - **<master>元素**：【必选】主连接串配置，若主连接串连续失败三次且重连不上，则会启用备连接串。
        - **<param>子元素**：用于设置连接相关参数，如：
            - connNum：连接池大小。
            - host：数据库主机地址。
            - port：数据库端口号。
            - user：数据库用户名。
            - password：数据库密码，明文密文都支持，加解密使用desTool工具。
            - db：要连接的数据库名称。
    - **<slave>元素**：【可选】备连接串配置，若备连接串连续失败三次且重连不上，则会启用主连接串。其内部结构与 <master> 元素一致。

2. **SQL语句集配置（<sql_set>）**：
    - **<sql_cluster>元素**：每个 <sql_cluster> 元素对应一个数据库（通过db属性指定与 <db_set> 中 <db> 的name属性关联），包含一组SQL语句。
    - **<sql>元素**：每个 <sql> 元素代表一条SQL语句，通过name属性指定语句名称，内部为具体的SQL语句内容，其中使用 :v 作为占位符，在实际执行时需替换为具体值。
 -->

<SQL_config>
    <!--数据库集-->
    <db_set>
        <db name='JXDuccBillJx' type='mysql'>
            <!--【必选】主连接串，如果主连接串失败三次，且重连不上，则会用备连接串-->
            <master>
                <param name="connNum">2</param>                    <!--连接池大小-->
                <param name="host">************</param>
                <param name="port">8902</param>
                <param name="user">ducc_bill</param>
                <param name="password">QYm3=zJd</param>                <!--明文密文都支持，加解密用desTool工具-->
                <param name="db">DUCC_BILL_JX</param>
            </master>
        </db>

        <db name='JXDuccPlcaJx' type='mysql'>
            <!--【必选】主连接串，如果主连接串失败三次，且重连不上，则会用备连接串-->
            <master>
                <param name="connNum">2</param>                    <!--连接池大小-->
                <param name="host">************</param>
                <param name="port">8903</param>
                <param name="user">ducc_plca</param>
                <param name="password">9#K4@acN</param>                <!--明文密文都支持，加解密用desTool工具-->
                <param name="db">DUCC_PLCA_JX</param>
            </master>
        </db>
        <db name='JXPgDuccBillJx' type='pgsql'>
            <!--【必选】主连接串，如果主连接串失败三次，且重连不上，则会用备连接串-->
            <master>
                <param name="connNum">2</param>                    <!--连接池大小-->
                <param name="host">************</param>
                <param name="port">18821</param>
                <param name="user">ducc_bill_jx</param>
                <param name="password">Bill%jxdx2023</param>                <!--明文密文都支持，加解密用desTool工具-->
                <param name="db">bssdb</param>
            </master>
        </db>

        <db name='JXODuccBillJx' type='oracle'>
            <master>
                <param name="connNum">2</param>
                <param name="host">***************</param>
                <param name="port">1521</param>
                <param name="user">DUCC_BILL_JX</param>
                <param name="password">Dkjf_pw34$</param>
                <param name="db">bssdb</param>
            </master>
        </db>

        <db name='JXjfdb3' type='oracle'>
            <master>
                <param name="connNum">2</param>
                <param name="host">*************</param>
                <param name="port">1521</param>
                <param name="user">ducc</param>
                <param name="password">Ducc2021</param>
                <param name="db">jfdb3</param>
            </master>
        </db>



    </db_set>

    <sql_set>

        <sql_cluster db='JXPgDuccBillJx'>
          <sql name="QueryFluxOverflowFee">
                SELECT a.source_inst_id, a.flux_over, a.charge,b.name,a.ofr_id,c.offer_name
                FROM dat_real_fee_##LATNID##_##MM## a,acct_item_type b,offer c
                WHERE a.acct_item_type_id=b.acct_item_type_id AND a.ofr_id=c.offer_id
                  AND  a.acct_id = :v
                  AND a.fee_type = 1
                  AND a.flux_over>0
                  AND a.acct_item_type_id IN (261600927,261600200,261600202,261600300,261600302,261600500,261600502,261600305,261600207,261600309,261600507,261600306,261600310)
            </sql>

            <sql name="QueryFluxFunctionFee">
                SELECT source_inst_id, charge, ofr_id,b.name,c.offer_name
                FROM dat_real_fee_##LATNID##_##MM## a,acct_item_type b,offer c
                WHERE a.acct_item_type_id=b.acct_item_type_id AND a.ofr_id=c.offer_id
                AND acct_id = :v
                AND acct_item_type_id IN (262100102)
            </sql>

            <sql name="QueryRealFeeOfferIds">
                SELECT ofr_id FROM dat_real_fee_##LATNID##_##MM## WHERE PRD_INST_ID = :v 
                AND acct_item_type_id IN (261600927,261600200,261600202,261600300,261600302,261600500,261600502,261600305,261600207,261600309,261600507,261600306,261600310) 
                AND flux_over &gt; 0 AND charge &gt; 0
            </sql>

        </sql_cluster>


        <sql_cluster db='JXODuccBillJx'>
            <sql name="QueryRemainAccuInfo">
                SELECT offer_inst_id, accu_type_id, INIT_VAL, ACCU_USED_VAL, ACCU_VAL, EFF_DATE, EXP_DATE
                FROM accumulation_sub_##LATNID##_##MODVAL##
                WHERE offer_inst_id = :1 AND (to_char(EFF_DATE,'yyyymm')=:2 OR to_char(EFF_DATE,'yyyymm')=:3)
                AND to_char(EXP_DATE,'yyyymm')>=:4
            </sql>
            <sql name="QueryOfferTariff">
                 select  offer_id,value_string,pricing_plan_id,pricing_plan_name,tariff_unit,remark from  offer_tariff
                 where offer_id=:1
            </sql>
            
        </sql_cluster>


        <sql_cluster db='JXDuccBillJx'>
            <sql name="QueryLatnIdByBillingNbr">
				SELECT LATN_ID FROM par_gsm_code_all WHERE :v LIKE CONCAT(GSM_CODE, '%%') AND LENGTH(GSM_CODE) >= 7 ORDER BY LENGTH(GSM_CODE) DESC LIMIT 1
			</sql>
            <sql name="QueryFlowOffer">
                select DISTINCT offer_id From offer  a,pricing_combine b where  a.pricing_plan_id=b.PRICING_PLAN_ID
                and b.event_pricing_strategy_id in(
                select event_pricing_strategy_id From  event_pricing_strategy  where  event_type_id in(20232,20134,20136,20126,20121,20124,20231,20119,20230))
                and a.offer_id=:v
            </sql>

            <sql name="QueryPriorityOffer">
                SELECT DISTINCT CONCAT( offer_id, ':',  offer_name, ':',  CASE  WHEN b.CALC_PRIORITY >= 60000 THEN '套内优先级'  ELSE '套外优先级'  END, ':',  b.CALC_PRIORITY    ) AS combined_priority FROM offer a JOIN pricing_combine b ON a.pricing_plan_id = b.PRICING_PLAN_ID WHERE b.event_pricing_strategy_id IN ( SELECT event_pricing_strategy_id  FROM event_pricing_strategy  WHERE event_type_id IN (20232, 20134, 20136, 20126, 20121, 20124, 20231, 20119, 20230)    )    AND a.offer_id = :v
            </sql>


            <sql name="QueryOfferNameByIds">
                SELECT OFFER_ID, OFFER_NAME FROM offer WHERE OFFER_ID = :v  LIMIT 1
            </sql>

            <sql name="QueryAccuTypeNameById">
                SELECT ACCU_TYPE_NAME FROM accu_type WHERE ACCU_TYPE_ID = :v and tariff_unit_id=11
            </sql>

            <sql name="QueryAccuInitRule">
                SELECT a.ACCU_TYPE_ID,a.OFFER_ID FROM accu_init_rule a,accu_type b WHERE b.TARIFF_UNIT_ID=11 and  a.ACCU_TYPE_ID=b.ACCU_TYPE_ID and OFFER_ID = :v
            </sql>

            <sql name="QueryOfferPricingRelMainTag3">
                SELECT OFFER_PRICING_REL_ID, OFFER_ID, MAIN_TAG FROM offer_pricing_rel WHERE OFFER_ID = :v AND MAIN_TAG = 3
            </sql>

            <sql name="QueryOfferPricingRelMainTag5">
                SELECT OFFER_PRICING_REL_ID, OFFER_ID, MAIN_TAG FROM offer_pricing_rel WHERE OFFER_ID = :v AND MAIN_TAG = 5
            </sql>

            <sql name="QueryOfferPricingRelForDirectional">
                SELECT DISTINCT a.offer_id, a.jt_offer_code, a.offer_name, m.value_string
                FROM offer_pricing_rel a, TB_BIL_PRICING_COMBINE_GROUP b, PRICING_COMBINE c, EVENT_PRICING_STRATEGY d, PRICING_SECTION e, PRICING_RULE f, PRICING_REF_VALUE m
                WHERE a.offer_id = b.prod_offer_id
                  AND b.pricing_plan_id = c.pricing_plan_id
                  AND c.event_pricing_strategy_id = d.event_pricing_strategy_id
                  AND d.event_pricing_strategy_id = e.event_pricing_strategy_id
                  AND e.pricing_section_id = f.pricing_section_id
                  AND f.result_ref_value_id = m.ref_value_id
                  AND a.offer_id = :v
                  AND :v LIKE CONCAT(m.value_string, '%%')
            </sql>

            <sql name="QueryDirectionalOfferPricing">
                select DISTINCT e.offer_id,e.offer_name,d.pricing_plan_id,b.value_string From   pricing_rule a,pricing_ref_value b,pricing_section c,pricing_combine d,offer e where  a.result_ref_value_id=b.ref_value_id and a.obj_id in(
select pricing_ref_object_id from  pricing_ref_object where  extern_property_string='RTG')
                and a.pricing_section_id=c.pricing_section_id and c.parent_section_id in(select pricing_section_id from  pricing_section where  section_type_id=4)
                and  c.event_pricing_strategy_id=d.event_pricing_strategy_id  and e.pricing_plan_id=d.pricing_plan_id
                and e.offer_id = :v
            </sql>

            <sql name="QuerySchoolFlowOffer">
                SELECT max(
                    CASE
                        WHEN c.event_type_id IN (20230, 20231, 20232) THEN 1
                        WHEN c.event_type_id IN (20134, 20124, 20119) THEN 2
                    END
                ) AS local_or_province, a.offer_id
                FROM offer a, pricing_combine b, event_pricing_strategy c
                WHERE a.offer_id = :v
                  AND a.pricing_plan_id = b.pricing_plan_id
                  AND b.event_pricing_strategy_id = c.event_pricing_strategy_id
                  AND c.event_pricing_strategy_id IN (
                    SELECT event_pricing_strategy_id
                    FROM pricing_section
                    WHERE zone_item_id = '53' AND section_type_id = 2
                  )
                GROUP BY a.offer_id
            </sql>

            <sql name="QueryCellId">
                SELECT item_ref_value FROM zone_item_value WHERE zone_item_id= :v
            </sql>

            <sql name="QuerySchoolFlowCell">
                SELECT item_ref_value FROM zone_item_value WHERE zone_item_id=53 AND item_ref_value = :v
            </sql>

            <sql name="QueryTownFlowCell">
                SELECT item_ref_value FROM zone_item_value WHERE zone_item_id = :v AND item_ref_value = :v
            </sql>

           <sql name="QueryTownFlowCellAttr">
              select DISTINCT a.offer_id,e.property_define_id From offer a,pricing_combine b, pricing_section c,pricing_ref_value d,pricing_ref_object e   where parent_section_id in(
              select pricing_section_id from  pricing_section where section_type_id=8
                ) and a.pricing_plan_id=b.pricing_plan_id and b.event_pricing_strategy_id=c.event_pricing_strategy_id
                and  c.zone_item_id=d.ref_value_id and  d.pricing_ref_object_id=e.pricing_ref_object_id and a.offer_id= :v
            </sql>


            <sql name="QueryLocalProvinceFlowOffer">
                SELECT max(
                    CASE
                        WHEN c.event_type_id IN (20230, 20231, 20232) THEN 1
                        WHEN c.event_type_id IN (20134, 20124, 20119) THEN 2
                    END
                ) AS local_or_province, a.offer_id
                FROM offer a, pricing_combine b, event_pricing_strategy c
                WHERE a.offer_id = :v AND a.offer_id IN (40019781)
                  AND a.pricing_plan_id = b.pricing_plan_id
                  AND b.event_pricing_strategy_id = c.event_pricing_strategy_id
                GROUP BY a.offer_id
            </sql>

            <sql name="QueryAcctItemOfferIds">
                SELECT ofr_id FROM dat_acct_item_##LATNID##_##BILLINGCYCLE## WHERE PRD_INST_ID = :v 
                AND acct_item_type_id IN (261600927,261600200,261600202,261600300,261600302,261600500,261600502,261600305,261600207,261600309,261600507,261600306,261600310) 
                AND charge &gt; 0
            </sql>

            <sql name="QueryOffer5GMainPackage">
                SELECT offer_id FROM OFFER_EXT_ATTR WHERE attr_id='146' AND offer_id = :v
            </sql>

            <sql name="QueryDailyCumulativeRentFee">
                SELECT offer_id FROM offer WHERE pricing_plan_id IN(
                    SELECT pricing_plan_id FROM pricing_combine WHERE event_pricing_strategy_id IN(
                        SELECT event_pricing_strategy_id FROM pricing_section WHERE event_pricing_strategy_id IN(
                            SELECT event_pricing_strategy_id FROM pricing_section WHERE section_type_id=3
                        )
                        AND pricing_section_id IN(
                            SELECT pricing_section_id FROM tariff WHERE accu_type_id IN(
                                SELECT accu_type_id FROM accu_init_rule WHERE accu_type_id IN(
                                    SELECT accu_type_id FROM accu_type WHERE tariff_unit_id=11
                                ) 
                                AND ratable_cycle_id IN(
                                    SELECT ratable_cycle_id FROM ratable_cycle WHERE ratable_cycle_type_id=7 AND ratable_cycle_count IN(1,2)
                                )
                            )
                        )
                    )
                ) AND offer_id = :v
            </sql>

            <sql name="QueryAlternativeOfferTariff">
                select  DISTINCT  a.offer_id,a.pricing_plan_id,b.pricing_plan_name From   offer a,pricing_plan b,pricing_combine c,event_pricing_strategy d where  a.pricing_plan_id=b.pricing_plan_id and  b.pricing_plan_id=c.pricing_plan_id  and c.event_pricing_strategy_id=d.event_pricing_strategy_id and  d.event_type_id in(20232,20134,20136,20126,20121,20124,20231,20119,20230)  and a.offer_id= :v
            </sql>

        </sql_cluster>

        <sql_cluster db='JXDuccPlcaJx'>
            <sql name="QueryUserProfile">
                SELECT PROD_INST_ID, ACCT_ID, PROD_ID FROM PROD_INST_HIS_##LATNID## WHERE  prod_id='41010200'  and  DATE_FORMAT(exp_date, '%%Y%%m') >= :v  and acc_num = :v LIMIT 1
            </sql>

            <sql name="QueryProdInstRel_a_100800">
                SELECT a_prod_inst_id FROM prod_inst_rel_##LATNID## WHERE (z_prod_inst_id= :v or a_prod_inst_id= :v) and  rel_type='100800'
            </sql>

            <sql name="QueryProdInstRel_100800">
                SELECT a_prod_inst_id,z_prod_inst_id,eff_date,exp_date FROM prod_inst_rel_##LATNID## WHERE a_prod_inst_id= :v and  rel_type='100800'
            </sql>

            <sql name="QueryOfferProdInstRelHisA1Info">
                SELECT offer_inst_id, prod_offer_id, EFF_DATE, EXP_DATE, update_date, OBJ_ID, OBJ_TYPE,ext_offer_inst_id
                FROM offer_prod_inst_rel_##LATNID##
                WHERE rela_obj_inst_type = 'A1' AND prod_inst_id = :v
                AND offer_type not in('20')
                AND eff_date &lt;= STR_TO_DATE(CONCAT(:v, '01'), '%%Y%%m%%d')
                AND (exp_date &gt;= STR_TO_DATE(CONCAT(:v, '01'), '%%Y%%m%%d') OR exp_date IS NULL)
            </sql>

            <sql name="QueryOfferProdInstRelHisA1SharedInfo">
                SELECT obj_id
                FROM offer_prod_inst_rel_##LATNID##
                WHERE offer_inst_id = :v
            </sql>

            <sql name="QueryOfferInstOrderList">
                SELECT offer_inst_id, last_order_item_id
                FROM offer_inst_##LATNID##
                WHERE offer_inst_id IN (:v)
            </sql>

            <sql name="QueryTownFlowAttrValue">
                SELECT attr_value FROM OFFER_INST_ATTR_##LATNID## WHERE offer_inst_id = :v AND attr_id = :v
            </sql>

            <sql name="QueryTownFlowAttr">
                SELECT attr_value FROM OFFER_INST_ATTR_##LATNID## WHERE offer_inst_id = :v AND attr_id = 60019023
            </sql>

            <sql name="QueryAccuTypeGroupMbr">
                SELECT count(*) FROM cc_par_config WHERE config_code = 'UNLIMIT_OFRID' AND value = :v
            </sql>

            <sql name="QueryOfferPricingRelPricingType">
                SELECT PRICING_TYPE FROM offer_pricing_rel WHERE OFFER_ID = :v limit 1
            </sql>
            <sql name="QueryOfferProdInstRelProdInst">
                SELECT PROD_INST_ID
                FROM offer_prod_inst_rel_##LATNID##
                WHERE offer_inst_id = :v
            </sql>
            <sql name="QueryProdInst">
                SELECT acc_num
                FROM prod_inst_##LATNID##
                WHERE prod_inst_id = :v limit 1
            </sql>

            <sql name="QueryAccNumByProdInstId">
                SELECT acc_num
                FROM PROD_INST_HIS_##LATNID##
                WHERE prod_inst_id = :v LIMIT 1
            </sql>

            <sql name="QuerySharedFlowPackageTime">
                SELECT update_date FROM offer_prod_inst_rel_##LATNID## WHERE ext_offer_inst_id = :v AND prod_inst_id = :v
            </sql>

             <sql name="QueryUserTrendingEvents">
                SELECT params, send_content, business_id, create_time 
                FROM tb_ecp_advice_info_bak_##MM## 
                WHERE business_id IN (7783,6522,6523,7796,5425,5424,7618,5703,5321,7676,7799) 
                AND tel = :v
                ORDER BY create_time DESC
            </sql>

        </sql_cluster>

       <sql_cluster db='JXjfdb3'>
            <sql name="QueryChargeDetail">
                SELECT uuid, start_time, rating_group, roam_flag, cell_id, ori_charge, disct_charge
                FROM pcall_1x_##LATNID##_##BILLINGCYCLE##
                WHERE billing_nbr = :v
                ORDER BY start_time
            </sql>
            <sql name="QueryOfferFlowCharge">
                select prd_ofr_id,sum(billing_duration) as billing_duration,sum(ori_charge) as ori_charge,round(sum(ori_charge)/sum(billing_duration),5) as price
                From  pcall_1x_##LATNID##_##BILLINGCYCLE##
                where billing_nbr= :1
                group by prd_ofr_id
            </sql>
           
       </sql_cluster>


    </sql_set>

</SQL_config>