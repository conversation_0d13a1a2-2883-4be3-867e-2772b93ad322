import os

# 全局数据库管理器实例
db_manager_instance = None
# 全局配置实例
config_parser_instance = None
# 全局收费类型fee_type配置
charge_type_id_config = None

def init_db_manager(db_manager):
    global db_manager_instance
    db_manager_instance = db_manager

def init_config_parser(config_parser):
    global config_parser_instance, charge_type_id_config
    config_parser_instance = config_parser
    # 初始化收费类型fee_type配置
    charge_type_id_str = config_parser.get_charge_type_id()
    if charge_type_id_str:
        # 将逗号分隔的字符串转换为整数列表
        charge_type_id_config = [int(x.strip()) for x in charge_type_id_str.split(',') if x.strip()]
    else:
        charge_type_id_config = []

def get_config_path():
    """
    获取配置文件路径，从XML配置文件读取路径
    """
    global config_parser_instance

    # 从XML配置文件读取路径
    if config_parser_instance:
        config_file_path = config_parser_instance.get_http_config_file()
        if config_file_path and os.path.exists(config_file_path):
            return config_file_path

    return None

def get_charge_type_id_list():
    """
    获取收费类型ID列表
    """
    global charge_type_id_config
    return charge_type_id_config if charge_type_id_config else []