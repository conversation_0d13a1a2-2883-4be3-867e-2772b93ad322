import json
import logging
import pandas as pd
import os

import logging2
import traceback
from flask import Flask, request, jsonify
from DCConfigParser import DCConfigParser
from DBPyMgr import DBPyMgr
from UnitTool import UnitTool
from httpClient import HTTPClient
from configLoader import ConfigLoader
from datetime import datetime
import ast,time

# 导入API可视化集成模块
from Visualization import add_visualization_data

# 全局数据库管理器实例
db_manager_instance = None
# 全局配置实例
config_parser_instance = None
# 全局收费类型fee_type配置
charge_type_id_config = None

app = Flask(__name__)

def init_db_manager(db_manager):
    global db_manager_instance
    db_manager_instance = db_manager

def init_config_parser(config_parser):
    global config_parser_instance, charge_type_id_config
    config_parser_instance = config_parser
    # 初始化收费类型fee_type配置
    charge_type_id_str = config_parser.get_charge_type_id()
    if charge_type_id_str:
        # 将逗号分隔的字符串转换为整数列表
        charge_type_id_config = [int(x.strip()) for x in charge_type_id_str.split(',') if x.strip()]
    else:
        charge_type_id_config = []

def get_config_path():
    """
    获取配置文件路径，从XML配置文件读取路径
    """
    global config_parser_instance
    
    # 从XML配置文件读取路径
    if config_parser_instance:
        config_file_path = config_parser_instance.get_http_config_file()
        if config_file_path and os.path.exists(config_file_path):
            return config_file_path
    
    return None

def get_charge_type_id_list():
    """
    获取收费类型ID列表
    """
    global charge_type_id_config
    return charge_type_id_config if charge_type_id_config else []

def _parse_params_content(params_str, business_id):
    """
    根据business_id解析params参数，生成对应的Content内容
    """
    try:
        if not params_str:
            return "参数为空"
        
        # 解析JSON字符串
        params = json.loads(params_str)
        
        if business_id == 7783:  # 套外15G断网
            used = params.get('used', '')
            balance = params.get('balance', '')
            return f"已使用流量{used}，再使用{balance}流量达到套外15G断网"
            
        elif business_id == 6522:  # 政企断网
            return "政企断网"
            
        elif business_id == 6523:  # 语音卡套外超出1G断网
            used = params.get('used', '')
            balance = params.get('balance', '')
            return f"已使用流量{used}，再使用{balance}流量达到语音卡套外超出1G断网"
            
        elif business_id in [7796, 5425, 5424, 7618]:  # 降速提醒
            used = params.get('used', '')
            netspeed = params.get('netspeed', '')
            return f"降速提醒,已使用流量{used}G，降速的速率为{netspeed}"
            
        elif business_id == 5703:  # 不限量阈值提醒
            used = params.get('used', '')
            first_limit = params.get('firstLimit', '')
            return f"不限量阈值提醒,已使用流量{used}G,首次降速流量使用量阈值{first_limit}G"
            
        elif business_id == 7676:  # 阀值提醒普通流量80%和100%
            used = params.get('used', '')
            first_limit = params.get('firstLimit', '')
            return f"阀值提醒提醒,已使用流量{used}G,首次降速流量使用量阈值{first_limit}G"
            
        elif business_id in [5321, 7799]:  # 费用超量提醒
            card_fee = params.get('card_fee', '')
            cost = params.get('cost', '')
            return f"费用超量提醒,{card_fee}总的超出费用{cost}元"
            
        else:
            return "未知业务类型"
            
    except json.JSONDecodeError:
        logging2.error(f"解析params参数失败: {params_str}")
        return "参数解析失败"
    except Exception as e:
        logging2.error(f"解析params内容时出错: {str(e)}")
        return "解析出错"

def _process_fee_bill_data_without_card_relation(api_result, acct_type):
    """
    处理账单费用数据 - 根据ACCT_TYPE过滤数据
    """
    try:
        # 获取原始数据
        acct_name = api_result.get("ACCT_NAME", "")
        fee_info_list = api_result.get("feeInfoList", [])
        
        if not fee_info_list:
            return {
                "status": "success",
                "message": "未查询到账单费用数据",
                "ACCT_NAME": acct_name,
                "feeInfoList": [],
                "totalCount": "0"
            }

        # 根据ACCT_TYPE进行数据过滤
        filtered_fee_list = []
        
        for fee_item in fee_info_list:
            acct_item_type_name = fee_item.get("ACCT_ITEM_TYPE_NAME", "")
            
            # 先过滤AMOUNT为0的记录
            amount = fee_item.get("AMOUNT", "0")
            # 使用字符串比较判断AMOUNT为0的情况，包括"0"和"0.00"
            if amount in ["0", "0.0", "0.00"] or not amount or amount.strip() == "":
                continue  # 跳过AMOUNT为0的记录
            
            # 根据ACCT_TYPE进行过滤
            should_include = False
            
            if acct_type == "1":  # 流量
                if "流量" in acct_item_type_name:
                    should_include = True
            elif acct_type == "2":  # 语音
                if "语音" in acct_item_type_name:
                    should_include = True
            elif acct_type == "3":  # 短信
                if "短信" in acct_item_type_name:
                    should_include = True
            elif acct_type == "4":  # 功能费
                try:
                    # 使用配置的CHARGE_TYPE_ID进行过滤
                    charge_type_ids = get_charge_type_id_list()
                    if charge_type_ids:
                        # 从fee_item中获取CHARGE_TYPE_ID字段
                        charge_type_id = fee_item.get("CHARGE_TYPE_ID")
                        if charge_type_id:
                            charge_type_id_int = int(charge_type_id)
                            if charge_type_id_int in charge_type_ids:
                                should_include = True
                    # 如果没有配置CHARGE_TYPE_ID，则全部过滤，不包含任何功能费记录
                except (ValueError, TypeError):
                    # 如果CHARGE_TYPE_ID不能转换为整数，则过滤掉
                    should_include = False
            elif acct_type == "5":  # 全部
                should_include = True
            
            if should_include:
                filtered_fee_list.append(fee_item)

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "ACCT_NAME": acct_name,
            "feeInfoList": filtered_fee_list,
            "totalCount": str(len(filtered_fee_list))
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理账单费用数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"处理数据时出错: {str(e)}",
            "ACCT_NAME": "",
            "feeInfoList": [],
            "totalCount": "0"
        }


def _process_fee_bill_data(api_result, acct_type, latn_id, prod_inst_id):
    """
    处理账单费用数据
    根据ACCT_TYPE过滤数据，并验证主副卡关系
    """
    try:
        global db_manager_instance
        
        # 获取原始数据
        acct_name = api_result.get("ACCT_NAME", "")
        fee_info_list = api_result.get("feeInfoList", [])
        
        if not fee_info_list:
            return {
                "status": "success",
                "message": "未查询到账单费用数据",
                "ACCT_NAME": acct_name,
                "feeInfoList": [],
                "totalCount": "0"
            }

        # 首先根据ACCT_TYPE进行数据过滤
        filtered_fee_list = []
        
        for fee_item in fee_info_list:
            acct_item_type_name = fee_item.get("ACCT_ITEM_TYPE_NAME", "")
            
            # 根据ACCT_TYPE进行过滤
            should_include = False
            
            if acct_type == "1":  # 流量
                if "流量" in acct_item_type_name:
                    should_include = True
            elif acct_type == "2":  # 语音
                if "语音" in acct_item_type_name:
                    should_include = True
            elif acct_type == "3":  # 短信
                if "短信" in acct_item_type_name:
                    should_include = True
            elif acct_type == "4":  # 功能费
                try:
                    # 使用配置的CHARGE_TYPE_ID进行过滤
                    charge_type_ids = get_charge_type_id_list()
                    if charge_type_ids:
                        # 从fee_item中获取CHARGE_TYPE_ID字段
                        charge_type_id = fee_item.get("CHARGE_TYPE_ID")
                        if charge_type_id:
                            charge_type_id_int = int(charge_type_id)
                            if charge_type_id_int in charge_type_ids:
                                should_include = True
                    # 如果没有配置CHARGE_TYPE_ID，则全部过滤，不包含任何功能费记录
                except (ValueError, TypeError):
                    # 如果CHARGE_TYPE_ID不能转换为整数，则过滤掉
                    should_include = False
            elif acct_type == "5":  # 全部
                should_include = True
            
            if should_include:
                filtered_fee_list.append(fee_item)

        # 查询主副卡关系，收集所有有效的产品实例ID
        valid_prod_inst_ids = set()
        replacements = [['##LATNID##', str(latn_id)]]
        
        try:
            # 首先查询QueryProdInstRel_a_100800获取a_prod_inst_id
            params = [prod_inst_id, prod_inst_id]
            a_rows = db_manager_instance.excute_sql(
                sql_name='QueryProdInstRel_a_100800',
                params=params,
                lst_replace_code_value=replacements
            )
            
            if a_rows:
                # 如果查询到数据，收集所有的a_prod_inst_id
                for row in a_rows:
                    a_prod_inst_id = row[0] if row else None
                    if a_prod_inst_id:
                        valid_prod_inst_ids.add(str(a_prod_inst_id))
                        
                        # 再查询QueryProdInstRel_100800验证主副卡关系
                        params2 = [a_prod_inst_id]
                        a1_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInstRel_100800',
                            params=params2,
                            lst_replace_code_value=replacements
                        )
                        
                        if a1_rows:
                            # 如果QueryProdInstRel_100800有数据，收集所有的a_prod_inst_id和z_prod_inst_id
                            for a1_row in a1_rows:
                                if len(a1_row) >= 2:
                                    main_prod_inst_id = a1_row[0]  # a_prod_inst_id
                                    sub_prod_inst_id = a1_row[1]   # z_prod_inst_id
                                    if main_prod_inst_id:
                                        valid_prod_inst_ids.add(str(main_prod_inst_id))
                                    if sub_prod_inst_id:
                                        valid_prod_inst_ids.add(str(sub_prod_inst_id))
            else:
                # 如果QueryProdInstRel_a_100800查不到数据，则直接使用传入的prod_inst_id
                valid_prod_inst_ids.add(str(prod_inst_id))
                logging2.debug(f"QueryProdInstRel_a_100800查不到数据，直接使用当前PROD_INST_ID: {prod_inst_id}")
        
        except Exception as e:
            logging2.error(f"查询主副卡关系时出错: {str(e)}")
            # 如果查询出错，则只使用传入的prod_inst_id
            valid_prod_inst_ids.add(str(prod_inst_id))

        logging2.info(f"收集到的有效产品实例ID集合: {valid_prod_inst_ids}")

        # 根据主副卡关系过滤数据
        final_filtered_fee_list = []
        for fee_item in filtered_fee_list:
            prod_inst_id_in_fee = fee_item.get("PROD_INST_ID", "")
            
            # 如果没有PROD_INST_ID，则过滤掉这些数据，不添加到结果中
            if not prod_inst_id_in_fee:
                logging2.debug(f"过滤掉没有PROD_INST_ID的数据: {fee_item.get('ACCT_ITEM_TYPE_NAME', '')}")
                continue
            
            # 检查当前fee_item的PROD_INST_ID是否在有效的产品实例ID集合中
            if str(prod_inst_id_in_fee) in valid_prod_inst_ids:
                final_filtered_fee_list.append(fee_item)
                logging2.debug(f"匹配成功: PROD_INST_ID={prod_inst_id_in_fee} 在有效的产品实例ID集合中")
            else:
                logging2.debug(f"过滤掉: PROD_INST_ID={prod_inst_id_in_fee} 不在有效的产品实例ID集合中")

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "ACCT_NAME": acct_name,
            "feeInfoList": final_filtered_fee_list,
            "totalCount": str(len(final_filtered_fee_list))
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理账单费用数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"处理数据时出错: {str(e)}",
            "ACCT_NAME": "",
            "feeInfoList": [],
            "totalCount": "0"
        }


def filter_accu_data(json_data, accu_type_id_set):
    """
    解析JSON报文并按offerId分组筛选指定accuTypeId的数据

    参数:
        json_data: JSON格式的字符串数据
        accu_type_id_set: 需要筛选的accuTypeId集合

    返回:
        按offerId分组筛选后的结果列表
    """
    # 解析JSON数据
    data = json.loads(json_data)

    # 用于存储按offerId分组的结果
    offer_results = {}

    # 遍历每个优惠实例信息
    for offer_info in data.get("detailOfferInstInfo", []):
        offer_id = offer_info.get("offerId")
        filtered_users = []

        # 遍历每个用户账户信息
        for user_acc in offer_info.get("accuQryUserList", []):
            # 检查accuTypeId是否在指定集合中
            if user_acc.get("accuTypeId") in accu_type_id_set:
                # 提取符合条件的用户账户信息
                filtered_users.append({
                    "accNbr": user_acc.get("accNbr"),
                    "accuTypeId": user_acc.get("accuTypeId"),
                    "accuTypeAttr": user_acc.get("accuTypeAttr"),
                    "servTypeId": user_acc.get("servTypeId"),
                    "destinationAttr": user_acc.get("destinationAttr"),
                    "usageAmount": user_acc.get("usageAmount"),
                    "unitTypeId": user_acc.get("unitTypeId"),
                    "accuId": user_acc.get("accuId")
                })

        # 如果该offer下有符合条件的用户，添加到结果中
        if filtered_users:
            offer_results[offer_id] = filtered_users

    # 将结果转换为所需的格式
    result_list = [{"offerId": offer_id, "accuQryUserList": users}
                   for offer_id, users in offer_results.items()]

    return result_list

def _process_internet_detail_data(api_result, billing_nb, billing_cycle):
    """
    处理OPENAPI返回的上网详单数据
    1. 按时间排序，取第一条费用>0的记录的关键信息
    2. 按日统计流量和费用
    """
    try:
        data_bill_items = api_result.get("dataBillItems", [])
        
        if not data_bill_items:
            return {
                "status": "success",
                "message": "未查询到上网详单数据",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "startTime": "",
                "callingArea": "",
                "rg": "",
                "bsid": "",
                "Daysum": []
            }

        # 按startTime排序（最早到最晚）
        sorted_items = sorted(data_bill_items, key=lambda x: x.get("startTime", ""))
        
        # 查找第一条费用>0的记录
        first_fee_record = None
        for item in sorted_items:
            try:
                fee_value = int(item.get("fee", 0)) if item.get("fee", 0) != "" else 0
            except (ValueError, TypeError):
                fee_value = 0
            
            if fee_value > 0:
                first_fee_record = {
                    "startTime": item.get("startTime", ""),
                    "callingArea": item.get("callingArea", ""),
                    "rg": item.get("rg", ""),
                    "bsid": item.get("bsid", "")
                }
                break
        
        # 如果没有找到费用>0的记录，使用第一条记录
        if not first_fee_record and sorted_items:
            first_item = sorted_items[0]
            first_fee_record = {
                "startTime": first_item.get("startTime", ""),
                "callingArea": first_item.get("callingArea", ""),
                "rg": first_item.get("rg", ""),
                "bsid": first_item.get("bsid", "")
            }

        # 按日统计流量和费用
        daily_summary = {}
        for item in data_bill_items:
            start_time = item.get("startTime", "")
            if len(start_time) >= 8:
                # 提取日期部分（YYYYMMDD）
                day_key = start_time[:8]
                
                if day_key not in daily_summary:
                    daily_summary[day_key] = {
                        "fee": 0,
                        "volume": 0
                    }
                
                # 获取当前项的费用和流量，转换为数值类型
                try:
                    item_fee = int(item.get("fee", 0)) if item.get("fee", 0) != "" else 0
                except (ValueError, TypeError):
                    item_fee = 0
                
                try:
                    item_volume = int(item.get("volume", 0)) if item.get("volume", 0) != "" else 0
                except (ValueError, TypeError):
                    item_volume = 0
                
                # 累加费用
                daily_summary[day_key]["fee"] += item_fee
                
                # 只有当费用大于0时才累加流量
                if item_fee > 0:
                    daily_summary[day_key]["volume"] += item_volume

        # 转换为列表格式并按日期排序，过滤掉费用为0的日期
        daysum_list = []
        for day_key in sorted(daily_summary.keys()):
            day_fee = daily_summary[day_key]["fee"]
            # 只有费用大于0的日期才添加到结果中
            if day_fee > 0:
                daysum_list.append({
                    "Daytime": day_key,
                    "fee": day_fee,
                    "volume": daily_summary[day_key]["volume"]
                })

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "startTime": first_fee_record.get("startTime", "") if first_fee_record else "",
            "callingArea": first_fee_record.get("callingArea", "") if first_fee_record else "",
            "rg": first_fee_record.get("rg", "") if first_fee_record else "",
            "bsid": first_fee_record.get("bsid", "") if first_fee_record else "",
            "Daysum": daysum_list
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理上网详单数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"未查到上网详单数据",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "startTime": "",
            "callingArea": "",
            "rg": "",
            "bsid": "",
            "Daysum": []
        }

def query_remain_accu_info(latn_id, offer_inst_id, accu_type_id, obj_type, obj_id):
    """
    查询量本分表accumulation_sub_[latn_id]_[offer_inst_id%10]，返回量本信息
    """
    global db_manager_instance
    # 动态表名替换
    mod_raw = int(offer_inst_id) % 10
    mod_adjusted = mod_raw if mod_raw != 0 else 10
    mod_val = f'{mod_adjusted:02d}'
    lst_replace_code_value = [['##LATNID##', str(latn_id)], ['##MODVAL##', mod_val]]
    params = [offer_inst_id, accu_type_id, obj_type, obj_id]
    try:
        rows = db_manager_instance.excute_sql('QueryRemainAccuInfo', params=params, lst_replace_code_value=lst_replace_code_value)
        return rows
    except Exception as e:
        logging2.error(f"QueryRemainAccuInfo error: {e}")
        return []
    
def query_accu_type_name(accu_type_id):
    """
    通过accu_type_id查询accu_type表，获取ACCU_TYPE_NAME
    """
    global db_manager_instance
    try:
        rows = db_manager_instance.excute_sql('QueryAccuTypeNameById', params=[accu_type_id])
        if rows and len(rows) > 0:
            return rows[0][0]
        else:
            return None
    except Exception as e:
        logging2.error(f"QueryAccuTypeNameById error: {e}")
        return None
    
def query_user_profile_internal(billing_nb, latn_id, billing_cycle):
    """
    用户资料查询
    """
    try:
        replacements = [['##LATNID##', str(latn_id)]]
        params = [billing_cycle, billing_nb]
        
        result = db_manager_instance.excute_sql(
            sql_name='QueryUserProfile',
            params=params,
            lst_replace_code_value=replacements
        )
        
        if not result or len(result[0]) != 3:
            return {"Is_user_profile": 0}
            
        prod_inst_id, acct_id, prod_id = result[0]
        
        return {
            "Is_user_profile": 1,
            "PROD_INST_ID": prod_inst_id,
            "ACCT_ID": acct_id,
            "PROD_ID": prod_id
        }
    except Exception as e:
        logging2.error(f"用户资料查询出错: {str(e)}")
        return {"Is_user_profile": 0}


def query_package_usage_internal(prod_inst_id, latn_id, billing_cycle, acct_id, billing_nb):
    """
    套餐使用明细查询
    """
    try:
        result_data = {"offerAccuDetail": []}
        
        # 获取配置文件路径和URL
        config_path = get_config_path()
        if not config_path:
            logging2.error("找不到配置文件config.json")
            return result_data
            
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("AccuUseQryCenter")
        if not url:
            logging2.error("配置中未找到AccuUseQryCenter接口URL")
            return result_data

        # 构建openapi请求数据
        request_payload = {
            "operAttrStruct": {
                "lanId": 0,
                "operOrgId": 0,
                "operPost": 0,
                "operServiceId": "string",
                "operTime": "string",
                "staffId": 0
            },
            "accNbr": billing_nb,
            "destinationAttr": "2",
            "billingCycle": int(billing_cycle),
            "offerId": 0,
            "qryType": 2,
            "offerInstStr": 0,
            "acctId": 0
        }

        logging2.info(f"调用AccuUseQryCenter接口，请求参数: {request_payload}")

        # 发送请求并处理结果
        try:
            from UnitTool import UnitTool
            result = UnitTool.send_post_request(url, request_payload)
            logging2.info(f"AccuUseQryCenter接口返回结果: {result}")

            if not result or result.get("resultCode") != "0":
                logging2.error(f"AccuUseQryCenter接口调用失败: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")
                return result_data

            # 处理返回数据
            clean_pkgs = []
            for offer_info in result.get("offerInstInfo", []):
                offer_id = offer_info.get("offerId")
                offer_name = offer_info.get("offerName")
                offer_inst_id = offer_info.get("offerInstId")
                eff_date = offer_info.get("EFF_DATE")
                exp_date = offer_info.get("EXP_DATE")
                accu_qry_list = offer_info.get("accuQryList", [])
                
                # 只处理unitTypeId为3的数据（流量数据）
                filtered_accu_list = [accu for accu in accu_qry_list if str(accu.get("unitTypeId", "")) == "3"]
                
                if not filtered_accu_list:
                    continue  # 如果没有流量数据，跳过这个套餐
                
                # 判断是否为共享套餐：ownerType为2代表共享
                shared_owner_ids = set()
                is_shared = False
                total_init_val = 0
                total_used_val = 0
                total_remain_val = 0
                
                for accu in filtered_accu_list:
                    owner_type = accu.get("ownerType")
                    owner_id = accu.get("ownerId")
                    init_val = accu.get("initVal", 0)
                    usage_val = accu.get("usageVal", 0)
                    accu_val = accu.get("accuVal", 0)
                    
                    if owner_type == 2:  # 共享类型
                        is_shared = True
                        if owner_id:
                            shared_owner_ids.add(owner_id)
                    
                    # 累加流量数据
                    total_init_val += init_val
                    total_used_val += usage_val
                    total_remain_val += accu_val
                
                # 格式化日期
                try:
                    if eff_date and len(str(eff_date)) == 14:  # YYYYMMDDHHMMSS格式
                        eff_date_formatted = datetime.strptime(str(eff_date), '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        eff_date_formatted = str(eff_date) if eff_date else ""
                        
                    if exp_date and len(str(exp_date)) == 14:  # YYYYMMDDHHMMSS格式
                        exp_date_formatted = datetime.strptime(str(exp_date), '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        exp_date_formatted = str(exp_date) if exp_date else ""
                except:
                    eff_date_formatted = str(eff_date) if eff_date else ""
                    exp_date_formatted = str(exp_date) if exp_date else ""
                
                                 # 构建套餐数据
                clean_pkg = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": offer_id,
                    "offerName": offer_name,
                    "EFF_DATE": eff_date_formatted,
                    "EXP_DATE": exp_date_formatted,
                    "shared_flag": 1 if is_shared else 0,
                    "shared_obj_ids": list(shared_owner_ids),
                    "total_traffic": round(total_init_val / 1024 / 1024, 2),  # 转换为GB
                    "used_traffic": round(total_used_val / 1024 / 1024, 2),   # 转换为GB
                    "remain_traffic": round(total_remain_val / 1024 / 1024, 2), # 转换为GB
                    "usage_rate": round((total_used_val / total_init_val) * 100, 1) if total_init_val > 0 else 0
                }
                clean_pkgs.append(clean_pkg)
            
            result_data["offerAccuDetail"] = clean_pkgs
            return result_data

        except Exception as req_ex:
            logging2.error(f"调用AccuUseQryCenter接口时出错: {str(req_ex)}")
            return result_data
        
    except Exception as e:
        logging2.error(f"套餐使用明细查询出错: {str(e)}")
        return {"offerAccuDetail": []}

def query_billing_info_internal(acct_id, billing_cycle, latn_id):
    """
    账单信息查询方法（查询4种费用类型）
    """
    try:
        if len(billing_cycle) != 6:
            return {
                "Is_charge_detail": 0, 
                "basic_package_fee": 0.00,
                "flux_booster_fee": 0.00, 
                "night_flux_fee": 0.00,
                "overflow_flux_fee": 0.00
            }
        
        # 使用通用费用查询方法
        fee_configs = [
            ('QueryBasicPackageFee', 'basic_package_fee', '套餐基础费用'),
            ('QueryFluxBoosterFee', 'flux_booster_fee', '流量加油包费用'),
            ('QueryNightFluxFee', 'night_flux_fee', '夜间流量包费用'),
            ('QueryOverflowFluxFee', 'overflow_flux_fee', '套餐外流量费用')
        ]
        
        result = {"Is_charge_detail": 0}
        has_any_fee = False
        
        for sql_name, field_name, description in fee_configs:
            fee_amount = query_fee_by_type(acct_id, billing_cycle, latn_id, sql_name, description)
            result[field_name] = fee_amount
            if fee_amount > 0:
                has_any_fee = True
        
        result["Is_charge_detail"] = 1 if has_any_fee else 0
        return result
        
    except Exception as e:
        logging2.error(f"账单信息查询出错: {str(e)}")
        return {
            "Is_charge_detail": 0,
            "basic_package_fee": 0.00,
            "flux_booster_fee": 0.00,
            "night_flux_fee": 0.00,
            "overflow_flux_fee": 0.00
        }

def query_fee_by_type(acct_id, billing_cycle, latn_id, sql_name, description="费用"):
    """
    通用费用查询
    """
    try:
        month_mm = billing_cycle[4:]
        replacements = [
            ['##LATNID##', str(latn_id)],
            ['##MM##', month_mm]
        ]
        params = [acct_id]
        
        result = db_manager_instance.excute_sql(
            sql_name=sql_name,
            params=params,
            lst_replace_code_value=replacements
        )
        return round(float(result[0][0] or 0), 2) if result and result[0][0] else 0.00
    except Exception as e:
        logging2.error(f"查询{description}出错: {e}")
        return 0.00

def get_latn_id_if_missing(billing_nb, latn_id):
    """
    获取本地网ID
    """
    if latn_id:
        return latn_id, None
        
    try:
        result = db_manager_instance.excute_sql(
            sql_name='QueryLatnIdByBillingNbr', 
            params=[billing_nb]
        )
        if result and result[0] and result[0][0]:
            return result[0][0], None
        else:
            return None, "本地网查询失败"
    except Exception as e:
        logging2.error(f"查询本地网时出错: {e}")
        return None, "本地网查询异常"

# 判断账期1+1接口
# curl -X POST http://**************:1888/query/is_billing_cycle_service -H "Content-Type: application/json" -d '{"billing_nbr": "123456", "billing_cycle": "202504", "latn_id": "791"}'
@app.route('/query/is_billing_cycle_service', methods=['POST'])
def is_billing_cycle_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        billing_cycle = json_dict.get('billing_cycle', '')
        if not billing_cycle or (len(billing_cycle) != 6 and len(billing_cycle) != 2):
            logging2.warning("缺少或格式错误的参数 billing_cycle")
            response_data = {"status": "success", "Is_billing_cycle": 0, "message": "缺少或无效的参数 billing_cycle"}
            # return jsonify(add_visualization_data("is_billing_cycle_service", response_data, "账期1+1判断查询", "参数错误"))
            return response_data

        # 获取当前年月和上一个月
        now = datetime.now()
        current_ym = now.strftime('%Y%m')
        #当账期只有月份自动补充当前年
        if len(billing_cycle)==2:
            current_year = now.strftime('%Y')
            billing_cycle = current_year + billing_cycle
        # 计算上一个月
        if now.month == 1:
            prev_ym = f"{now.year-1}12"
        else:
            prev_ym = f"{now.year}{now.month-1:02d}"
        logging2.info(f"当前年月: {current_ym}, 上一个月: {prev_ym}, 传入账期: {billing_cycle}")

        if billing_cycle in (current_ym, prev_ym):
            # 账期在允许范围内
            response_data = {"status": "success", "Is_billing_cycle": 1, "message": f"账期 {billing_cycle} 在允许范围内"}
            # return jsonify(add_visualization_data("is_billing_cycle_service", response_data, "账期1+1判断查询", f"账期在允许范围内"))
            return response_data
        else:
            # 账期超出范围
            logging2.warning(f"账期 {billing_cycle} 超出允许范围 ({current_ym}, {prev_ym})")
            response_data = {"status": "success", "Is_billing_cycle": 0, "message": "超出流量费用争议诊断账期范围，请核实。"}
            # return jsonify(add_visualization_data("is_billing_cycle_service", response_data, "账期1+1判断查询", f"账期超出允许范围，当前只支持：{current_ym}、{prev_ym}"))
            return response_data

    except Exception as e:
        logging2.error(f"账期1+1接口处理请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500


# 号段表本地网查询接口
# curl -X POST http://**************:1888/query/latn_id_service -H "Content-Type: application/json" -d '{"billing_nb": "13787876554", "latn_id": "791"}'
@app.route('/query/latn_id_service', methods=['POST'])
def latn_id_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details = []
        for row in json_dict:
            # latn_id = row.get('latn_id', '')
            billing_nb = row.get('billing_nb', '') # 获取 billing_nb
            billing_cycle = row.get('billing_cycle', '') # 获取 billing_cycle
            disputeType = row.get('disputeType', '') # 获取 disputeType
            charge=row.get('charge', '') # 获取 charge
            # 获取当前年月和上一个月
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            # 当账期只有月份自动补充当前年
            if len(billing_cycle) == 2:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + billing_cycle
            # 检查传入的 latn_id 是否有值
            # if latn_id:
            #     logging2.info(f"直接使用传入的 latn_id: {latn_id}")
            #     response_data = {"status": "success", "Is_latn_id": 1, "latn_id": latn_id}
            #     # 如果请求中存在，则添加额外字段
            #     if billing_nb:
            #         response_data['billing_nb'] = billing_nb
            #     if billing_cycle:
            #         response_data['billing_cycle'] = billing_cycle
            #     if disputeType:
            #         response_data['disputeType'] = disputeType
            #     if charge:
            #         response_data['charge'] = charge
            #     input_details.append(response_data)

            # 如果 latn_id 为空，则尝试从 billing_nb 查询
            if not billing_nb:
                logging2.warning("缺少必要参数 latn_id 或 billing_nb")
                # Is_latn_id 为 0，不透传额外字段
                response_data = {"status": "success", "Is_latn_id": 0,"message": "缺少必要参数 latn_id 或 billing_nb"}
                # return jsonify(add_visualization_data("latn_id_service", response_data, "号段表本地网查询", "参数错误"))
                return response_data

            logging2.info(f"尝试通过 billing_nb 查询 latn_id: {billing_nb}")
            params = [billing_nb]
            result = db_manager_instance.excute_sql(sql_name='QueryLatnIdByBillingNbr', params=params)

        # 处理查询结果
            if result and result[0] and result[0][0]:
                found_latn_id = result[0][0]
                logging2.info(f"查询成功，找到 latn_id: {found_latn_id}")
                response_data = {"status": "success", "Is_latn_id": 1, "latn_id": found_latn_id}
                # 如果请求中存在，则添加额外字段
                response_data['billing_nb'] = billing_nb
                if billing_cycle:
                    response_data['billing_cycle'] = billing_cycle
                if disputeType:
                    response_data['disputeType'] = disputeType
                if charge:
                    response_data['charge'] = charge
                input_details.append(response_data)
            else:
                logging2.warning(f"未能通过 billing_nb {billing_nb} 查询到 latn_id")
                response_data = {"status": "success", "Is_latn_id": 0, "message": "本地网查询失败"}
                # return jsonify(add_visualization_data("latn_id_service", response_data, "号段表本地网查询", f"号码未查询到对应本地网ID"))
                return response_data
        logging2.debug(f" input_details: {input_details}")
        response={"status": "success","Is_latn_id": 1,"input_details":input_details}
        # return jsonify(add_visualization_data("latn_id_service", response, "号段表本地网查询", f"号码查到对应本地网ID"))
        return response

    except Exception as e:
        logging2.error(f"号段表本地网查询接口处理请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500


# 查询用户资料服务
# curl -X POST http://**************:1888/query/user_profile_service -H "Content-Type: application/json" -d '{"billing_nb": "13345448334", "billing_cycle": "202504", "latn_id":"888", "disputeType": "1"}'
@app.route('/query/user_profile_service', methods=['POST'])
def user_profile_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        for  row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            # disputeType = row.get('disputeType')
            charge =row.get('charge')
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            if len(billing_cycle) == 2:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + billing_cycle
            if len(billing_cycle) == 1:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + "0" + billing_cycle

            if not billing_nb or not latn_id:
                logging2.warning("缺少必要参数 billing_nb 或 latn_id")
                response_data = {"status": "success", "message": "缺少必要参数 billing_nb 或 latn_id", "Is_user_profile": 0}
                # return jsonify(add_visualization_data("user_profile_service", response_data, "用户资料查询", "参数错误"))
                return response_data

            logging2.info(f"查询用户资料: billing_nb={billing_nb}, latn_id={latn_id}")

            # 替换本地网
            replacements = [['##LATNID##', str(latn_id)]]
            params = [billing_cycle, billing_nb]

            # 执行SQL查询
            result = db_manager_instance.excute_sql(
                sql_name='QueryUserProfile',
                params=params,
                lst_replace_code_value=replacements
            )

            if not result:
                logging2.warning(f"未找到用户资料: billing_nb={billing_nb}, latn_id={latn_id}")
                input_details.append({"status": "success", "message": "未找到用户资料", "Is_user_profile": 0})

            # 假设查询结果是 [(prod_inst_id, acct_id, prod_id)]
            if len(result) > 0 and len(result[0]) == 3:
                prod_inst_id, acct_id, prod_id = result[0]
                logging2.info(f"查询成功: PROD_INST_ID={prod_inst_id}, ACCT_ID={acct_id}, PROD_ID={prod_id}")

                # # 检查 PROD_ID
                # if str(prod_id) != '41010200':
                #     logging2.warning(f"非手机产品: PROD_ID={prod_id}")
                #     input_details.append({"status": "success", "message": "争议号码非手机号，请核实。", "Is_user_profile": 0})

                # 成功响应
                response_data = {
                    # "status": "success",
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": prod_id,
                    "charge":charge,
                    "Is_user_profile": 1
                }
                input_details.append(response_data)
            else:
                logging2.error(f"查询结果格式不正确: {result}")
                input_details.append({"status": "success", "message": "查询用户资料失败，结果格式不正确", "Is_user_profile": 0})
        # return jsonify({"status": "success","Is_user_profile": 1, "input_details": input_details})
        return jsonify(add_visualization_data("user_profile_service",{"input_details": input_details},"查询用户资料",""))
    except Exception as e:
        logging2.error(f"处理用户资料查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500
    

@app.route('/query/user_info', methods=['POST'])
def user_info():
    try:
        # 获取并解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)

        # 结果存储
        input_details = []
        prod_inst_id_set = set()  # 使用集合替代列表，提高查找效率

        # 遍历输入明细
        for row in json_dict.get('input_details', []):
            # 提取公共参数
            acct_id = row.get('ACCT_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            charge = row.get('charge')

            # 查询相关产品实例
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, prod_inst_id]
            a_rows = db_manager_instance.excute_sql(
                sql_name='QueryProdInstRel_a_100800',
                params=params,
                lst_replace_code_value=replacements
            )

            # 处理查询结果
            for a_prod_inst_id in a_rows:
                # 去重逻辑优化
                if a_prod_inst_id in prod_inst_id_set:
                    continue
                # 构建响应数据
                response_data = {
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": row.get('PROD_ID'),  # 使用 row.get() 避免未定义变量
                    "charge": charge,
                    "Is_user_profile": 1
                }
                # 添加到结果集
                input_details.append(response_data)
                prod_inst_id_set.add(a_prod_inst_id)
            logging2.info(f"prod_inst_id_set:{prod_inst_id_set.__len__()}")
            if prod_inst_id_set.__len__()==0:
                # 构建响应数据
                response_data = {
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": row.get('PROD_ID'),  # 使用 row.get() 避免未定义变量
                    "charge": charge,
                    "Is_user_profile": 1
                }
                # 添加到结果集
                input_details.append(response_data)

        logging2.info(f"user_info:{input_details}")
        return {"status": "success", "input_details": input_details}
    except Exception as e:
        logging2.error(f"处理用户资料查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500



# 查询流量溢出费用账单服务
# curl -X POST http://**************:1888/query/flux_overflow_fee_service -H "Content-Type: application/json" -d '{"ACCT_ID":602734955,"Is_user_profile":1,"PROD_ID":41010200,"PROD_INST_ID":47508849346,"billing_cycle":"202504","billing_nb":"13755548334","disputeType":"1","latn_id":"790","status":"success"}'
@app.route('/query/flux_overflow_fee_service', methods=['POST'])
def flux_overflow_fee_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        for  row in json_dict.get('input_details'):
            disputeType = row.get('disputeType')
            acct_id = row.get('ACCT_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            ycharge=row.get('charge')


            # disputeType=1 流量费用争议逻辑
            if str(disputeType) == '11111':
                # 校验必要参数
                if not acct_id or not billing_cycle or not latn_id:
                    logging2.warning("缺少必要参数 ACCT_ID, billing_cycle 或 latn_id")
                    input_details.append({"status": "success", "message": "缺少必要参数 ACCT_ID, billing_cycle 或 latn_id", "Is_flux_fee": 0})

                # 提取月份 MM
                if len(billing_cycle) != 6:
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    response_data = {"status": "success", "message": "无效的 billing_cycle 格式", "Is_flux_fee": 0}
                    # return jsonify(add_visualization_data("flux_overflow_fee_service", response_data, "流量溢出费用查询", f"账期格式错误"))
                    return response_data
                month_mm = billing_cycle[4:]

                logging2.info(f"查询流量溢出费用: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, latn_id={latn_id}, MM={month_mm}")

                # SQL查询参数和替换值
                replacements = [
                    ['##LATNID##', str(latn_id)],
                    ['##MM##', month_mm]
                ]
                params = [acct_id]

                # 执行 SQL 查询
                results = db_manager_instance.excute_sql(
                    sql_name='QueryFluxOverflowFee',
                    params=params,
                    lst_replace_code_value=replacements
                )

                # 处理查询结果
                if not results:
                    logging2.warning(f"未查询到流量费用记录: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, latn_id={latn_id}")
                    input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "该用户投诉月份在计费系统中未查询到流量费用，请核实。", "Is_flux_fee": 0})

                has_charge = False
                fee_details = []
                for row in results:
                    # 假设查询结果是 (source_inst_id, flux_over, charge)
                    if len(row) == 6:
                        source_inst_id, flux_over, charge,name,ofr_id,offer_name = row
                        fee_details.append({
                            "offer_inst_id": source_inst_id,
                            "flux_over": flux_over,
                            "charge": charge,
                            "acct_item_name":name,
                            "offer_id":ofr_id,
                            "offer_name":offer_name
                        })
                        if charge and int(charge) > 0:
                            has_charge = True
                    else:
                        logging2.error(f"查询结果格式不正确: {row}")
                        # 可以选择跳过此行或返回错误
                input_details.append(fee_details)
            # disputeType=2 流量包功能费争议逻辑
            elif str(disputeType) == '11112':
                # 校验必要参数
                if not acct_id or not billing_cycle or not latn_id:
                    logging2.warning("缺少必要参数 ACCT_ID, billing_cycle 或 latn_id")
                    input_details.append({"status": "success", "message": "缺少必要参数 ACCT_ID, billing_cycle 或 latn_id", "Is_flux_fee": 0})

                if len(billing_cycle) != 6:
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    input_details.append({"status": "success", "message": "无效的 billing_cycle 格式", "Is_flux_fee": 0})
                month_mm = billing_cycle[4:]

                # 查询功能费（262100102），需要查出ofr_id字段
                replacements = [
                    ['##LATNID##', str(latn_id)],
                    ['##MM##', month_mm]
                ]
                params = [acct_id]
                try:
                    # 查出source_inst_id, charge, ofr_id
                    results = db_manager_instance.excute_sql(
                        sql_name='QueryFluxFunctionFee',
                        params=params,
                        lst_replace_code_value=replacements
                    )
                except Exception as e:
                    logging2.error(f"功能费争议SQL执行异常: {e}")
                    input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "查询计费系统流量包功能费异常","Is_flux_fee": 0})

                if not results:
                    input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})

                # 检查charge字段，收集offer_inst_id, charge, ofr_id
                offer_inst_id_list = []
                charge_list = []
                ofr_id_list = []
                for row in results:
                    if len(row) >= 3:
                        offer_inst_id, charge, ofr_id = row[0], row[1], row[2]
                    elif len(row) == 2:
                        offer_inst_id, charge = row[0], row[1]
                        ofr_id = None
                    else:
                        continue
                    offer_inst_id_list.append(offer_inst_id)
                    charge_list.append(charge)
                    ofr_id_list.append(ofr_id)

                has_positive = any(charge is not None and float(charge) > 0 for charge in charge_list)
                has_negative = all(charge is not None and float(charge) <= 0 for charge in charge_list)

                if has_negative:
                    input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})

                if has_positive:
                    # 查询订单号 offer_inst_id -> last_order_item_id
                    offer_inst_id_set = set(offer_inst_id_list)
                    offer_inst_id_list_unique = list(offer_inst_id_set)
                    if not offer_inst_id_list_unique:
                        input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "未获取到有效的流量包实例ID", "Is_flux_fee": 0})
                    # 查询订单号
                    replacements2 = [
                        ['##LATNID##', str(latn_id)]
                    ]
                    params2 = offer_inst_id_list_unique
                    try:
                        order_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferInstOrderList',
                            params=params2,
                            lst_replace_code_value=replacements2
                        )
                    except Exception as e:
                        logging2.error(f"查询订单号SQL异常: {e}")
                        input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "查询订单号异常", "Is_flux_fee": 0})

                    # 查询OFFER_ID和OFFER_NAME
                    # 先收集所有ofr_id（OFFER_ID）
                    offer_id_set = set()
                    offer_instid_to_offerid = {}
                    for idx, offer_inst_id in enumerate(offer_inst_id_list):
                        ofr_id = ofr_id_list[idx]
                        if ofr_id:
                            offer_id_set.add(ofr_id)
                            offer_instid_to_offerid[offer_inst_id] = ofr_id

                    offer_id_to_name = {}
                    if offer_id_set:
                        # 查询销售品名称
                        try:
                            offer_id_list = list(offer_id_set)
                            offer_name_rows = db_manager_instance.excute_sql(
                                sql_name='QueryOfferNameByIds',
                                params=offer_id_list
                            )
                            for row in offer_name_rows:
                                if len(row) >= 2:
                                    offer_id_to_name[str(row[0])] = row[1]
                        except Exception as e:
                            logging2.error(f"查询OFFER_NAME异常: {e}")

                    # 组装fluxOfrList
                    flux_ofr_list = []
                    # 订单号映射
                    offer_instid_to_orderid = {}
                    for row in order_rows:
                        if len(row) >= 2:
                            offer_instid_to_orderid[str(row[0])] = row[1]

                    for idx, offer_inst_id in enumerate(offer_inst_id_list):
                        ofr_id = ofr_id_list[idx]
                        offer_id = ofr_id if ofr_id else offer_instid_to_offerid.get(offer_inst_id)
                        offer_name = offer_id_to_name.get(str(offer_id), None) if offer_id else None
                        last_order_item_id = offer_instid_to_orderid.get(str(offer_inst_id), None)
                        flux_ofr_list.append({
                            "OFFER_ID": offer_id,
                            "OFFER_NAME": offer_name,
                            "offerInstId": offer_inst_id,
                            "lastOrderItemId": last_order_item_id
                        })

                    input_details.append({
                        "status": "success",
                        "message": "用户有订购流量包，订单号列表见详情。",
                        "Is_flux_fee": 0,
                        "fluxOfrList": flux_ofr_list,
                        "hasFluxOfrList": 1 if flux_ofr_list else 0,
                        "PROD_INST_ID": prod_inst_id,
                        "latn_id": latn_id,
                        "billing_cycle": billing_cycle,
                        "charge":ycharge,
                        "billing_nb": billing_nb
                    })
                else:
                    input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})
                # return jsonify({"status": "success","Is_flux_fee": 1,"input_details":input_details})
            # disputeType=3 国际漫游流量费用争议
            elif str(disputeType) == '3':
                input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,
                    "status": "success",
                    "message": "国际漫游由于是集团批价，省内计费只是负责代收，具体资费以及国际漫游包的介绍，请参考manyou.189.cn网站查询。如网站查询后还无法核实问题，可转人工处理",
                    "Is_flux_fee": 0
                })

            else:
                logging2.warning(f"不支持的 disputeType: {disputeType}")
                input_details.append({"billing_nb":billing_nb,"billing_cycle":billing_cycle,"latn_id":latn_id,"acct_id":acct_id,"charge":ycharge,"status": "success", "message": "现仅支持流量费用争议、流量包功能费争议和国际漫游流量费用争议问题查询，暂时不支持其他问题查询。", "Is_flux_fee": 0})
        logging2.info(f"流量溢出费用查询响应报文input_details={input_details}")
        # return jsonify(add_visualization_data("flux_overflow_fee_service",{"input_details": input_details},"流量套外费用",""))
        return {"status": "success","Is_flux_fee": 1, "input_details": input_details}
            # ({"message":"流量套外费用","input_details": input_details})
    except Exception as e:
        logging2.error(f"处理流量溢出费用查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500


# 查询用户订购套外资费销售品信息
# curl -X POST http://**************:1888/query/unlimited_package_service -H "Content-Type: application/json" -d '{"PROD_INST_ID":47508849346,"latn_id":"790","billing_cycle":"202504"}'
@app.route('/query/unlimited_package_service', methods=['POST'])
def unlimited_package_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        valid_pkgs = []
        for  row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge=row.get('charge')

            # 校验必要参数
            if not prod_inst_id or not latn_id or not billing_cycle or len(billing_cycle) != 6:
                logging2.warning("缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle")
                input_details.append({"status": "success", "message": "缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle", "Is_unlimited_package": 0})
            else:
                # 查询A1单产品套餐信息
                replacements = [['##LATNID##', str(latn_id)]]
                params = [prod_inst_id,billing_cycle,billing_cycle]
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )

                cycle_int = int(billing_cycle)
                for row in a1_rows:
                    offer_inst_id, prod_offer_id, eff_date, exp_date, update_date, obj_id, obj_type,ext_offer_inst_id = row
                    try:
                        eff_ym = eff_date.year * 100 + eff_date.month
                        exp_ym = exp_date.year * 100 + exp_date.month
                    except:
                        continue
                    offer_id_to_name=""
                    if eff_ym <= cycle_int <= exp_ym:
                        #查询销售品名称
                        offer_name_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferNameByIds',
                            params=prod_offer_id
                        )
                        logging2.info(f"offer_name_rows={len(offer_name_rows)}")
                        for row in offer_name_rows:
                                offer_id_to_name=row[1]
                                # offer_id_to_name[str(row[0])] = row[1]
                        else:
                            logging2.warning(f"No offer found for ID: {prod_offer_id}")
                        #查询销售品下面的量本ID
                        # acc_rows = db_manager_instance.excute_sql(
                        #     sql_name='QueryAccuInitRule', params=[prod_offer_id], lst_replace_code_value=[]
                        # )
                        # accu_type_list=[]
                        # for ar in acc_rows:
                        #     accu_type_list=ar[0]
                        #查询不限量
                        grp_rows = db_manager_instance.excute_sql(
                            sql_name='QueryAccuTypeGroupMbr', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        is_unlimited=0
                        if grp_rows:
                            is_unlimited= 1
                        #查询提速包
                        ts_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferPricingRelMainTag3', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        is_speedup=0
                        if ts_rows:
                            is_speedup= 1


                        #查询流量销售品
                        flowOffer_rows = db_manager_instance.excute_sql(
                                sql_name='QueryFlowOffer', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        if flowOffer_rows:
                            priorityOffer = []
                            priorityOffer_rows = db_manager_instance.excute_sql(
                                sql_name='QueryPriorityOffer', params=[prod_offer_id], lst_replace_code_value=[]
                            )
                            for pr_rows in priorityOffer_rows:
                                priorityOffer=pr_rows[0]

                            rows = db_manager_instance.excute_sql(
                                sql_name='QueryOfferTariff',
                                params=[prod_offer_id],
                                lst_replace_code_value=[]
                            )
                            tariff_mark=""
                            # 如果QueryOfferTariff查询到数据
                            if rows:
                                for row in rows:
                                    offer_id=row[0]
                                    value_string=row[1]
                                    pricing_plan_id=row[2]
                                    pricing_plan_name=row[3]
                                    tariff_unit=row[4]
                                    tariff_mark=row[5]
                                    tariff_desc=""
                                    logging2.info(f"unlimited_package_service.tariff_unit:{tariff_unit}")
                                    if tariff_unit=='1':
                                        tariff_desc=value_string+"分/G"
                                    else:
                                        if value_string:
                                            tariff_desc=str(float(value_string)/10)+"分/KB"
                                    
                                    # 查询是否为5G主套餐，判断流量费用封顶
                                    cap_limit_desc = "450元封顶"  # 默认非5G主套餐
                                    try:
                                        five_g_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryOffer5GMainPackage', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if five_g_rows:
                                            cap_limit_desc = "600元封顶"  # 5G主套餐
                                            logging2.info(f"offer_id {prod_offer_id} 是5G主套餐，使用600元封顶")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 是非5G主套餐，使用450元封顶")
                                    except Exception as cap_e:
                                        logging2.warning(f"查询5G主套餐状态时出错: {str(cap_e)}")
                                        # 出错时使用默认值450元封顶

                                    # 查询是否为日累积租费
                                    daily_cumulative_rent_fee = ""  # 默认不是日累积租费
                                    try:
                                        daily_rent_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryDailyCumulativeRentFee', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if daily_rent_rows:
                                            daily_cumulative_rent_fee = "日累积租费"  # 是日累积租费
                                            logging2.info(f"offer_id {prod_offer_id} 是日累积租费")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 不是日累积租费")
                                    except Exception as rent_e:
                                        logging2.warning(f"查询日累积租费状态时出错: {str(rent_e)}")
                                        # 出错时使用默认值（空字符串）
                                    
                                    valid_pkgs.append({
                                        "offer_id": prod_offer_id,
                                        "offer_inst_id": offer_inst_id,
                                        "ext_offer_inst_id":ext_offer_inst_id,
                                        "offer_name":offer_id_to_name,
                                        "offer_priority":priorityOffer,
                                        "tariff_desc":tariff_desc,
                                        "tariff_mark":tariff_mark,
                                        # "ACCU_TYPE_ID":accu_type_list,
                                        "is_unlimited":is_unlimited,
                                        "is_speedup":is_speedup,
                                        "cap_limit_desc":cap_limit_desc,  # 流量费用封顶字段
                                        "daily_cumulative_rent_fee":daily_cumulative_rent_fee,  # 日累积租费字段
                                        "EFF_DATE": eff_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        "EXP_DATE": exp_date.strftime('%Y-%m-%d %H:%M:%S')
                                    })
                            else:
                                # 如果QueryOfferTariff查询不到数据
                                logging2.info(f"QueryOfferTariff查询不到数据，使用QueryAlternativeOfferTariff查询: offer_id={prod_offer_id}")
                                alt_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAlternativeOfferTariff',
                                    params=[prod_offer_id],
                                    lst_replace_code_value=[]
                                )
                                
                                for alt_row in alt_rows:
                                    offer_id = alt_row[0]
                                    pricing_plan_id = alt_row[1]
                                    pricing_plan_name = alt_row[2]
                                    tariff_desc = ""
                                    
                                    # 查询是否为5G主套餐，判断流量费用封顶
                                    cap_limit_desc = "450元封顶"  # 默认非5G主套餐
                                    try:
                                        five_g_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryOffer5GMainPackage', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if five_g_rows:
                                            cap_limit_desc = "600元封顶"  # 5G主套餐
                                            logging2.info(f"offer_id {prod_offer_id} 是5G主套餐，使用600元封顶")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 是非5G主套餐，使用450元封顶")
                                    except Exception as cap_e:
                                        logging2.warning(f"查询5G主套餐状态时出错: {str(cap_e)}")
                                        # 出错时使用默认值450元封顶

                                    # 查询是否为日累积租费
                                    daily_cumulative_rent_fee = ""  # 默认不是日累积租费
                                    try:
                                        daily_rent_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryDailyCumulativeRentFee', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if daily_rent_rows:
                                            daily_cumulative_rent_fee = "日累积租费"  # 是日累积租费
                                            logging2.info(f"offer_id {prod_offer_id} 是日累积租费")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 不是日累积租费")
                                    except Exception as rent_e:
                                        logging2.warning(f"查询日累积租费状态时出错: {str(rent_e)}")
                                        # 出错时使用默认值（空字符串）
                                    
                                    valid_pkgs.append({
                                        "offer_id": prod_offer_id,
                                        "offer_inst_id": offer_inst_id,
                                        "ext_offer_inst_id":ext_offer_inst_id,
                                        "offer_name":offer_id_to_name,
                                        "offer_priority":priorityOffer,
                                        "tariff_desc":tariff_desc,
                                        "tariff_mark":tariff_mark,
                                        # "ACCU_TYPE_ID":accu_type_list,
                                        "is_unlimited":is_unlimited,
                                        "is_speedup":is_speedup,
                                        "cap_limit_desc":cap_limit_desc,  # 流量费用封顶字段
                                        "daily_cumulative_rent_fee":daily_cumulative_rent_fee,  # 日累积租费字段
                                        "EFF_DATE": eff_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        "EXP_DATE": exp_date.strftime('%Y-%m-%d %H:%M:%S')
                                    })
        logging2.info(f"查询用户订购套餐明细务返回报文valid_pkgs={valid_pkgs}")
        return jsonify(
            add_visualization_data("unlimited_package_service", {"message": "用户订购套餐明细","PROD_INST_ID": prod_inst_id, "latn_id": latn_id, "billing_cycle": billing_cycle,"valid_pkgs": valid_pkgs,**({"billing_nb": billing_nb} if billing_nb is not None else {})}, "用户订购套餐明细", ""))
    except Exception as e:
        logging2.error(f"处理不量套餐查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500


# 查询是否有剩余量本服务
# curl -X POST http://**************:1888/query/remain_accu_service -H "Content-Type: application/json" -d '{"latn_id": "790", "billing_cycle": "202504", "offerAccuDetail": [{"offer_inst_id": "123456", "OBJ_TYPE": "1", "OBJ_ID": "123456", "EFF_DATE": "2025-04-01 00:00:00", "EXP_DATE": "2025-04-30 23:59:59", "update_date": "2025-04-01 00:00:00", "accu_type_id": "8031512", "is_unlimited": "1"}]}'
@app.route('/query/remain_accu_service', methods=['POST'])
def remain_accu_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        for  row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge=row.get('charge')
        if not latn_id or not billing_cycle:
            return jsonify({"status": "success", "message": "缺少必要参数latn_id、billing_cycle或offerAccuDetail", "Is_remain_accu": 0})
        replacements = [['##LATNID##', str(latn_id)]]
        params = [prod_inst_id,billing_cycle,billing_cycle]
        a1_rows = db_manager_instance.excute_sql(
            sql_name='QueryOfferProdInstRelHisA1Info',
            params=params,
            lst_replace_code_value=replacements
        )
        offer_detail=[]
        cycle_int = int(billing_cycle)
        last_cycle_int=cycle_int-1
        for row in a1_rows:
            offer_inst_id, prod_offer_id, eff_date, exp_date, update_date, obj_id, obj_type = row
            mod_raw = int(offer_inst_id) % 10
            # mod_adjusted = mod_raw if mod_raw != 0 else 10
            mod_adjusted=mod_raw+1
            mod_val = f'{mod_adjusted:02d}'
            lst_replace_code_value = [['##LATNID##', str(latn_id)], ['##MODVAL##', mod_val]]
            rows = db_manager_instance.excute_sql(
                sql_name='QueryRemainAccuInfo', params=[offer_inst_id,cycle_int,last_cycle_int,cycle_int],
                lst_replace_code_value=lst_replace_code_value
            )
            for row in rows:
                # row: offer_inst_id, accu_type_id, INIT_VAL, ACCU_USED_VAL, ACCU_VAL, EFF_DATE, EXP_DATE
                eff_date_str = row[5].strftime('%Y-%m-%d %H:%M:%S') if row[5] else None
                exp_date_str = row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None
                accu_type_id = row[1]
                accu_type_name=""
                # accu_type_name = db_manager_instance.query_accu_type_name(accu_type_id)
                # 查询不限量
                logging2.info(f"accu_type_id={accu_type_id}")
                accu_type_rows = db_manager_instance.excute_sql(
                    sql_name='QueryAccuTypeNameById', params=[accu_type_id], lst_replace_code_value=[]
                )
                logging2.info(f"accu_type_rows={len(accu_type_rows)}")
                for rowname in accu_type_rows:
                    accu_type_name = rowname[0]
                logging2.info(f"accu_type_name={accu_type_name}")
                offer_detail.append({
                    "offer_inst_id":offer_inst_id,
                    "offer_id":prod_offer_id,
                    "accu_type_id": accu_type_id,
                    "accuTypeName": accu_type_name,
                    "INIT_VAL": row[2],
                    "ACCU_USED_VAL": row[3],
                    "ACCU_VAL": row[4],
                    "ACCU_EFF_DATE": eff_date_str,
                    "ACCU_EXP_DATE": exp_date_str
                })
        logging2.info(f"查询是否有剩余量本服务返回报文offer_detail={offer_detail}")
        return jsonify(
            add_visualization_data("remain_accu_service",
                                   {
                                       "message": "用户使用剩余量本明细",
                                       "offerAccuDetail": offer_detail
                                   },
                                   "用户使用剩余量本明细", ""))
    except Exception as e:
        logging2.error(f"处理剩余量本查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500


#查询收费清单服务(查数据库方式)
@app.route('/query/charge_detail_service', methods=['POST'])
def charge_detail_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('latn_id')
        logging2.debug(f"billing_cycle={billing_cycle}")
        endDate=UnitTool.get_last_day_of_month(billing_cycle)
        logging2.debug(f"endDate={endDate}")
        startDate = f"{billing_cycle}01"
        selected_details = []
        accNun_detail = []
        select_accNun=[]
        for row in json_dict.get('valid_pkgs'):
            accu_type_id=row.get('ACCU_TYPE_ID')
            offer_id=row.get('offer_id')
            offer_inst_id=row.get('offer_inst_id')
            pricingType=""
            replacements = [['##LATNID##', str(latn_id)]]
            if not accu_type_id or not offer_id:
                pricingTypeRow= db_manager_instance.excute_sql(
                    sql_name='QueryOfferPricingRelPricingType', params=[offer_id], lst_replace_code_value=[]
                )
                for rows in pricingTypeRow:
                    pricingType = rows[0]
            logging2.info(f"pricingType={pricingType}")
            if pricingType=="80C":
                prodInstList = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelProdInst', params=[offer_inst_id], lst_replace_code_value=replacements
                )
                if prodInstList:
                    select_accNun=prodInstList[0]
        unique_accNun = set(select_accNun)
        logging2.debug(f"unique_accNun={unique_accNun}")
        if len(unique_accNun)>0:
            for prodInstId in unique_accNun:
                accNumList = db_manager_instance.excute_sql(
                    sql_name='QueryProdInst', params=[prodInstId], lst_replace_code_value=replacements
                )
                logging2.info(f"accNumList={accNumList}")
                for accNumrows in accNumList:
                    accNum=accNumrows[0]
                    # 使用字典构造代替字符串拼接
                    replacements = [
                        ['##LATNID##', str(latn_id)],
                        ['##BILLINGCYCLE##', billing_cycle]
                    ]
                    params = [accNum]
                    try:
                        # 查出source_inst_id, charge, ofr_id
                        results = db_manager_instance.excute_sql(
                            sql_name='QueryOfferFlowCharge',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for result in results:
                            accNun_detail.append({
                            "billing_nb":accNum,
                            "offer_id":result[0],
                            "sum_flow": result[1],
                            "sum_fee": result[2],
                            "price": result[3]
                        })
                    except Exception as e:
                        logging2.error(f"功能费争议SQL执行异常: {e}")
                        input_details.append(
                            {"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id,
                             "acct_id": acct_id, "charge": ycharge, "status": "success",
                             "message": "查询计费系统流量包功能费异常", "Is_flux_fee": 0})

                # logging2.info(f"resultJson={resultJson}")
                selected_details.append({
                "accNun_detail":accNun_detail
            })
        else:
            replacements = [
                ['##LATNID##', str(latn_id)],
                ['##BILLINGCYCLE##', billing_cycle]
            ]
            params = [billing_nb]
            try:
                # 查出source_inst_id, charge, ofr_id
                results = db_manager_instance.excute_sql(
                    sql_name='QueryOfferFlowCharge',
                    params=params,
                    lst_replace_code_value=replacements
                )
                for result in results:
                    accNun_detail.append({
                        "billing_nb": billing_nb,
                        "offer_id": result[0],
                        "sum_flow": result[1],
                        "sum_fee": result[2],
                        "price":result[3]
                    })
            except Exception as e:
                logging2.error(f"功能费争议SQL执行异常: {e}")
                input_details.append(
                    {"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id,
                     "message": "查询计费系统流量包功能费异常", "Is_flux_fee": 0})
            # logging2.info(f"resultJson={resultJson}")
            selected_details.append({
                "accNun_detail":accNun_detail
            })
        logging2.info(f"清单返回详细信息selected_details={selected_details}")
        response_data = {
            "message": "清单返回详细信息",
            "BillItemsDetail": selected_details
        }
        # return jsonify(add_visualization_data("charge_detail_service", response_data, "收费清单查询", ""))
        return response_data
    except Exception as e:
        logging2.error(f"处理收费清单查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": f"内部服务器错误: {str(e)}"}), 500


# 非不限量流量争议诊断服务
# curl -X POST http://**************:1888/query/non_unlimited_flux_dispute_service -H "Content-Type: application/json" -d '{...}'
@app.route('/query/non_unlimited_flux_dispute_service', methods=['POST'])
def non_unlimited_flux_dispute_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        charge_details = json_dict.get('chargeDetails', [])
        offer_accu_detail = json_dict.get('offerAccuDetail', [])
        latn_id = json_dict.get('latn_id')  # 获取latn_id用于后续查询

        # 只返回一条诊断结果，按优先级判断
        final_result = None
        for detail in charge_details:
            start_time_str = detail.get('start_time')
            start_time = datetime.strptime(start_time_str, '%Y%m%d%H%M%S') if start_time_str else None
            valid_accu_found = False
            # 定向流量相关变量
            directional_offer_ids = set()
            directional_accu_found = False
            # 共享包相关统计变量
            total_shared_pkg = 0  # 共享包总数
            invalid_shared_pkg = 0  # 不可用的共享包数
            shared_pkg_msg = ''
            # 校园流量相关变量
            school_flow_found = False
            # 乡镇流量相关变量
            town_flow_attr_values = {}  # 存储乡镇流量包基站组
            town_flow_found = False
            # 本地/省内流量相关变量
            local_province_flow_found = False
            local_province_flow_types = {}  # 存储本地/省内流量类型
            # 查找所有有量本且ACCU_VAL>0的offerinstid
            for offer in offer_accu_detail:
                offer_inst_id = offer.get('offer_inst_id')
                eff_date = offer.get('EFF_DATE')
                exp_date = offer.get('EXP_DATE')
                shared_flag = offer.get('shared_flag', 0)
                update_date = offer.get('update_date')
                is_unlimited = offer.get('is_unlimited', 0)
                offer_id = offer.get('offer_id')
                # 只处理非不限量套餐
                if is_unlimited:
                    continue
                # 校验套餐生失效
                offer_eff = datetime.strptime(eff_date, '%Y-%m-%d %H:%M:%S') if eff_date else None
                offer_exp = datetime.strptime(exp_date, '%Y-%m-%d %H:%M:%S') if exp_date else None
                if not offer_eff or not offer_exp:
                    continue
                # 套餐实例有效期覆盖话单时间
                logging2.debug(f"套餐实例有效期判断: offer_eff={offer_eff}, start_time={start_time}, offer_exp={offer_exp}")
                if offer_eff <= start_time <= offer_exp:
                    # 有量本且ACCU_VAL>0
                    for accu in offer.get('accu_details', []):
                        accu_val = accu.get('ACCU_VAL', 0)
                        accu_eff = accu.get('EFF_DATE')
                        accu_exp = accu.get('EXP_DATE')
                        # 量本ACCU_VAL>0且生失效有效
                        if accu_val > 0 and accu_eff and accu_exp:
                            accu_eff_dt = datetime.strptime(accu_eff, '%Y-%m-%d %H:%M:%S')
                            accu_exp_dt = datetime.strptime(accu_exp, '%Y-%m-%d %H:%M:%S')
                            logging2.debug(f"量本有效期判断: accu_eff_dt={accu_eff_dt}, start_time={start_time}, accu_exp_dt={accu_exp_dt}")
                            if accu_eff_dt <= start_time <= accu_exp_dt:
                                valid_accu_found = True
                                # 定向流量判断，offer_id存在且有效量本
                                if offer_id:
                                    db_result = db_manager_instance.excute_sql('QueryOfferPricingRelMainTag5', params=[offer_id])
                                    if db_result and len(db_result) > 0:
                                        directional_offer_ids.add(offer_id)
                                        directional_accu_found = True
                                # 校园流量判断，offer_id存在且有效量本
                                if offer_id:
                                    school_offer_result = db_manager_instance.excute_sql('QuerySchoolFlowOffer', params=[offer_id])
                                    if school_offer_result and len(school_offer_result) > 0:
                                        school_flow_found = True
                                # 乡镇流量判断
                                if latn_id and offer_inst_id:
                                    # 查询乡镇流量属性
                                    replacements = [['##LATNID##', str(latn_id)]]
                                    town_flow_result = db_manager_instance.excute_sql(
                                        sql_name='QueryTownFlowAttr', 
                                        params=[offer_inst_id], 
                                        lst_replace_code_value=replacements
                                    )
                                    if town_flow_result and len(town_flow_result) > 0:
                                        attr_value = town_flow_result[0][0]
                                        town_flow_attr_values[str(offer_inst_id)] = attr_value
                                        town_flow_found = True
                                # 本地/省内流量判断
                                if offer_id:
                                    local_province_result = db_manager_instance.excute_sql('QueryLocalProvinceFlowOffer', params=[offer_id])
                                    if local_province_result and len(local_province_result) > 0 and local_province_result[0][0] is not None:
                                        local_or_province = local_province_result[0][0]
                                        local_province_flow_types[str(offer_id)] = local_or_province
                                        local_province_flow_found = True
                                # 判断共享包可用性
                                if shared_flag == 1:
                                    total_shared_pkg += 1
                                    # 判断update_date
                                    if update_date:
                                        update_dt = datetime.strptime(update_date, '%Y-%m-%d %H:%M:%S')
                                        logging2.debug(f"共享包可用性判断: start_time={start_time}, update_dt={update_dt}")
                                        if start_time > update_dt:
                                            pass  # 该共享包可用，不计入不可用
                                        else:
                                            invalid_shared_pkg += 1
                                            shared_pkg_msg = '批价正常，共享包有剩余量但不可用'
                                    else:
                                        invalid_shared_pkg += 1
                                        shared_pkg_msg = '批价正常，共享包有剩余量但不可用'
                                # 非共享包不参与共享包判断
                                
            # 按优先级判断结果：1、是否有效套餐实例判断，2、是否共享量本判断，3、是否定向流量判断，4、是否校园流量判断，5、是否乡镇流量判断，6、是否本地/省内流量判断，7、判断是否跨月清单判断
            if not valid_accu_found:
                # 是否有效套餐实例判断：没有找到任何有效量本
                final_result = {'result': '批价正常，流量费用话单产生时，有剩余量的销售品不在有效期内，无法使用剩余量本。'}
                break
            # 是否共享量本判断：只有所有共享包都不可用才返回该结果
            elif total_shared_pkg > 0 and invalid_shared_pkg == total_shared_pkg:
                final_result = {'result': shared_pkg_msg}
                break
            # 是否定向流量判断
            elif directional_accu_found:
                # 用所有定向流量 offer_id 和 chargeDetails 的 rating_group 组合去查SQL
                for offer_id in directional_offer_ids:
                    rating_group = detail.get('rating_group')
                    if offer_id and rating_group:
                        directional_sql_result = db_manager_instance.excute_sql('QueryOfferPricingRelForDirectional', params=[offer_id, rating_group])
                        if directional_sql_result and len(directional_sql_result) > 0:
                            final_result = {'result': '未走上定向流量，人工待查'}
                            break
                if final_result:
                    break
            # 是否校园流量判断
            elif school_flow_found:
                cell_id = detail.get('cell_id')
                if cell_id:
                    school_cell_result = db_manager_instance.excute_sql('QuerySchoolFlowCell', params=[cell_id])
                    if school_cell_result and len(school_cell_result) > 0:
                        final_result = {'result': '未走上校园流量，人工待查'}
                        break
            # 是否乡镇流量判断
            elif town_flow_found:
                cell_id = detail.get('cell_id')
                if cell_id:
                    for offer_inst_id, attr_value in town_flow_attr_values.items():
                        # 查询基站是否在乡镇流量基站组中
                        town_cell_result = db_manager_instance.excute_sql('QueryTownFlowCell', params=[attr_value, cell_id])
                        if town_cell_result and len(town_cell_result) > 0:
                            final_result = {'result': '未走上乡镇流量，人工待查'}
                            break
                if final_result:
                    break
            # 是否本地/省内流量判断
            elif local_province_flow_found:
                roam_flag = detail.get('roam_flag')
                if roam_flag:
                    for offer_id, local_or_province in local_province_flow_types.items():
                        # 根据local_or_province和roam_flag判断
                        if local_or_province == 1 and roam_flag != '0':
                            # 本地流量套餐，但不是本地使用
                            final_result = {'result': '未走上本地流量，人工待查'}
                            break
                        elif local_or_province == 2 and roam_flag not in ['0', '1']:
                            # 省内流量套餐，但不是本地或省内使用
                            final_result = {'result': '未走上省内流量，人工待查'}
                            break
                if final_result:
                    break
            else:
                # 判断是否跨月清单
                billing_cycle = json_dict.get('billing_cycle')
                if start_time and billing_cycle:
                    # 从start_time中提取年月
                    start_time_ym = start_time.strftime('%Y%m')
                    # 判断是否跨月
                    if start_time_ym != billing_cycle:
                        final_result = {'result': '批价正常，可能为跨月清单导致，请核实'}
                        break
        # 循环结束后，若不是以上判断结果，返回默认结果
        if not final_result:
            final_result = {'result': '批价正常，收费时，无可用的免费量，各量本使用情况、溢出费用与溢出流量见详情。'}
            # 需要透传 offerAccuDetail
            return jsonify({
                'status': 'success',
                **final_result,
                'offerAccuDetail': offer_accu_detail,
                'hasOfferAccuDetail': 1  # 标识有offerAccuDetail
            })
        else:
            return jsonify({
                'status': 'success',
                **final_result,
                'hasOfferAccuDetail': 0  # 标识没有offerAccuDetail
            })
    except Exception as e:
        logging2.error(f"非不限量流量争议诊断服务处理请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({'status': 'error', 'message': f'服务器错误: {str(e)}'}), 500

# 查询主副卡关系
@app.route('/query/userrel_100800_service', methods=['POST'])
def userrel_100800_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        accNum_detail = []
        for row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge=row.get('charge')

            # 校验必要参数
            if not prod_inst_id or not latn_id or not billing_cycle or len(billing_cycle) != 6:
                logging2.warning("缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle")
                input_details.append({"status": "success", "message": "缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle", "Is_unlimited_package": 0})
            else:
                # 查询A1单产品套餐信息
                replacements = [['##LATNID##', str(latn_id)]]
                params = [prod_inst_id,prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )
                for row in a_rows:
                    a_prod_inst_id = row
                    params = [a_prod_inst_id]
                    a1_rows = db_manager_instance.excute_sql(
                        sql_name='QueryProdInstRel_100800',
                        params=params,
                        lst_replace_code_value=replacements
                    )
                    for arow in a1_rows:
                        a_accNum=''
                        z_accNum=''
                        a_prod_inst_id, z_prod_inst_id, eff_date,exp_date  = arow
                        params=[a_prod_inst_id]
                        a2_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInst',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for brow in a2_rows:
                            a_accNum=brow
                        params=[z_prod_inst_id]
                        a3_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInst',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for crow in a3_rows:
                            z_accNum=brow
                        accNum_detail.append({
                            "主卡号码": a_accNum,
                            "副卡号码": z_accNum,
                            "生效时间": eff_date,
                            "失效时间": exp_date
                        })
        return jsonify(
            add_visualization_data("userrel_100800_service", {"accNum_detail_10080":accNum_detail}, "主副卡关系", ""))
    except Exception as e:
        logging2.error(f"处理不量套餐查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500




# 套餐使用明细和账单信息查询服务
# curl -X POST http://**************:1888/query/package_usage_billing_service -H "Content-Type: application/json" -d '{"billing_nb": "123456", "billing_cycle": "202503", "latn_id": "791"}'
@app.route('/query/package_usage_billing_service', methods=['POST'])
def package_usage_billing_service():
    """
    套餐使用明细和账单信息查询接口
    """
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"套餐使用明细和账单信息查询请求参数: {data}")
        json_dict = json.loads(data)

        # 获取请求参数
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('latn_id')

        # 基础参数校验
        if not billing_nb or not billing_cycle:
            return jsonify({
                "status": "failed", 
                "message": "缺少必要参数 billing_nb 或 billing_cycle"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "failed", 
                "message": "无效的 billing_cycle 格式"
            })

        # 获取本地网ID（如果缺失则查询）
        latn_id, error_msg = get_latn_id_if_missing(billing_nb, latn_id)
        if not latn_id:
            return jsonify({
                "status": "failed", 
                "message": error_msg
            })

        logging2.info(f"开始查询套餐使用明细和账单信息: billing_nb={billing_nb}, billing_cycle={billing_cycle}, latn_id={latn_id}")

        # 查询用户资料信息
        user_profile_data = query_user_profile_internal(billing_nb, latn_id, billing_cycle)
        if not user_profile_data or user_profile_data.get('Is_user_profile') != 1:
            return jsonify({
                "status": "failed", 
                "message": "未找到用户资料信息"
            })

        prod_inst_id = user_profile_data.get('PROD_INST_ID')
        acct_id = user_profile_data.get('ACCT_ID')

        # 并行查询套餐使用明细和账单信息
        package_usage_data = query_package_usage_internal(prod_inst_id, latn_id, billing_cycle, acct_id, billing_nb)
        billing_data = query_billing_info_internal(acct_id, billing_cycle, latn_id)

        # 返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "offerAccuDetail": package_usage_data.get('offerAccuDetail', []), # 套餐使用明细信息
            **{k: v for k, v in billing_data.items() if k != 'Is_charge_detail'} # 账单信息
        }

        return jsonify(response_data)

    except Exception as e:
        logging2.error(f"处理套餐使用明细和账单信息查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500

#量本使用量明细接口
@app.route('/query/userResourceQueryDetailBon', methods=['POST'])
def userResourceQueryDetailBon():
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"量本使用量明细接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取关键参数
        accNbr = json_dict.get('accNbr')
        billingCycle = json_dict.get('billingCycle')

        # 收集套餐实例ID和累积类型ID
        offer_inst_ids = set()
        accu_type_ids = set()

        for offer in json_dict.get("offerInfo", []):
            offer_inst_id = offer.get("offerInstId")
            if offer_inst_id is not None:
                offer_inst_ids.add(offer_inst_id)

            # 修复空键问题，假设正确键名为'accuRecords'
            for accuRecord in offer.get("accuRecords", []):
                accuTypeId = accuRecord.get("accuTypeId")
                if accuTypeId is not None:
                    accu_type_ids.add(accuTypeId)

        logging2.info(f"userResourceQueryDetailBon accuTypeId_set:{accu_type_ids}")

        # 准备查询请求
        config_path = get_config_path()
        if not config_path:
            return jsonify({"status": "error", "message": "找不到配置文件config.json"})
            
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("AccuUseQryCenter")
        if not url:
            return jsonify({"status": "error", "message": "配置中未找到AccuUseQryCenter接口URL"})
        user_resourcedetails = []

        # 分批处理套餐实例ID查询
        batch_size = 10  # 每批处理的数量
        offer_batches = [list(offer_inst_ids)[i:i + batch_size] for i in range(0, len(offer_inst_ids), batch_size)]

        for batch in offer_batches:
            for offer_inst_id in batch:
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 11111111111
                    },
                    "svcObjectStruct": {
                        "objType": "3",
                        "objValue": accNbr,
                        "objAttr": "2",
                        "dataArea": "2"
                    },
                    "queryFlag": "3",
                    "billingCycle": int(billingCycle),
                    "offerInstId": offer_inst_id
                }

                logging2.info(f"userResourceQueryDetailBon data:{request_payload}")

                # 发送请求并处理结果
                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"userResourceQueryDetailBon result:{result}")

                    if result:
                        filtered_data = filter_accu_data(result, accu_type_ids)
                        user_resourcedetails.append(filtered_data)
                except Exception as req_ex:
                    logging2.error(f"请求套餐实例ID {offer_inst_id} 时出错: {str(req_ex)}")
                    response_data = {
                        "status": "error",
                        "message": "查询失败",
                        "billing_nb": accNbr,  # 修正变量名
                        "billing_cycle": billingCycle,
                        "userResourcedetails": user_resourcedetails
                    }
                    return jsonify(response_data)
                    # 可以选择继续处理其他批次，或者根据业务需求决定是否终止

        logging2.info(f"userResourceQueryDetailBon userResourcedetails:{user_resourcedetails}")

        # 返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": accNbr,  # 修正变量名
            "billing_cycle": billingCycle,
            "userResourcedetails": user_resourcedetails
        }
        return jsonify(response_data)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({"status": "error", "message": "无效的请求格式"}), 400
    except Exception as e:
        logging2.error(f"量本使用量明细接口请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500

#乡镇、校园-定价协议查询
@app.route('/query/userTownFlowSchoolCell', methods=['POST'])
def userTownFlowSchoolCell():
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")
        # 初始化响应列表
        response_list = []
        # 处理每个输入项
        for input_row in request_data.get('input_details', []):
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')
            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            a1_rows = db_manager_instance.excute_sql(
                sql_name='QueryOfferProdInstRelHisA1Info',
                params=params,
                lst_replace_code_value=replacements
            )
            # 初始化销售品详情列表
            offer_detail = []
            # 处理每个销售品实例
            for offer_row in a1_rows:
                (offer_inst_id, prod_offer_id, eff_date,
                 exp_date, update_date, obj_id, obj_type,ext_offer_inst_id) = offer_row
                # 查询乡镇基站信息
                town_flow_cell_id = _get_town_flow_cell_id(prod_offer_id, offer_inst_id)
                # 查询校园基站信息
                school_flow_flag = _check_school_flow(prod_offer_id)
                # 构建销售品详情
                offer_info = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": prod_offer_id,
                    "eff_date": eff_date.strftime('%Y-%m-%d %H:%M:%S') if eff_date else None,
                    "exp_date": exp_date.strftime('%Y-%m-%d %H:%M:%S') if exp_date else None,
                    "offer_townFlow": town_flow_cell_id,
                    "offer_school": school_flow_flag
                }
                offer_detail.append(offer_info)
            # 构建单条响应数据
            response_item = {
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)
        logging2.info(f"response_list:{response_list}")
        # 返回完整响应
        return jsonify(response_list)
    except Exception as e:
        logging2.error(f"处理请求时发生错误: {str(e)}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500

def _get_town_flow_cell_id(prod_offer_id, offer_inst_id):
    """查询乡镇基站组ID的辅助函数"""
    # 查询订购销售品是否有乡镇基站销售品
    town_flow_rows = db_manager_instance.excute_sql(
        sql_name='QueryTownFlowCellAttr',
        params=[prod_offer_id],
        lst_replace_code_value=[]
    )
    if not town_flow_rows:
        return ""
    # 获取属性定义ID
    property_define_id = town_flow_rows[0][1]  # 假设第二列是property_define_id
    # 查询乡镇基站组ID
    town_flow_cell = db_manager_instance.excute_sql(
        sql_name='QueryTownFlowAttrValue',
        params=[offer_inst_id, property_define_id],
        lst_replace_code_value=[]
    )
    return town_flow_cell[0][0] if town_flow_cell else ""

def _check_school_flow(prod_offer_id):
    """检查是否为校园流量销售品的辅助函数"""
    school_offer_result = db_manager_instance.excute_sql(
        'QuerySchoolFlowOffer',
        params=[prod_offer_id]
    )
    return 53 if school_offer_result else ""


#话单基站是否属于校园基站或乡镇基站
@app.route('/query/userTownFlowSchoolCellRtBillItem', methods=['POST'])
def userTownFlowSchoolCellRtBillItem():
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")

        # 验证请求数据结构
        if 'input_details' not in request_data or not isinstance(request_data['input_details'], list):
            raise ValueError("请求数据缺少input_details列表")

        # 初始化响应列表
        response_list = []
        bsid_summary_json = {}  # 存储解析后的BSID汇总数据

        # 处理每个输入项
        for input_row in request_data['input_details']:
            # 获取必要参数
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')

            # 参数验证
            if not all([billing_nb, prod_inst_id, latn_id, billing_cycle]):
                logging2.warning(f"输入项缺少必要参数: {input_row}")
                response_item = {
                    "status": "error",
                    "message": "缺少必要参数",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 构建HTTP请求数据
            request_date = {
                "endDate": billing_cycle + "31",
                "operAttrStruct": {
                    "operServiceId": "202501140913056692116205",
                    "lanId": latn_id,
                    "operPost": 1,
                    "operOrgId": 0,
                    "staffId": 0,
                    "operTime": "20250114091305"
                },
                "isDesensitive": "0",
                "svcObjectStruct": {
                    "objValue": billing_nb,
                    "objAttr": "2",
                    "objType": "3",
                    "dataArea": "2"
                },
                "qryType": "3",
                "startDate": billing_cycle + "01"
            }

            # 发送HTTP请求
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            http_client = HTTPClient(ConfigLoader(config_path))
            http_response = http_client.send_post_request(
                payload=request_date,
                url_key="RtBillItem"
            )

            # 处理HTTP响应
            if not http_response:
                logging2.error(f"HTTP请求失败: billing_nb={billing_nb}")
                response_item = {
                    "status": "error",
                    "message": "获取数据失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 解析流量数据并存储
            bsid_summary_json = _parse_traffic_data(http_response)
            bsid_data = json.loads(bsid_summary_json) if isinstance(bsid_summary_json, str) else {}

            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]

            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
            except Exception as e:
                logging2.error(f"SQL查询失败: {e}, params={params}")
                response_item = {
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 初始化销售品详情列表
            offer_detail = []

            # 处理每个销售品实例
            for offer_row in a1_rows:
                (offer_inst_id, prod_offer_id, eff_date,
                 exp_date, update_date, obj_id, obj_type,ext_offer_inst_id) = offer_row

                # 查询乡镇基站信息
                town_flow_cell_id = _get_town_flow_cell_id(prod_offer_id, offer_inst_id)

                # 查询校园基站信息
                school_flow_flag = _check_school_flow(prod_offer_id)

                # 构建销售品详情
                offer_info = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": prod_offer_id,
                    "eff_date": eff_date.strftime('%Y-%m-%d %H:%M:%S') if eff_date else None,
                    "exp_date": exp_date.strftime('%Y-%m-%d %H:%M:%S') if exp_date else None,
                    "offer_townFlow": town_flow_cell_id,
                    "offer_school": school_flow_flag
                }
                offer_detail.append(offer_info)

            # 构建单条响应数据
            response_item = {
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)

        # ============== 添加用户要求的逻辑 ==============
        # 提取非空的offer_townFlow和offer_school
        offer_town_flow_list = []
        school_flow_flag_list = []

        for item in response_list:
            if item.get("status") == "success":
                for offer in item.get("offer_detail", []):
                    town_flow = offer.get("offer_townFlow")
                    school_flag = offer.get("offer_school")
                    if town_flow:
                        offer_town_flow_list.append(town_flow)
                    if school_flag is not None:  # 检查是否非空
                        school_flow_flag_list.append(school_flag)

        # 去重处理
        offer_town_flow_list = list(set(offer_town_flow_list))
        school_flow_flag_list = list(set(school_flow_flag_list))

        # 存储查询结果
        offer_town_flow_list_ids = []
        school_flow_flag_list_ids = []

        # 遍历乡镇基站列表查询数据库
        for town_flow in offer_town_flow_list:
            if town_flow:
                try:
                    # 假设查询数据库的函数，实际需根据数据库结构调整
                    result = db_manager_instance.excute_sql(
                        sql_name='QueryCellId',
                        params=[town_flow]
                    )
                    if result:
                        offer_town_flow_list_ids.extend([row[0] for row in result])
                except Exception as e:
                    logging2.error(f"查询乡镇基站ID失败: {e}, town_flow={town_flow}")

        # 遍历校园基站列表查询数据库
        for school_flag in school_flow_flag_list:
            if school_flag is not None:
                try:
                    # 假设查询数据库的函数，实际需根据数据库结构调整
                    result = db_manager_instance.excute_sql(
                        sql_name='QueryCellId',
                        params=[53]
                    )
                    if result:
                        school_flow_flag_list_ids.extend([row[0] for row in result])
                except Exception as e:
                    logging2.error(f"查询校园基站ID失败: {e}, school_flag={school_flag}")

        # 在bsidSummaryJson中匹配数据
        bsid_data = json.loads(bsid_summary_json) if isinstance(bsid_summary_json, str) else {}
        town_bsids = []
        school_bsids = []

        for bsid_info in bsid_data.get("bsidSummary", []):
            bsid = bsid_info.get("bsid")
            if bsid in offer_town_flow_list_ids:
                town_bsids.append(bsid)
            if bsid in school_flow_flag_list_ids:
                school_bsids.append(bsid)

        # 将结果添加到响应中
        final_response = {
            "status": "success",
            "message": "所有数据处理完成",
            "original_response": response_list,
            "town_bsids": town_bsids,
            "school_bsids": school_bsids,
            "bsid_summary": bsid_data
        }

        return jsonify(final_response)

    except Exception as e:
        logging2.error(f"处理请求时发生未知错误: {e}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500


# 提取清单报文中的bsid，汇总Volume、Fee以及最早时间StartTime
def _parse_traffic_data(json_data):
    try:
        # 解析JSON数据
        data = json.loads(json_data)
        # 提取数据项列表
        items = data.get("dataBillItems", [])
        if not items:
            return json.dumps({
                "status": "error",
                "message": "没有找到数据项"
            })

        # 转换为DataFrame以便处理
        df = pd.DataFrame(items)
        # 处理可能的空值或非数值类型
        df["volume"] = pd.to_numeric(df["volume"], errors="coerce").fillna(0).astype(int)
        df["fee"] = pd.to_numeric(df["fee"], errors="coerce").fillna(0).astype(int)
        # 转换时间格式
        df["startTime"] = pd.to_datetime(df["startTime"], format="%Y%m%d%H%M%S")
        # 按bsid分组，计算volume和fee的总和，以及最早的startTime
        grouped = df.groupby("bsid").agg({
            "volume": "sum",
            "fee": "sum",
            "startTime": "min"
        }).reset_index()
        # 准备结果JSON
        result = {
            "bsidSummary": [
                {
                    "bsid": str(row["bsid"]),  # 确保BSID为字符串类型
                    "totalVolume": row["volume"],
                    "totalFee": row["fee"],
                    "earliestStartTime": row["startTime"].strftime("%Y%m%d%H%M%S")
                }
                for _, row in grouped.iterrows()
            ]
        }
        return json.dumps(result, ensure_ascii=False)
    except Exception as e:
        logging2.error(f"解析流量数据失败: {e}")
        return json.dumps({
            "status": "error",
            "message": f"解析流量数据失败: {str(e)}"
        })


#定向-定价协议查询
@app.route('/query/userDirectionalOfferPricing', methods=['POST'])
def userDirectionalOfferPricing():
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")

        # 验证请求数据结构
        if 'input_details' not in request_data or not isinstance(request_data['input_details'], list):
            raise ValueError("请求数据缺少input_details列表")
        # 初始化响应列表
        response_list = []

        # 处理每个输入项
        for input_row in request_data['input_details']:
            # 获取必要参数
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')

            # 参数验证
            if not all([billing_nb, prod_inst_id, latn_id, billing_cycle]):
                logging2.warning(f"输入项缺少必要参数: {input_row}")
                response_item = {
                    "status": "error",
                    "message": "缺少必要参数",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 初始化销售品详情列表
            offer_detail = []

            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
                logging2.debug(f"查询销售品关系历史信息成功，结果数: {len(a1_rows)}")
            except Exception as e:
                logging2.error(f"查询销售品关系历史信息失败: {e}, params={params}")
                response_item = {
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 处理每个销售品实例
            for offer_row in a1_rows:
                try:
                    (offer_inst_id, prod_offer_id, eff_date,
                     exp_date, update_date, obj_id, obj_type) = offer_row

                    # 查询定向销售品定价
                    pricing_params = [prod_offer_id]
                    a2_rows = db_manager_instance.excute_sql(
                        sql_name='QueryDirectionalOfferPricing',
                        params=pricing_params,
                        lst_replace_code_value=replacements
                    )
                    logging2.debug(f"查询定向销售品定价成功，结果数: {len(a2_rows)}")

                    # 处理每个定价记录
                    for offerPricing_row in a2_rows:
                        offer_id, offer_name, pricing_plan_id, value_string = offerPricing_row

                        # 构建销售品详情（确保日期字段为字符串类型）
                        offer_info = {
                            "offer_inst_id": offer_inst_id,
                            "offer_id": prod_offer_id,
                            "eff_date": str(eff_date),
                            "exp_date": str(exp_date),
                            "offer_rg": value_string
                        }
                        offer_detail.append(offer_info)

                except Exception as e:
                    logging2.error(f"处理销售品实例时发生错误: {e}, prod_offer_id={prod_offer_id}")

            # 构建单条响应数据
            response_item = {
                "status": "success" if offer_detail else "warning",
                "message": "查询成功" if offer_detail else "未找到定价信息",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)

        # 返回完整响应
        return jsonify(response_list)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({"status": "error", "message": "无效的JSON格式"}), 400
    except ValueError as ve:
        logging2.error(f"请求格式错误: {ve}")
        return jsonify({"status": "error", "message": str(ve)}), 400
    except Exception as e:
        logging2.error(f"处理请求时发生未知错误: {e}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500


# 查询主副卡所有号码的套餐使用量查询接口
@app.route('/query/primarySecondaryCardUsage', methods=['POST'])
def primary_secondary_card_usage():
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询主副卡套餐使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数 - 根据要求只校验PROD_INST_ID和billing_nb
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 可选参数

            if not prod_inst_id or not billing_nb or not billing_cycle:
                logging2.warning("缺少必要参数 PROD_INST_ID 或 billing_nb 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 PROD_INST_ID 或 billing_nb 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 如果没有提供latn_id，尝试通过billing_nb查询
            if not latn_id:
                latn_id, error_msg = get_latn_id_if_missing(billing_nb, None)
                if not latn_id:
                    logging2.error(f"无法获取latn_id: {error_msg}")
                    response_list.append({
                        "status": "error",
                        "message": f"无法获取本地网ID: {error_msg}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue
                logging2.info(f"通过billing_nb查询到latn_id: {latn_id}")

            logging2.info(f"开始查询主副卡套餐使用量: PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billingCycle={billing_cycle}, latn_id={latn_id}")

            # 通过QueryOfferProdInstRelHisA1Info查询获得ext_offer_inst_id
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            
            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
                logging2.debug(f"查询销售品关系历史信息成功，结果数: {len(a1_rows)}")
            except Exception as e:
                logging2.error(f"查询销售品关系历史信息失败: {e}")
                response_list.append({
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if not a1_rows:
                logging2.warning(f"未找到套餐实例信息: PROD_INST_ID={prod_inst_id}")
                response_list.append({
                    "status": "error",
                    "message": "未找到套餐实例信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 提取所有ext_offer_inst_id
            ext_offer_inst_ids = []
            for db_row in a1_rows:
                # QueryOfferProdInstRelHisA1Info返回: offer_inst_id, prod_offer_id, EFF_DATE, EXP_DATE, update_date, OBJ_ID, OBJ_TYPE, ext_offer_inst_id
                if len(db_row) >= 8:
                    ext_offer_inst_id = db_row[7]  # ext_offer_inst_id是第8个字段
                    if ext_offer_inst_id:
                        ext_offer_inst_ids.append(ext_offer_inst_id)

            if not ext_offer_inst_ids:
                logging2.warning("未找到有效的ext_offer_inst_id")
                response_list.append({
                    "status": "error",
                    "message": "未找到有效的套餐实例ID",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"找到ext_offer_inst_ids: {ext_offer_inst_ids}")

            # 调用openapi接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("userResourceQueryDetailBon")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到userResourceQueryDetailBon接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            all_usage_details = []

            for ext_offer_inst_id in ext_offer_inst_ids:
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 11111111111
                    },
                    "svcObjectStruct": {
                        "objType": "3",
                        "objValue": billing_nb,
                        "objAttr": "2",
                        "dataArea": "2"
                    },
                    "queryFlag": "3",
                    "billingCycle": int(billing_cycle),
                    "offerInstId": str(ext_offer_inst_id)
                }

                logging2.info(f"调用openapi接口，请求参数: {request_payload}")

                # 发送请求并处理结果
                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"openapi接口返回结果: {result}")

                    if result:
                        response_data = result
                        
                        # 检查响应状态
                        if response_data.get("resultCode") == "0":
                            # 提取需要的数据
                            detail_offer_inst_info = response_data.get("detailOfferInstInfo", [])
                            
                            for offer_info in detail_offer_inst_info:
                                offer_id = offer_info.get("offerId")
                                offer_inst_id = offer_info.get("offerInstId")
                                accu_qry_user_list = offer_info.get("accuQryUserList", [])
                                
                                # 提取accNbr、usageAmount、accuTypeId、accuTypeAttr，只取unitTypeId为3的数据
                                user_usage_list = []
                                for user_info in accu_qry_user_list:
                                    unit_type_id = user_info.get("unitTypeId")
                                    # 只处理unitTypeId为3的数据
                                    if str(unit_type_id) == "3":
                                        acc_nbr = user_info.get("accNbr")
                                        usage_amount = user_info.get("usageAmount")
                                        accu_type_id = user_info.get("accuTypeId")
                                        accu_type_attr = user_info.get("accuTypeAttr")
                                        
                                        if acc_nbr and usage_amount is not None:
                                            user_usage_list.append({
                                                "accNbr": acc_nbr,
                                                "usageAmount": usage_amount,
                                                "accuTypeId": accu_type_id,
                                                "accuTypeAttr": accu_type_attr
                                            })
                                
                                # 只有当user_usage_list不为空时才添加到结果列表
                                if user_usage_list:
                                    all_usage_details.append({
                                        "offerId": offer_id,
                                        "offerInstId": offer_inst_id,
                                        "userUsageList": user_usage_list
                                    })
                        else:
                            logging2.error(f"openapi接口返回错误: {response_data.get('resultMsg', '未知错误')}")
                            
                except Exception as req_ex:
                    logging2.error(f"请求套餐实例ID {ext_offer_inst_id} 时出错: {str(req_ex)}")
                    # 继续处理其他套餐实例，不中断整个流程

            # 添加单个用户的查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billingCycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "usageDetails": all_usage_details
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "主副卡套餐使用量查询完成",
            "input_details": response_list
        }
        
        # 检查是否有usageDetails数据，如果有则添加可视化
        has_usage_data = False
        for item in response_list:
            if isinstance(item, dict) and item.get("usageDetails"):
                has_usage_data = True
                break
        
        if has_usage_data:
            logging2.info("检测到主副卡套餐使用量数据，添加可视化渲染")
            final_response = add_visualization_data("primarySecondaryCardUsage", final_response, "主副卡套餐使用量查询", "主副卡套餐使用量详细信息")
        
        logging2.info(f"主副卡套餐使用量查询完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error", 
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理主副卡套餐使用量查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error", 
            "message": "服务器内部错误"
        }), 500


# 上网详单查询接口
@app.route('/query/internet_detail_service', methods=['POST'])
def internet_detail_service():
    """
    上网详单查询接口
    调用OPENAPI的清单查询（稽核）接口，返回基站、RG、上网地、开始时间等关键信息
    """
    try:
        # 解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"上网详单查询接口请求参数: {data}")
        json_dict = json.loads(data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')

            # 基础参数校验
            if not billing_nb or not billing_cycle or not latn_id:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、billing_cycle 或 latn_id",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始上网详单查询: billing_nb={billing_nb}, billing_cycle={billing_cycle}, latn_id={latn_id}")

            # 生成startDate和endDate
            start_date = int(billing_cycle + "01")  # 月初第一天
            end_date = int(UnitTool.get_last_day_of_month(billing_cycle))  # 月末最后一天

            # 构建OPENAPI请求参数
            request_payload = {
                "endDate": end_date,
                "operAttrStruct": {
                    "operServiceId": "string",
                    "lanId": int(latn_id),
                    "operPost": 1,
                    "operOrgId": 0,
                    "staffId": 0,
                    "operTime": "string"
                },
                "isDesensitive": "0",
                "svcObjectStruct": {
                    "objValue": billing_nb,
                    "objAttr": "2",
                    "objType": "3",
                    "dataArea": "2"
                },
                "qryType": "3",
                "startDate": start_date
            }

            logging2.info(f"调用OPENAPI清单查询接口，请求参数: {request_payload}")

            # 调用OPENAPI接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("RtBillItem")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到RtBillItem接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
            
            try:
                result = UnitTool.send_post_request(url, request_payload)
                logging2.info(f"OPENAPI接口返回结果: {result}")

                if not result:
                    response_list.append({
                        "status": "error",
                        "message": "调用OPENAPI接口失败",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 检查返回结果
                if result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"OPENAPI接口返回错误: {result.get('resultMsg', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理返回数据
                response_data = _process_internet_detail_data(result, billing_nb, billing_cycle)
                response_list.append(response_data)

            except Exception as api_ex:
                logging2.error(f"调用OPENAPI接口时出错: {str(api_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用OPENAPI接口异常: {str(api_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "上网详单查询完成",
            "input_details": response_list
        }
        
        # 检查是否有任何包含Daysum数据的记录，如果有则添加可视化
        has_daysum_data = False
        for response_item in response_list:
            if isinstance(response_item, dict) and response_item.get("status") == "success":
                daysum_data = response_item.get("Daysum", [])
                if daysum_data and len(daysum_data) > 0:
                    has_daysum_data = True
                    break
        
        if has_daysum_data:
            logging2.info("检测到上网详单Daysum数据，添加可视化渲染")
            final_response = add_visualization_data("internet_detail_service", final_response, "上网详单查询", "上网详单按日分析")
        
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理上网详单查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 查询账单费用接口
@app.route('/query/qryJzrzDayFeeBill', methods=['POST'])
def qry_jzrz_day_fee_bill():
    """
    查询账单费用接口
    调用OPENAPI的查询账单费用接口，根据ACCT_TYPE过滤数据，并验证主副卡关系
    """
    try:
        # 解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"查询账单费用接口请求参数: {data}")
        json_dict = json.loads(data)

        # 获取请求参数
        acct_id = json_dict.get('ACCT_ID')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('LATN_ID')
        acct_type = json_dict.get('ACCT_TYPE')
        prod_inst_id = json_dict.get('PROD_INST_ID')  

        # 基础参数校验
        if not acct_id or not billing_cycle or not latn_id or not acct_type or not prod_inst_id:
            return jsonify({
                "status": "error",
                "message": "缺少必要参数 ACCT_ID、billing_cycle、LATN_ID、ACCT_TYPE 或 PROD_INST_ID"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "error",
                "message": "无效的 billing_cycle 格式，应为YYYYMM格式"
            })

        # 验证ACCT_TYPE参数
        if acct_type not in ['1', '2', '3', '4', '5']:
            return jsonify({
                "status": "error",
                "message": "无效的 ACCT_TYPE，应为1-5之间的数字"
            })

        logging2.info(f"开始查询账单费用: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}, PROD_INST_ID={prod_inst_id}")

        # 构建OPENAPI请求参数
        request_payload = {
            "ACCT_ID": str(acct_id),
            "BILLING_CYCLE_ID": str(billing_cycle),
            "LATN_ID": str(latn_id),
            "QRY_TYPE": "0",
            "QUWERY_KIND_TYPE": "1",
            "DHZ": "",
            "IS_GROUP": 0,
            "QUERY_TYPE": "0",
            "ACCT_LATN_ID": str(latn_id),
            "QRYZERO": 0,
            "QRY_INVALID_FLAG": "0"
        }

        logging2.info(f"调用OPENAPI查询账单费用接口，请求参数: {request_payload}")

        # 调用OPENAPI接口
        config_path = get_config_path()
        if not config_path:
            return jsonify({
                "status": "error",
                "message": "找不到配置文件config.json"
            })
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("qryJzrzDayFeeBill")
        if not url:
            return jsonify({
                "status": "error",
                "message": "配置中未找到qryJzrzDayFeeBill接口URL"
            })
        
        try:
            result = UnitTool.send_post_request(url, request_payload)
            logging2.info(f"OPENAPI接口返回结果: {result}")

            if not result:
                return jsonify({
                    "status": "error",
                    "message": "调用OPENAPI接口失败"
                })

            # 检查返回结果
            if result.get("resultCode") != "0":
                return jsonify({
                    "status": "error",
                    "message": f"OPENAPI接口返回错误: {result.get('resultMsg', '未知错误')}"
                })

            # 处理返回数据，传入PROD_INST_ID参数
            response_data = _process_fee_bill_data(result, acct_type, latn_id, prod_inst_id)
            
            # 检查是否有feeInfoList数据，如果有则添加可视化
            fee_info_list = response_data.get("feeInfoList", [])
            if fee_info_list:
                logging2.info("检测到账单费用数据，添加可视化渲染")
                response_data = add_visualization_data("qryJzrzDayFeeBill", response_data, "账单费用查询", "账单费用详细信息")
            
            return jsonify(response_data)

        except Exception as api_ex:
            logging2.error(f"调用OPENAPI接口时出错: {str(api_ex)}")
            return jsonify({
                "status": "error",
                "message": f"调用OPENAPI接口异常: {str(api_ex)}"
            })

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询账单费用请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 主副卡套外使用量接口
@app.route('/query/primary_secondary_card_overflow_usage', methods=['POST'])
def primary_secondary_card_overflow_usage():
    """
    主副卡套外使用量查询接口
    查询主副卡关系，分别查询主卡和副卡的套外使用量，并合并去重
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"主副卡套外使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')

            if not billing_nb or not prod_inst_id or not latn_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始查询主副卡套外使用量: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, latn_id={latn_id}, billing_cycle={billing_cycle}")

            # 查询主副卡关系，分别收集主卡和副卡的产品实例ID和号码
            main_card_data = []  # 主卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            sub_card_data = []   # 副卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            replacements = [['##LATNID##', str(latn_id)]]
            
            try:
                # 查询QueryProdInstRel_a_100800获取a_prod_inst_id
                params = [prod_inst_id, prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )
                
                if a_rows:
                    for row in a_rows:
                        a_prod_inst_id = row[0] if row else None
                        if a_prod_inst_id:
                            # 查询QueryProdInstRel_100800获取主副卡关系
                            params2 = [a_prod_inst_id]
                            a1_rows = db_manager_instance.excute_sql(
                                sql_name='QueryProdInstRel_100800',
                                params=params2,
                                lst_replace_code_value=replacements
                            )
                            
                            if a1_rows:
                                # 收集主副卡的产品实例ID
                                for a1_row in a1_rows:
                                    if len(a1_row) >= 2:
                                        main_prod_inst_id = a1_row[0]  # a_prod_inst_id (主卡)
                                        sub_prod_inst_id = a1_row[1]   # z_prod_inst_id (副卡)
                                        
                                        # 查询主卡号码
                                        if main_prod_inst_id:
                                            params3 = [main_prod_inst_id]
                                            main_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params3,
                                                lst_replace_code_value=replacements
                                            )
                                            if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                                main_card_data.append({
                                                    "prod_inst_id": str(main_prod_inst_id),
                                                    "acc_nbr": main_acc_rows[0][0]
                                                })
                                        
                                        # 查询副卡号码
                                        if sub_prod_inst_id:
                                            params4 = [sub_prod_inst_id]
                                            sub_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params4,
                                                lst_replace_code_value=replacements
                                            )
                                            if sub_acc_rows and sub_acc_rows[0] and sub_acc_rows[0][0]:
                                                sub_card_data.append({
                                                    "prod_inst_id": str(sub_prod_inst_id),
                                                    "acc_nbr": sub_acc_rows[0][0]
                                                })
                            else:
                                # 如果没有主副卡关系，当前产品实例作为主卡处理
                                params3 = [a_prod_inst_id]
                                main_acc_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAccNumByProdInstId',
                                    params=params3,
                                    lst_replace_code_value=replacements
                                )
                                if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                    main_card_data.append({
                                        "prod_inst_id": str(a_prod_inst_id),
                                        "acc_nbr": main_acc_rows[0][0]
                                    })
                else:
                    # 如果没有查到主副卡关系，直接使用传入的prod_inst_id和billing_nb作为主卡
                    main_card_data.append({
                        "prod_inst_id": str(prod_inst_id),
                        "acc_nbr": billing_nb
                    })
                
                # 对主卡和副卡数据进行去重处理：使用字典来去重，以prod_inst_id和acc_nbr的组合作为唯一键
                main_card_unique = {}
                for card in main_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    main_card_unique[key] = card
                main_card_data = list(main_card_unique.values())
                
                sub_card_unique = {}
                for card in sub_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    sub_card_unique[key] = card
                sub_card_data = list(sub_card_unique.values())
                    
                logging2.info(f"查询到主卡数据(去重后): {main_card_data}")
                logging2.info(f"查询到副卡数据(去重后): {sub_card_data}")
                    
            except Exception as e:
                logging2.error(f"查询主副卡关系时出错: {str(e)}")
                # 如果查询出错，则只使用传入的数据作为主卡
                main_card_data.append({
                    "prod_inst_id": str(prod_inst_id),
                    "acc_nbr": billing_nb
                })

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("AccuUseQryCenter")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 存储所有查询结果，用于最后去重
            all_offer_info = {}  # 使用字典存储，key为(offerId, offerInstId)，value为offer_info
            
            # 查询主卡数据：只提取unitTypeId=3的数据
            logging2.info("开始查询主卡数据...")
            for main_card in main_card_data:
                acc_nbr = main_card["acc_nbr"]
                
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询主卡，号码: {acc_nbr}，请求参数: {request_payload}")

                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"主卡AccuUseQryCenter接口返回结果: {result}")

                    if result and result.get("resultCode") == "0":
                        # 提取主卡数据，只要unitTypeId=3的数据
                        for offer_info in result.get("offerInstInfo", []):
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            eff_date = offer_info.get("EFF_DATE")
                            exp_date = offer_info.get("EXP_DATE")
                            calc_priority = offer_info.get("CALC_PRIORITY")
                            
                            # 过滤accuQryList中unitTypeId为3的数据
                            filtered_accu_list = []
                            for accu_info in offer_info.get("accuQryList", []):
                                unit_type_id = str(accu_info.get("unitTypeId", ""))
                                if unit_type_id == "3":
                                    filtered_accu_list.append({
                                        "accuTypeId": accu_info.get("accuTypeId"),
                                        "accuTypeName": accu_info.get("accuTypeName"),
                                        "beginTime": accu_info.get("beginTime"),
                                        "endTime": accu_info.get("endTime"),
                                        "usageVal": accu_info.get("usageVal"),
                                        "unitTypeId": accu_info.get("unitTypeId")
                                    })
                            
                            # 只有当过滤后的列表不为空时才添加
                            if filtered_accu_list:
                                key = (offer_id, offer_inst_id)
                                all_offer_info[key] = {
                                    "accNbr": acc_nbr,
                                    "offerId": offer_id,
                                    "offerInstId": offer_inst_id,
                                    "EFF_DATE": eff_date,
                                    "EXP_DATE": exp_date,
                                    "CALC_PRIORITY": calc_priority,
                                    "accuQryList": filtered_accu_list,
                                    "cardType": "主卡"
                                }
                                logging2.info(f"添加主卡数据: offerId={offer_id}, offerInstId={offer_inst_id}")
                    else:
                        logging2.warning(f"主卡AccuUseQryCenter接口调用失败，号码: {acc_nbr}，错误信息: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")
                        
                except Exception as req_ex:
                    logging2.error(f"请求主卡号码 {acc_nbr} 时出错: {str(req_ex)}")

            # 查询副卡数据：提取offerId=620001和unitTypeId=3的数据
            logging2.info("开始查询副卡数据...")
            for sub_card in sub_card_data:
                acc_nbr = sub_card["acc_nbr"]
                
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询副卡，号码: {acc_nbr}，请求参数: {request_payload}")

                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"副卡AccuUseQryCenter接口返回结果: {result}")

                    if result and result.get("resultCode") == "0":
                        # 提取副卡数据，要offerId=620001或unitTypeId=3的数据
                        for offer_info in result.get("offerInstInfo", []):
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            eff_date = offer_info.get("EFF_DATE")
                            exp_date = offer_info.get("EXP_DATE")
                            calc_priority = offer_info.get("CALC_PRIORITY")
                            
                            # 检查是否满足副卡条件：offerId=620001 或 包含unitTypeId=3的数据
                            should_include = False
                            filtered_accu_list = []
                            
                            if str(offer_id) == "620001":
                                # offerId=620001的数据，提取所有accuQryList
                                should_include = True
                                for accu_info in offer_info.get("accuQryList", []):
                                    filtered_accu_list.append({
                                        "accuTypeId": accu_info.get("accuTypeId"),
                                        "accuTypeName": accu_info.get("accuTypeName"),
                                        "beginTime": accu_info.get("beginTime"),
                                        "endTime": accu_info.get("endTime"),
                                        "usageVal": accu_info.get("usageVal"),
                                        "unitTypeId": accu_info.get("unitTypeId")
                                    })
                            else:
                                # 其他offerId，只提取unitTypeId=3的数据
                                for accu_info in offer_info.get("accuQryList", []):
                                    unit_type_id = str(accu_info.get("unitTypeId", ""))
                                    if unit_type_id == "3":
                                        should_include = True
                                        filtered_accu_list.append({
                                            "accuTypeId": accu_info.get("accuTypeId"),
                                            "accuTypeName": accu_info.get("accuTypeName"),
                                            "beginTime": accu_info.get("beginTime"),
                                            "endTime": accu_info.get("endTime"),
                                            "usageVal": accu_info.get("usageVal"),
                                            "unitTypeId": accu_info.get("unitTypeId")
                                        })
                            
                            # 只有当满足条件且过滤后的列表不为空时才添加
                            if should_include and filtered_accu_list:
                                if str(offer_id) == "620001":
                                    # offerId=620001的数据：副卡独立保留，不合并
                                    key = (acc_nbr, offer_id, offer_inst_id)
                                    all_offer_info[key] = {
                                        "accNbr": acc_nbr,
                                        "offerId": offer_id,
                                        "offerInstId": offer_inst_id,
                                        "EFF_DATE": eff_date,
                                        "EXP_DATE": exp_date,
                                        "CALC_PRIORITY": calc_priority,
                                        "accuQryList": filtered_accu_list,
                                        "cardType": "副卡"
                                    }
                                    logging2.info(f"副卡独立保留offerId=620001数据: 号码={acc_nbr}, offerId={offer_id}, offerInstId={offer_inst_id}")
                                else:
                                    # 其他offerId：检查能否合并到主卡
                                    merge_key = (offer_id, offer_inst_id)
                                    
                                    if merge_key in all_offer_info:
                                        # 能合并到主卡：进行去重处理
                                        existing_accu_list = all_offer_info[merge_key]["accuQryList"]
                                        existing_accu_keys = set()
                                        for accu in existing_accu_list:
                                            accu_key = (accu.get("accuTypeId"), accu.get("beginTime"), accu.get("endTime"))
                                            existing_accu_keys.add(accu_key)
                                        
                                        # 添加新的不重复的accu数据
                                        for new_accu in filtered_accu_list:
                                            new_accu_key = (new_accu.get("accuTypeId"), new_accu.get("beginTime"), new_accu.get("endTime"))
                                            if new_accu_key not in existing_accu_keys:
                                                existing_accu_list.append(new_accu)
                                                existing_accu_keys.add(new_accu_key)
                                        
                                        logging2.info(f"副卡数据合并到主卡（去重）: offerId={offer_id}, offerInstId={offer_inst_id}")
                                    else:
                                        # 不能合并到主卡：副卡独立保留
                                        sub_key = (acc_nbr, offer_id, offer_inst_id)
                                        all_offer_info[sub_key] = {
                                            "accNbr": acc_nbr,
                                            "offerId": offer_id,
                                            "offerInstId": offer_inst_id,
                                            "EFF_DATE": eff_date,
                                            "EXP_DATE": exp_date,
                                            "CALC_PRIORITY": calc_priority,
                                            "accuQryList": filtered_accu_list,
                                            "cardType": "副卡"
                                        }
                                        logging2.info(f"副卡独立保留无法合并的数据: 号码={acc_nbr}, offerId={offer_id}, offerInstId={offer_inst_id}")
                    else:
                        logging2.warning(f"副卡AccuUseQryCenter接口调用失败，号码: {acc_nbr}，错误信息: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")
                        
                except Exception as req_ex:
                    logging2.error(f"请求副卡号码 {acc_nbr} 时出错: {str(req_ex)}")

            # 将去重后的结果转换为列表
            all_overflow_usage = list(all_offer_info.values())
            
            logging2.info(f"合并去重后的结果数量: {len(all_overflow_usage)}")

            # 添加单个用户的查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "latn_id": latn_id,
                "overflowUsageDetails": all_overflow_usage
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "主副卡套外使用量查询完成",
            "input_details": response_list
        }
        
        # 检查是否有overflowUsageDetails数据，如果有则添加可视化
        # has_overflow_data = False
        # for item in response_list:
        #     if isinstance(item, dict) and item.get("overflowUsageDetails"):
        #         has_overflow_data = True
        #         break
        
        # if has_overflow_data:
        #     logging2.info("检测到套外使用量数据，添加可视化渲染")
        #     final_response = add_visualization_data("primary_secondary_card_overflow_usage", final_response, "主副卡使用量查询", "主副卡使用量详细信息")
        
        logging2.info(f"主副卡套外使用量查询完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理主副卡套外使用量查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 查询号码加入共享流量包的时间接口
@app.route('/query/shared_flow_package_time', methods=['POST'])
def shared_flow_package_time():
    """
    查询号码加入共享流量包的时间接口
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询共享流量包时间接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')

            if not billing_nb or not prod_inst_id or not latn_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始查询共享流量包时间: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, latn_id={latn_id}, billing_cycle={billing_cycle}")

            # 调用AccuUseQryCenter接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("AccuUseQryCenter")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 构建请求数据
            request_payload = {
                "operAttrStruct": {
                    "lanId": 0,
                    "operOrgId": 0,
                    "operPost": 0,
                    "operServiceId": "string",
                    "operTime": "string",
                    "staffId": 0
                },
                "accNbr": billing_nb,
                "destinationAttr": "2",
                "billingCycle": int(billing_cycle),
                "offerId": 0,
                "qryType": 2,
                "offerInstStr": 0,
                "acctId": 0
            }

            logging2.info(f"调用AccuUseQryCenter接口，请求参数: {request_payload}")

            # 发送请求并处理结果
            try:
                result = UnitTool.send_post_request(url, request_payload)
                logging2.info(f"AccuUseQryCenter接口返回结果: {result}")

                if not result or result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"AccuUseQryCenter接口调用失败: {result.get('resultMsg', '未知错误') if result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 提取共享定价实例的offerInstId集合
                shared_offer_inst_ids = []
                for offer_info in result.get("offerInstInfo", []):
                    offer_inst_id = offer_info.get("offerInstId")
                    accu_qry_list = offer_info.get("accuQryList", [])
                    
                    # 检查是否有ownerType=2的共享定价实例
                    has_shared_pricing = any(
                        accu.get("ownerType") == 2 
                        for accu in accu_qry_list
                    )
                    
                    if has_shared_pricing and offer_inst_id:
                        shared_offer_inst_ids.append(offer_inst_id)

                logging2.info(f"找到共享定价实例ID集合: {shared_offer_inst_ids}")

                # 查询每个共享定价实例的加入时间
                shared_package_times = []
                replacements = [['##LATNID##', str(latn_id)]]
                
                for offer_inst_id in shared_offer_inst_ids:
                    try:
                        params = [offer_inst_id, prod_inst_id]
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QuerySharedFlowPackageTime',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        
                        if result_rows and result_rows[0] and result_rows[0][0]:
                            update_date = result_rows[0][0]
                            shared_package_times.append({
                                "offerInstId": offer_inst_id,
                                "update_date": update_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(update_date, 'strftime') else str(update_date)
                            })
                            logging2.info(f"查询到共享流量包时间: offerInstId={offer_inst_id}, update_date={update_date}")
                        else:
                            logging2.warning(f"未查询到共享流量包时间: offerInstId={offer_inst_id}")
                            
                    except Exception as db_ex:
                        logging2.error(f"查询共享流量包时间出错: offerInstId={offer_inst_id}, 错误: {str(db_ex)}")

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "PROD_INST_ID": prod_inst_id,
                    "latn_id": latn_id,
                    "sharedPackageTimes": shared_package_times
                })

            except Exception as req_ex:
                logging2.error(f"调用AccuUseQryCenter接口时出错: {str(req_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用AccuUseQryCenter接口异常: {str(req_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询共享流量包时间完成",
            "input_details": response_list
        }
        
        logging2.info(f"查询共享流量包时间完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询共享流量包时间请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 查实时费用表、账单表判断资费销售品接口
@app.route('/query/billing_offer_ids_service', methods=['POST'])
def billing_offer_ids_service():
    """
    查实时费用表、账单表判断资费销售品接口
    根据账期类型（1+1账期或其他月份）查询不同的表
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询资费销售品接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            prod_inst_id = row.get('PROD_INST_ID')
            billing_cycle_str = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 需要latn_id来替换SQL中的##LATNID##

            if not prod_inst_id or not billing_cycle_str:
                logging2.warning("缺少必要参数 PROD_INST_ID 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 PROD_INST_ID 或 billing_cycle",
                    "PROD_INST_ID": prod_inst_id
                })
                continue

            if not latn_id:
                logging2.warning("缺少必要参数 latn_id")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 latn_id",
                    "PROD_INST_ID": prod_inst_id
                })
                continue

            logging2.info(f"开始查询资费销售品: PROD_INST_ID={prod_inst_id}, billing_cycle={billing_cycle_str}, latn_id={latn_id}")

            # 解析billing_cycle，支持多个账期用逗号分隔
            billing_cycles = [cycle.strip() for cycle in billing_cycle_str.split(',')]
            
            # 获取当前年月和上一个月（1+1账期）
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            if now.month == 1:
                prev_ym = f"{now.year-1}12"
            else:
                prev_ym = f"{now.year}{now.month-1:02d}"
            
            # 定义1+1账期集合
            one_plus_one_cycles = {current_ym, prev_ym}
            
            logging2.info(f"1+1账期: {one_plus_one_cycles}, 查询账期: {billing_cycles}")

            offer_id_list = []
            
            # 处理每个账期
            for billing_cycle in billing_cycles:
                # 验证账期格式
                if len(billing_cycle) != 6 or not billing_cycle.isdigit():
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    continue
                
                logging2.info(f"处理账期: {billing_cycle}")
                
                try:
                    if billing_cycle in one_plus_one_cycles:
                        # 1+1账期：查询实时费用表
                        logging2.info(f"账期 {billing_cycle} 属于1+1账期，查询实时费用表")
                        month_mm = billing_cycle[4:]
                        replacements = [
                            ['##LATNID##', str(latn_id)],
                            ['##MM##', month_mm]
                        ]
                        params = [prod_inst_id]
                        
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QueryRealFeeOfferIds',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                    else:
                        # 其他月份：查询账单表
                        logging2.info(f"账期 {billing_cycle} 不属于1+1账期，查询账单表")
                        replacements = [
                            ['##LATNID##', str(latn_id)],
                            ['##BILLINGCYCLE##', billing_cycle]
                        ]
                        params = [prod_inst_id]
                        
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QueryAcctItemOfferIds',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                    
                    # 处理查询结果
                    for result_row in result_rows:
                        if result_row and result_row[0]:
                            ofr_id = result_row[0]
                            offer_id_list.append({
                                "billing_cycle": int(billing_cycle),
                                "ofrid": ofr_id
                            })
                            logging2.info(f"账期 {billing_cycle} 查询到 ofr_id: {ofr_id}")
                    
                    if not result_rows:
                        logging2.info(f"账期 {billing_cycle} 未查询到相关数据")
                        
                except Exception as db_ex:
                    logging2.error(f"查询账期 {billing_cycle} 时出错: {str(db_ex)}")

            # 添加查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "PROD_INST_ID": prod_inst_id,
                "latn_id": latn_id,
                "offeridlist": offer_id_list
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询资费销售品完成",
            "input_details": response_list
        }
        
        logging2.info(f"查询资费销售品完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询资费销售品请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 用户热点事件接口
@app.route('/query/user_trending_events', methods=['POST'])
def user_trending_events():
    """
    用户热点事件查询接口
    根据用户号码和账期查询用户的热点事件信息
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"用户热点事件接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取请求参数
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')

        # 基础参数校验
        if not billing_nb or not billing_cycle:
            return jsonify({
                "status": "error",
                "message": "缺少必要参数 billing_nb 或 billing_cycle"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "error",
                "message": "无效的 billing_cycle 格式，应为YYYYMM格式"
            })

        logging2.info(f"开始查询用户热点事件: billing_nb={billing_nb}, billing_cycle={billing_cycle}")

        # 提取月份 MM
        month_mm = billing_cycle[4:]

        # 查询用户热点事件
        replacements = [['##MM##', month_mm]]
        params = [billing_nb]
        
        try:
            result_rows = db_manager_instance.excute_sql(
                sql_name='QueryUserTrendingEvents',
                params=params,
                lst_replace_code_value=replacements
            )
            
            logging2.info(f"查询到用户热点事件数据: {len(result_rows) if result_rows else 0} 条")
            
        except Exception as db_ex:
            logging2.error(f"查询用户热点事件时出错: {str(db_ex)}")
            return jsonify({
                "status": "error",
                "message": f"查询数据库时出错: {str(db_ex)}"
            })

        # 处理查询结果
        user_trending_events_list = []
        
        if result_rows:
            for row in result_rows:
                params_str, send_content, business_id, create_time = row
                
                # 格式化时间
                if create_time:
                    if hasattr(create_time, 'strftime'):
                        formatted_time = create_time.strftime('%Y-%m-%d %H:%M')
                    else:
                        formatted_time = str(create_time)
                else:
                    formatted_time = ""
                
                # 确定Content内容
                if send_content and send_content.strip():
                    # 如果send_content有内容，直接使用
                    content = send_content.strip()
                else:
                    # 如果send_content为空，解析params
                    content = _parse_params_content(params_str, business_id)
                
                # 只有当content不是"参数为空"时才添加到结果列表中
                if content != "参数为空":
                    user_trending_events_list.append({
                        "CreateTime": formatted_time,
                        "Content": content
                    })
        
        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "UserTrendingEventslist": user_trending_events_list
        }
        
        logging2.info(f"用户热点事件查询完成，返回结果: {response_data}")
        return jsonify(response_data)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理用户热点事件查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 查询主副卡使用量接口
@app.route('/query/main_secondary_card_usage', methods=['POST'])
def main_secondary_card_usage():
    """
    查询主副卡使用量接口
    根据主副卡关系查询AccuUseQryCenter和userResourceQueryDetailBon接口，返回指定格式数据
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询主副卡使用量接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 可选参数

            if not billing_nb or not prod_inst_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 如果没有提供latn_id，尝试通过billing_nb查询
            if not latn_id:
                latn_id, error_msg = get_latn_id_if_missing(billing_nb, None)
                if not latn_id:
                    logging2.error(f"无法获取latn_id: {error_msg}")
                    response_list.append({
                        "status": "error",
                        "message": f"无法获取本地网ID: {error_msg}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue
                logging2.info(f"通过billing_nb查询到latn_id: {latn_id}")

            logging2.info(f"开始查询主副卡使用量: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, billing_cycle={billing_cycle}, latn_id={latn_id}")

            # 查询主副卡关系，分别收集主卡和副卡的产品实例ID和号码
            main_card_data = []  # 主卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            sub_card_data = []   # 副卡数据: [{"prod_inst_id": xxx, "acc_nbr": xxx}]
            replacements = [['##LATNID##', str(latn_id)]]
            
            try:
                # 查询QueryProdInstRel_a_100800获取a_prod_inst_id
                params = [prod_inst_id, prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )
                
                if a_rows:
                    for row in a_rows:
                        a_prod_inst_id = row[0] if row else None
                        if a_prod_inst_id:
                            # 查询QueryProdInstRel_100800获取主副卡关系
                            params2 = [a_prod_inst_id]
                            a1_rows = db_manager_instance.excute_sql(
                                sql_name='QueryProdInstRel_100800',
                                params=params2,
                                lst_replace_code_value=replacements
                            )
                            
                            if a1_rows:
                                # 收集主副卡的产品实例ID
                                for a1_row in a1_rows:
                                    if len(a1_row) >= 2:
                                        main_prod_inst_id = a1_row[0]  # a_prod_inst_id (主卡)
                                        sub_prod_inst_id = a1_row[1]   # z_prod_inst_id (副卡)
                                        
                                        # 查询主卡号码
                                        if main_prod_inst_id:
                                            params3 = [main_prod_inst_id]
                                            main_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params3,
                                                lst_replace_code_value=replacements
                                            )
                                            if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                                main_card_data.append({
                                                    "prod_inst_id": str(main_prod_inst_id),
                                                    "acc_nbr": main_acc_rows[0][0]
                                                })
                                        
                                        # 查询副卡号码
                                        if sub_prod_inst_id:
                                            params4 = [sub_prod_inst_id]
                                            sub_acc_rows = db_manager_instance.excute_sql(
                                                sql_name='QueryAccNumByProdInstId',
                                                params=params4,
                                                lst_replace_code_value=replacements
                                            )
                                            if sub_acc_rows and sub_acc_rows[0] and sub_acc_rows[0][0]:
                                                sub_card_data.append({
                                                    "prod_inst_id": str(sub_prod_inst_id),
                                                    "acc_nbr": sub_acc_rows[0][0]
                                                })
                            else:
                                # 如果没有主副卡关系，当前产品实例作为主卡处理
                                params3 = [a_prod_inst_id]
                                main_acc_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAccNumByProdInstId',
                                    params=params3,
                                    lst_replace_code_value=replacements
                                )
                                if main_acc_rows and main_acc_rows[0] and main_acc_rows[0][0]:
                                    main_card_data.append({
                                        "prod_inst_id": str(a_prod_inst_id),
                                        "acc_nbr": main_acc_rows[0][0]
                                    })
                else:
                    # 如果没有查到主副卡关系，直接使用传入的prod_inst_id和billing_nb作为主卡
                    main_card_data.append({
                        "prod_inst_id": str(prod_inst_id),
                        "acc_nbr": billing_nb
                    })
                
                # 对主卡和副卡数据进行去重处理：使用字典来去重，以prod_inst_id和acc_nbr的组合作为唯一键
                main_card_unique = {}
                for card in main_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    main_card_unique[key] = card
                main_card_data = list(main_card_unique.values())
                
                sub_card_unique = {}
                for card in sub_card_data:
                    key = f"{card['prod_inst_id']}_{card['acc_nbr']}"
                    sub_card_unique[key] = card
                sub_card_data = list(sub_card_unique.values())
                    
                logging2.info(f"查询到主卡数据(去重后): {main_card_data}")
                logging2.info(f"查询到副卡数据(去重后): {sub_card_data}")
                    
            except Exception as e:
                logging2.error(f"查询主副卡关系时出错: {str(e)}")
                # 如果查询出错，则只使用传入的数据作为主卡
                main_card_data.append({
                    "prod_inst_id": str(prod_inst_id),
                    "acc_nbr": billing_nb
                })

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            accu_use_url = config_loader.get_url("AccuUseQryCenter")
            user_resource_url = config_loader.get_url("userResourceQueryDetailBon")
            
            if not accu_use_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            if not user_resource_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到userResourceQueryDetailBon接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 存储所有查询结果
            all_usage_data = []
            
            # 用于去重userResourceQueryDetailBon接口调用的集合
            queried_user_resource_keys = set()
            
            # 处理主卡数据
            for card in main_card_data:
                acc_nbr = card["acc_nbr"]
                
                # 构建AccuUseQryCenter请求数据
                accu_request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询主卡号码: {acc_nbr}，请求参数: {accu_request_payload}")

                try:
                    accu_result = UnitTool.send_post_request(accu_use_url, accu_request_payload)
                    logging2.info(f"AccuUseQryCenter接口返回结果(主卡): {accu_result}")

                    if accu_result and accu_result.get("resultCode") == "0":
                        offer_inst_infos = accu_result.get("offerInstInfo", [])
                        
                        for offer_info in offer_inst_infos:
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            accu_qry_list = offer_info.get("accuQryList", [])
                            
                            # 处理offerId=620001的数据
                            if offer_id == 620001:
                                for accu_item in accu_qry_list:
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    all_usage_data.append({
                                        "accNbr": acc_nbr,
                                        "offerInstId": offer_inst_id,
                                        "offerId": offer_id,
                                        "accuTypeId": accu_type_id,
                                        "accuTypeAttr": accu_type_attr,
                                        "usageAmount": usage_val,
                                        "accuTypeName": accu_type_name
                                    })
                            
                            # 处理unitTypeId=3的数据
                            for accu_item in accu_qry_list:
                                unit_type_id = accu_item.get("unitTypeId")
                                if str(unit_type_id) == "3":
                                    owner_type = accu_item.get("ownerType")
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    if owner_type == 2:
                                        # 创建去重键：accNbr + offerInstId
                                        dedup_key = f"{acc_nbr}_{offer_inst_id}"
                                        
                                        # 检查是否已经查询过相同的accNbr和offerInstId组合
                                        if dedup_key not in queried_user_resource_keys:
                                            queried_user_resource_keys.add(dedup_key)
                                            
                                            # ownerType=2，调用userResourceQueryDetailBon接口
                                            user_resource_payload = {
                                                "operAttrStruct": {
                                                    "lanId": 0,
                                                    "operOrgId": 0,
                                                    "operPost": 0,
                                                    "operServiceId": "string",
                                                    "operTime": "string",
                                                    "staffId": 11111111111
                                                },
                                                "svcObjectStruct": {
                                                    "objType": "3",
                                                    "objValue": acc_nbr,
                                                    "objAttr": "2",
                                                    "dataArea": "2"
                                                },
                                                "queryFlag": "3",
                                                "billingCycle": int(billing_cycle),
                                                "offerInstId": str(offer_inst_id)
                                            }
                                            
                                            logging2.info(f"调用userResourceQueryDetailBon接口，主卡号码: {acc_nbr}，offerInstId: {offer_inst_id}")
                                            
                                            try:
                                                user_resource_result = UnitTool.send_post_request(user_resource_url, user_resource_payload)
                                                logging2.info(f"userResourceQueryDetailBon接口返回结果(主卡): {user_resource_result}")
                                                
                                                if user_resource_result and user_resource_result.get("resultCode") == "0":
                                                    detail_offer_inst_infos = user_resource_result.get("detailOfferInstInfo", [])
                                                    
                                                    for detail_offer_info in detail_offer_inst_infos:
                                                        detail_offer_id = detail_offer_info.get("offerId")
                                                        detail_offer_inst_id = detail_offer_info.get("offerInstId")
                                                        accu_qry_user_list = detail_offer_info.get("accuQryUserList", [])
                                                        
                                                        for user_info in accu_qry_user_list:
                                                            user_unit_type_id = user_info.get("unitTypeId")
                                                            if str(user_unit_type_id) == "3":
                                                                user_acc_nbr = user_info.get("accNbr")
                                                                user_accu_type_id = user_info.get("accuTypeId")
                                                                user_accu_type_attr = user_info.get("accuTypeAttr")
                                                                user_usage_amount = user_info.get("usageAmount")
                                                                
                                                                all_usage_data.append({
                                                                    "accNbr": user_acc_nbr,
                                                                    "offerInstId": detail_offer_inst_id,
                                                                    "offerId": detail_offer_id,
                                                                    "accuTypeId": user_accu_type_id,
                                                                    "accuTypeAttr": user_accu_type_attr,
                                                                    "usageAmount": user_usage_amount,
                                                                    "accuTypeName": accu_type_name  # 使用第一个接口的accuTypeName
                                                                })
                                            except Exception as user_resource_ex:
                                                logging2.error(f"调用userResourceQueryDetailBon接口失败(主卡): {str(user_resource_ex)}")
                                        else:
                                            logging2.info(f"跳过重复查询userResourceQueryDetailBon接口，主卡号码: {acc_nbr}，offerInstId: {offer_inst_id}")
                                    
                                    elif owner_type == 1:
                                        # ownerType=1，直接从原接口返回数据
                                        all_usage_data.append({
                                            "accNbr": acc_nbr,
                                            "offerInstId": offer_inst_id,
                                            "offerId": offer_id,
                                            "accuTypeId": accu_type_id,
                                            "accuTypeAttr": accu_type_attr,
                                            "usageAmount": usage_val,
                                            "accuTypeName": accu_type_name
                                        })
                        
                except Exception as accu_ex:
                    logging2.error(f"调用AccuUseQryCenter接口失败(主卡): {str(accu_ex)}")
            
            # 处理副卡数据（不包含ownerType=2的特殊处理）
            for card in sub_card_data:
                acc_nbr = card["acc_nbr"]
                
                # 构建AccuUseQryCenter请求数据
                accu_request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 0
                    },
                    "accNbr": acc_nbr,
                    "destinationAttr": "2",
                    "billingCycle": int(billing_cycle),
                    "offerId": 0,
                    "qryType": 2,
                    "offerInstStr": 0,
                    "acctId": 0
                }

                logging2.info(f"调用AccuUseQryCenter接口查询副卡号码: {acc_nbr}，请求参数: {accu_request_payload}")

                try:
                    accu_result = UnitTool.send_post_request(accu_use_url, accu_request_payload)
                    logging2.info(f"AccuUseQryCenter接口返回结果(副卡): {accu_result}")

                    if accu_result and accu_result.get("resultCode") == "0":
                        offer_inst_infos = accu_result.get("offerInstInfo", [])
                        
                        for offer_info in offer_inst_infos:
                            offer_id = offer_info.get("offerId")
                            offer_inst_id = offer_info.get("offerInstId")
                            accu_qry_list = offer_info.get("accuQryList", [])
                            
                            # 处理offerId=620001的数据
                            if offer_id == 620001:
                                for accu_item in accu_qry_list:
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    all_usage_data.append({
                                        "accNbr": acc_nbr,
                                        "offerInstId": offer_inst_id,
                                        "offerId": offer_id,
                                        "accuTypeId": accu_type_id,
                                        "accuTypeAttr": accu_type_attr,
                                        "usageAmount": usage_val,
                                        "accuTypeName": accu_type_name
                                    })
                            
                            # 处理unitTypeId=3的数据（副卡只处理ownerType=1的情况）
                            for accu_item in accu_qry_list:
                                unit_type_id = accu_item.get("unitTypeId")
                                if str(unit_type_id) == "3":
                                    owner_type = accu_item.get("ownerType")
                                    accu_type_id = accu_item.get("accuTypeId")
                                    accu_type_attr = accu_item.get("accuTypeAttr")
                                    accu_type_name = accu_item.get("accuTypeName")
                                    usage_val = accu_item.get("usageVal")
                                    
                                    if owner_type == 1:
                                        # ownerType=1，直接从原接口返回数据
                                        all_usage_data.append({
                                            "accNbr": acc_nbr,
                                            "offerInstId": offer_inst_id,
                                            "offerId": offer_id,
                                            "accuTypeId": accu_type_id,
                                            "accuTypeAttr": accu_type_attr,
                                            "usageAmount": usage_val,
                                            "accuTypeName": accu_type_name
                                        })
                                    # 副卡不处理ownerType=2的情况
                        
                except Exception as accu_ex:
                    logging2.error(f"调用AccuUseQryCenter接口失败(副卡): {str(accu_ex)}")

            # 添加查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "PROD_INST_ID": prod_inst_id,
                "usage_data": all_usage_data
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询主副卡使用量完成",
            "input_details": response_list
        }
        
        # 检查是否有usage_data，有值才添加可视化数据
        has_usage_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                usage_data = detail.get("usage_data", [])
                if usage_data and len(usage_data) > 0:
                    has_usage_data = True
                    break
        
        logging2.info(f"查询主副卡使用量完成，返回结果: {final_response}")
        
        # 根据是否有usage_data决定是否添加可视化数据
        if has_usage_data:
            logging2.info("检测到使用量数据，添加可视化数据")
            return jsonify(add_visualization_data("main_secondary_card_usage", final_response, "主副卡使用量分析", ""))
        else:
            logging2.info("未检测到使用量数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询主副卡使用量请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 权益包订购与费用关系接口
@app.route('/query/benefits_package_fee_relation', methods=['POST'])
def benefits_package_fee_relation():
    """
    权益包订购与费用关系接口
    调用qryJzrzDayFeeBill和PricingQuery接口，整合返回数据
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"权益包订购与费用关系接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            acct_id = row.get('ACCT_ID')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('LATN_ID')
            acct_type = row.get('ACCT_TYPE')

            # 基础参数校验
            if not acct_id or not prod_inst_id or not billing_nb or not billing_cycle or not latn_id or not acct_type:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 ACCT_ID、PROD_INST_ID、billing_nb、billing_cycle、LATN_ID 或 ACCT_TYPE",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 验证ACCT_TYPE参数
            if acct_type not in ['1', '2', '3', '4', '5']:
                response_list.append({
                    "status": "error",
                    "message": "无效的 ACCT_TYPE，应为1-5之间的数字",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始权益包订购与费用关系查询: ACCT_ID={acct_id}, PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}")

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            qry_fee_url = config_loader.get_url("qryJzrzDayFeeBill")
            pricing_query_url = config_loader.get_url("PricingQuery")
            
            if not qry_fee_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到qryJzrzDayFeeBill接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            if not pricing_query_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到PricingQuery接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 调用qryJzrzDayFeeBill接口
            qry_fee_payload = {
                "ACCT_ID": str(acct_id),
                "BILLING_CYCLE_ID": str(billing_cycle),
                "LATN_ID": str(latn_id),
                "QRY_TYPE": "0",
                "QUWERY_KIND_TYPE": "1",
                "DHZ": "",
                "IS_GROUP": 0,
                "QUERY_TYPE": "0",
                "ACCT_LATN_ID": str(latn_id),
                "QRYZERO": 0,
                "QRY_INVALID_FLAG": "0"
            }

            logging2.info(f"调用qryJzrzDayFeeBill接口，请求参数: {qry_fee_payload}")

            try:
                qry_fee_result = UnitTool.send_post_request(qry_fee_url, qry_fee_payload)
                logging2.info(f"qryJzrzDayFeeBill接口返回结果: {qry_fee_result}")

                if not qry_fee_result or qry_fee_result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"qryJzrzDayFeeBill接口调用失败: {qry_fee_result.get('resultMsg', '未知错误') if qry_fee_result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理qryJzrzDayFeeBill返回数据
                fee_bill_data = _process_fee_bill_data_without_card_relation(qry_fee_result, acct_type)
                
                if fee_bill_data.get("status") != "success":
                    response_list.append({
                        "status": "error",
                        "message": f"处理账单费用数据失败: {fee_bill_data.get('message', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                fee_info_list = fee_bill_data.get("feeInfoList", [])
                
                # 调用PricingQuery接口
                pricing_query_payload = {
                    "destinationAttr": "2",
                    "accNbr": billing_nb
                }

                logging2.info(f"调用PricingQuery接口，请求参数: {pricing_query_payload}")

                try:
                    pricing_query_result = UnitTool.send_post_request(pricing_query_url, pricing_query_payload)
                    logging2.info(f"PricingQuery接口返回结果: {pricing_query_result}")

                    if not pricing_query_result or pricing_query_result.get("resultCode") != "0":
                        logging2.warning(f"PricingQuery接口调用失败: {pricing_query_result.get('resultMsg', '未知错误') if pricing_query_result else '接口无响应'}")
                        pricing_plan_info = []
                    else:
                        pricing_plan_info = pricing_query_result.get("pricingPlanInfo", [])

                except Exception as pricing_ex:
                    logging2.error(f"调用PricingQuery接口时出错: {str(pricing_ex)}")
                    pricing_plan_info = []

                # 整合两个接口的数据
                integrated_data = []
                
                # 遍历feeInfoList，与pricingPlanInfo进行匹配
                for fee_item in fee_info_list:
                    acc_num = fee_item.get("ACC_NUM")
                    offer_inst_id = fee_item.get("OFFER_INST_ID")
                    
                    # 查找匹配的pricingPlanInfo
                    matched_pricing_plan = None
                    for pricing_item in pricing_plan_info:
                        service_nbr = pricing_item.get("ServiceNbr")
                        pricing_offer_inst_id = pricing_item.get("offerInstId")
                        
                        # 匹配条件：ACC_NUM与ServiceNbr相等，OFFER_INST_ID与offerInstId相等
                        if (str(acc_num) == str(service_nbr) and 
                            str(offer_inst_id) == str(pricing_offer_inst_id)):
                            matched_pricing_plan = pricing_item
                            break
                    
                    # 整合数据
                    if matched_pricing_plan:
                        # 创建pricing_plan的副本，移除pricingDesc字段
                        filtered_pricing_plan = {k: v for k, v in matched_pricing_plan.items() if k != "pricingDesc"}
                    else:
                        filtered_pricing_plan = matched_pricing_plan
                    
                    integrated_item = {
                        "feeInfo": fee_item,
                        "pricingPlan": filtered_pricing_plan
                    }
                    integrated_data.append(integrated_item)

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "ACCT_ID": acct_id,
                    "PROD_INST_ID": prod_inst_id,
                    "LATN_ID": latn_id,
                    "ACCT_TYPE": acct_type,
                    "ACCT_NAME": fee_bill_data.get("ACCT_NAME", ""),
                    "integratedData": integrated_data,
                    "totalCount": str(len(integrated_data))
                })

            except Exception as fee_ex:
                logging2.error(f"调用qryJzrzDayFeeBill接口时出错: {str(fee_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用qryJzrzDayFeeBill接口异常: {str(fee_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "权益包订购与费用关系查询完成",
            "input_details": response_list
        }
        
        # 检查是否有integratedData，有值才添加可视化数据
        has_integrated_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                integrated_data = detail.get("integratedData", [])
                if integrated_data and len(integrated_data) > 0:
                    has_integrated_data = True
                    break
        
        logging2.info(f"权益包订购与费用关系查询完成，返回结果: {final_response}")
        
        # 根据是否有integratedData决定是否添加可视化数据
        if has_integrated_data:
            logging2.info("检测到权益包费用关系数据，添加可视化数据")
            return jsonify(add_visualization_data("benefits_package_fee_relation", final_response, "权益包订购与费用关系", "权益包费用关系分析"))
        else:
            logging2.info("未检测到权益包费用关系数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理权益包订购与费用关系查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


# 权益包账单费用接口
@app.route('/query/benefits_package_bill_fee', methods=['POST'])
def benefits_package_bill_fee():
    """
    权益包账单费用接口
    调用qryJzrzDayFeeBill接口，根据ACCT_TYPE过滤数据（不验证主副卡关系）
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"权益包账单费用接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 获取请求参数
            acct_id = row.get('ACCT_ID')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('LATN_ID')
            acct_type = row.get('ACCT_TYPE')

            # 基础参数校验
            if not acct_id or not prod_inst_id or not billing_nb or not billing_cycle or not latn_id or not acct_type:
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 ACCT_ID、PROD_INST_ID、billing_nb、billing_cycle、LATN_ID 或 ACCT_TYPE",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 验证ACCT_TYPE参数
            if acct_type not in ['1', '2', '3', '4', '5']:
                response_list.append({
                    "status": "error",
                    "message": "无效的 ACCT_TYPE，应为1-5之间的数字",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始权益包账单费用查询: ACCT_ID={acct_id}, PROD_INST_ID={prod_inst_id}, billing_nb={billing_nb}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}")

            # 获取配置信息
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            qry_fee_url = config_loader.get_url("qryJzrzDayFeeBill")
            
            if not qry_fee_url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到qryJzrzDayFeeBill接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 调用qryJzrzDayFeeBill接口
            qry_fee_payload = {
                "ACCT_ID": str(acct_id),
                "BILLING_CYCLE_ID": str(billing_cycle),
                "LATN_ID": str(latn_id),
                "QRY_TYPE": "0",
                "QUWERY_KIND_TYPE": "1",
                "DHZ": "",
                "IS_GROUP": 0,
                "QUERY_TYPE": "0",
                "ACCT_LATN_ID": str(latn_id),
                "QRYZERO": 0,
                "QRY_INVALID_FLAG": "0"
            }

            logging2.info(f"调用qryJzrzDayFeeBill接口，请求参数: {qry_fee_payload}")

            try:
                qry_fee_result = UnitTool.send_post_request(qry_fee_url, qry_fee_payload)
                logging2.info(f"qryJzrzDayFeeBill接口返回结果: {qry_fee_result}")

                if not qry_fee_result or qry_fee_result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"qryJzrzDayFeeBill接口调用失败: {qry_fee_result.get('resultMsg', '未知错误') if qry_fee_result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 处理qryJzrzDayFeeBill返回数据
                fee_bill_data = _process_fee_bill_data_without_card_relation(qry_fee_result, acct_type)
                
                if fee_bill_data.get("status") != "success":
                    response_list.append({
                        "status": "error",
                        "message": f"处理账单费用数据失败: {fee_bill_data.get('message', '未知错误')}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "ACCT_ID": acct_id,
                    "PROD_INST_ID": prod_inst_id,
                    "LATN_ID": latn_id,
                    "ACCT_TYPE": acct_type,
                    "ACCT_NAME": fee_bill_data.get("ACCT_NAME", ""),
                    "feeInfoList": fee_bill_data.get("feeInfoList", []),
                    "totalCount": fee_bill_data.get("totalCount", "0")
                })

            except Exception as fee_ex:
                logging2.error(f"调用qryJzrzDayFeeBill接口时出错: {str(fee_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用qryJzrzDayFeeBill接口异常: {str(fee_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "权益包账单费用查询完成",
            "input_details": response_list
        }
        
        # 检查是否有feeInfoList数据，有值才添加可视化数据
        has_fee_data = False
        for detail in response_list:
            if isinstance(detail, dict) and detail.get("status") == "success":
                fee_info_list = detail.get("feeInfoList", [])
                if fee_info_list and len(fee_info_list) > 0:
                    has_fee_data = True
                    break
        
        logging2.info(f"权益包账单费用查询完成，返回结果: {final_response}")
        
        # 根据是否有feeInfoList决定是否添加可视化数据
        if has_fee_data:
            logging2.info("检测到权益包账单费用数据，添加可视化数据")
            return jsonify(add_visualization_data("benefits_package_bill_fee", final_response, "权益包账单费用", "权益包账单费用明细"))
        else:
            logging2.info("未检测到权益包账单费用数据，直接返回原始数据")
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理权益包账单费用查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1888)