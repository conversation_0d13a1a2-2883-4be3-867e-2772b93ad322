import json
import logging2
import traceback
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from api_services.common.utils import _parse_params_content

# 创建数据分析服务蓝图
analysis_bp = Blueprint('analysis_services', __name__)

# 用户热点事件接口
@analysis_bp.route('/query/user_trending_events', methods=['POST'])
def user_trending_events():
    """
    用户热点事件查询接口
    根据用户号码和账期查询用户的热点事件信息
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"用户热点事件接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取请求参数
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')

        # 基础参数校验
        if not billing_nb or not billing_cycle:
            return jsonify({
                "status": "error",
                "message": "缺少必要参数 billing_nb 或 billing_cycle"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "error",
                "message": "无效的 billing_cycle 格式，应为YYYYMM格式"
            })

        logging2.info(f"开始查询用户热点事件: billing_nb={billing_nb}, billing_cycle={billing_cycle}")

        # 提取月份 MM
        month_mm = billing_cycle[4:]

        # 查询用户热点事件
        replacements = [['##MM##', month_mm]]
        params = [billing_nb]
        
        try:
            result_rows = db_manager_instance.excute_sql(
                sql_name='QueryUserTrendingEvents',
                params=params,
                lst_replace_code_value=replacements
            )
            
            logging2.info(f"查询到用户热点事件数据: {len(result_rows) if result_rows else 0} 条")
            
        except Exception as db_ex:
            logging2.error(f"查询用户热点事件时出错: {str(db_ex)}")
            return jsonify({
                "status": "error",
                "message": f"查询数据库时出错: {str(db_ex)}"
            })

        # 处理查询结果
        user_trending_events_list = []
        
        if result_rows:
            for row in result_rows:
                params_str, send_content, business_id, create_time = row
                
                # 格式化时间
                if create_time:
                    if hasattr(create_time, 'strftime'):
                        formatted_time = create_time.strftime('%Y-%m-%d %H:%M')
                    else:
                        formatted_time = str(create_time)
                else:
                    formatted_time = ""
                
                # 确定Content内容
                if send_content and send_content.strip():
                    # 如果send_content有内容，直接使用
                    content = send_content.strip()
                else:
                    # 如果send_content为空，解析params
                    content = _parse_params_content(params_str, business_id)
                
                # 只有当content不是"参数为空"时才添加到结果列表中
                if content != "参数为空":
                    user_trending_events_list.append({
                        "CreateTime": formatted_time,
                        "Content": content
                    })
        
        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "UserTrendingEventslist": user_trending_events_list
        }
        
        logging2.info(f"用户热点事件查询完成，返回结果: {response_data}")
        return jsonify(response_data)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理用户热点事件查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500

