import requests
from config<PERSON><PERSON><PERSON> import ConfigLoader
from typing import Dict, Optional


class HTTPClient:
    """HTTP客户端，负责发送POST请求"""

    def __init__(self, config_loader: ConfigLoader):
        self.config_loader = config_loader

    def send_post_request(
            self,
            payload: Dict,
            headers: Optional[Dict] = None,
            timeout: int = 60,
            url_key: str = "RtBillItem"
    ) -> Optional[Dict]:
        """
        发送JSON格式的HTTP POST请求并返回JSON响应

        参数:
        payload (Dict): 请求JSON数据
        headers (Optional[Dict]): 请求头
        timeout (int): 请求超时时间（秒）
        url_key (str): 配置文件中URL的键名

        返回:
        Optional[Dict]: 解析后的JSON响应，失败时返回None
        """
        # 获取URL
        url = self.config_loader.get_url(url_key)
        if not url:
            print(f"错误: 配置中未找到URL键 '{url_key}'")
            return None

        # 设置默认请求头
        default_headers = {"Content-Type": "application/json"}
        if headers:
            default_headers.update(headers)

        try:
            # 发送POST请求
            response = requests.post(
                url=url,
                json=payload,
                headers=default_headers,
                timeout=timeout
            )

            # 检查HTTP状态码
            response.raise_for_status()

            # 解析JSON响应
            return response.json()

        except requests.exceptions.HTTPError as e:
            print(f"HTTP错误 ({response.status_code}): {e}")
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
        except Exception as e:
            print(f"未知错误: {e}")

        return None