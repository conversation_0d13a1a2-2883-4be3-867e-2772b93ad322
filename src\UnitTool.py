import requests
import json
import logging
import datetime

import logging2
from collections import defaultdict

class UnitTool:
    """查询套外流量和费用"""
    def summarize_volume_and_fee(json_str):
        """
        从JSON数据中按offerInstId和offerName分组，汇总volume和fee值
        参数:
        json_str (str): JSON格式的字符串

        返回:
        dict: 汇总结果，格式为 {套餐ID_套餐名称: {"total_volume": 总流量, "total_fee": 总费用}}
        """
        try:
            # 解析JSON数据
            data = json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return {}

        # 用于存储汇总结果
        summary = defaultdict(lambda: {"total_volume": 0, "total_fee": 0})

        # 提取数据项列表
        data_items = data.get('dataBillItems', [])

        for item in data_items:
            fee = item.get('fee', 0)
            volume = item.get('volume', 0)
            accu_info_list = item.get('AccuInfoStruct', [])

            # 筛选出unit为0且Amount等于fee的记录
            valid_accu = [
                accu for accu in accu_info_list
                if str(accu.get('unit')) == '0' and str(accu.get('Amount')) == str(fee)
            ]

            if valid_accu:
                # 提取套餐信息
                offer_inst_id = valid_accu[0].get('offerInstId')
                offer_name = valid_accu[0].get('offerName')

                if offer_inst_id and offer_name:
                    key = f"{offer_inst_id}_{offer_name}"
                    # 累加volume和fee
                    summary[key]["total_volume"] += volume
                    summary[key]["total_fee"] += fee

        return dict(summary)
    # 查询总的流量含免费和套外offerInstId、offerName、totalVolume、totalFee
    def parse_json_data(json_data):
        # 解析JSON数据
        data = json.loads(json_data)

        # 初始化结果字典，用于存储每个套餐的汇总信息
        summary = defaultdict(lambda: {"volume": 0, "fee": 0})

        # 遍历每个流量记录
        for item in data.get("dataBillItems", []):
            # 获取费用
            fee = item.get("fee", 0)

            # 遍历套餐信息
            for accu_info in item.get("AccuInfoStruct", []):
                offer_inst_id = accu_info.get("offerInstId", "")
                offer_name = accu_info.get("offerName", "")

                # 只处理流量相关的记录（unit为3通常表示流量）
                if accu_info.get("unit") == "3":
                    volume = int(accu_info.get("Amount", 0))
                else:
                    volume = 0

                # 使用offerInstId和offerName作为键
                key = (offer_inst_id, offer_name)

                # 汇总流量和费用
                summary[key]["volume"] += volume
                summary[key]["fee"] += fee

        # 转换结果为列表格式
        result = []
        for (offer_inst_id, offer_name), values in summary.items():
            result.append({
                "offerInstId": offer_inst_id,
                "offerName": offer_name,
                "totalVolume": values["volume"],
                "totalFee": values["fee"]
            })

        return result

    #查询总流量和套外费用

    def integrate_offer_info(json_str):
        """
        整合套餐流量和费用信息，按offerInstId和offerName分组
        参数:
        json_str (str): JSON格式的字符串

        返回:
        list: 整合结果，格式为 [{
            "offerInstId": 套餐ID,
            "offerName": 套餐名称,
            "totalVolume": 总流量,
            "totalFee": 总费用
        }]
        """
        try:
            # 解析JSON数据
            data = json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return []

        # 用于存储汇总结果
        summary = defaultdict(lambda: {"total_volume": 0, "total_fee": 0})

        # 提取数据项列表
        data_items = data.get('dataBillItems', [])

        for item in data_items:
            fee = item.get('fee', 0)
            volume = item.get('volume', 0)
            accu_info_list = item.get('AccuInfoStruct', [])

            # 处理套外费用相关的AccuInfoStruct记录
            for accu in accu_info_list:
                if str(accu.get('unit')) == '0' and str(accu.get('Amount')) == str(fee):
                    offer_inst_id = accu.get('offerInstId')
                    offer_name = accu.get('offerName')
                    if offer_inst_id and offer_name:
                        key = f"{offer_inst_id}_{offer_name}"
                        summary[key]["total_fee"] += fee

            # 处理总流量相关的AccuInfoStruct记录
            for accu in accu_info_list:
                if str(accu.get('unit')) == '3':
                    offer_inst_id = accu.get('offerInstId')
                    offer_name = accu.get('offerName')
                    if offer_inst_id and offer_name:
                        key = f"{offer_inst_id}_{offer_name}"
                        summary[key]["total_volume"] += int(accu.get('Amount', 0))

        # 转换结果为列表格式
        result = []
        for key, values in summary.items():
            offer_inst_id, offer_name = key.split('_', 1)
            result.append({
                "offerInstId": offer_inst_id,
                "offerName": offer_name,
                "totalVolume": values["total_volume"],
                "totalFee": values["total_fee"]
            })

        return result

    def get_last_day_of_month(date_str):
        """
        获取指定月份的最后一天日期
        参数:
        date_str (str): 格式为YYYYMM的日期字符串

        返回:
        str: 格式为YYYYMMDD的最后一天日期字符串
        """
        try:
            # 解析输入的年月
            logging2.debug(f"date_str={date_str}")
            year = int(date_str[:4])
            month = int(date_str[4:])
            # 计算下个月的第一天
            next_month = datetime.date(year, month, 1) + datetime.timedelta(days=32)
            # 下个月的第一天减去一天即为当前月的最后一天
            last_day = next_month.replace(day=1) - datetime.timedelta(days=1)
            logging2.debug(f"last_day={last_day}")
            # 返回格式化的日期字符串
            return last_day.strftime('%Y%m%d')
        except ValueError:
            print("输入的日期格式不正确，请使用YYYYMM格式")
            return None
        except Exception as e:
            print(f"发生错误: {e}")
            return None

    def send_post_request(url, payload, headers=None, timeout=60):
        """
        发送JSON格式的HTTP POST请求并返回JSON响应

        参数:
        url (str): 请求URL
        payload (dict): 请求JSON数据
        headers (dict, optional): 请求头，默认包含Content-Type: application/json
        timeout (int, optional): 请求超时时间（秒）

        返回:
        dict: 解析后的JSON响应
        None: 请求失败或响应解析错误
        """
        # 设置默认请求头
        default_headers = {
            'Content-Type': 'application/json'
        }
        if headers:
            default_headers.update(headers)

        try:
            # 发送POST请求
            if isinstance(payload, dict):
                # 如果payload是字典，使用json参数
                response = requests.post(
                    url=url,
                    json=payload,
                    headers=default_headers,
                    timeout=timeout
                )
            else:
                # 如果payload是字符串，使用data参数
                response = requests.post(
                    url=url,
                    data=payload,
                    headers=default_headers,
                    timeout=timeout
                )

            # 检查HTTP状态码
            response.raise_for_status()  # 非200状态码会抛出HTTPError

            # 解析JSON响应
            return response.json()

        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP错误: {http_err}")
        except requests.exceptions.RequestException as req_err:
            print(f"请求异常: {req_err}")
        except json.JSONDecodeError as json_err:
            print(f"JSON解析错误: {json_err}")
        except Exception as e:
            print(f"未知错误: {e}")

        return None