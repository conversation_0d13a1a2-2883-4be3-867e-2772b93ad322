def create_app():
    """
    创建并配置Flask应用
    """
    from flask import Flask
    import warnings
    import os
    
    # 屏蔽Flask开发服务器警告
    warnings.filterwarnings('ignore', message='This is a development server')
    warnings.filterwarnings('ignore', message='Do not use the development server')

    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 关闭Flask自带的调试信息
    app.config['ENV'] = 'production'
    app.config['DEBUG'] = False
    app.config['TESTING'] = False

    # 导入蓝图模块
    from api_services.billing_services import billing_bp
    from api_services.user_services import user_bp
    from api_services.package_services import package_bp
    from api_services.dispute_services import dispute_bp
    from api_services.relation_services import relation_bp
    from api_services.pricing_services import pricing_bp
    from api_services.analysis_services import analysis_bp
    from api_services.benefits_services import benefits_bp

    # 注册蓝图
    app.register_blueprint(billing_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(package_bp)
    app.register_blueprint(dispute_bp)
    app.register_blueprint(relation_bp)
    app.register_blueprint(pricing_bp)
    app.register_blueprint(analysis_bp)
    app.register_blueprint(benefits_bp)

    return app

# 全局应用实例
app = None