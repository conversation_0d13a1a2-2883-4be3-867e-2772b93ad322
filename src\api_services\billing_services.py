import json
import logging2
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path, get_charge_type_id_list
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data

# 创建计费服务蓝图
billing_bp = Blueprint('billing_services', __name__)

# 判断账期1+1接口
@billing_bp.route('/query/is_billing_cycle_service', methods=['POST'])
def is_billing_cycle_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        billing_cycle = json_dict.get('billing_cycle', '')
        
        if not billing_cycle or (len(billing_cycle) != 6 and len(billing_cycle) != 2):
            logging2.warning("缺少或格式错误的参数 billing_cycle")
            response_data = {"status": "success", "Is_billing_cycle": 0, "message": "缺少或无效的参数 billing_cycle"}
            return response_data

        # 获取当前年月和上一个月
        now = datetime.now()
        current_ym = now.strftime('%Y%m')
        
        # 当账期只有月份自动补充当前年
        if len(billing_cycle) == 2:
            current_year = now.strftime('%Y')
            billing_cycle = current_year + billing_cycle
            
        # 计算上一个月
        if now.month == 1:
            prev_ym = f"{now.year-1}12"
        else:
            prev_ym = f"{now.year}{now.month-1:02d}"
            
        logging2.info(f"当前年月: {current_ym}, 上一个月: {prev_ym}, 传入账期: {billing_cycle}")

        if billing_cycle in (current_ym, prev_ym):
            # 账期在允许范围内
            response_data = {"status": "success", "Is_billing_cycle": 1, "message": f"账期 {billing_cycle} 在允许范围内"}
            return response_data
        else:
            # 账期超出范围
            logging2.warning(f"账期 {billing_cycle} 超出允许范围 ({current_ym}, {prev_ym})")
            response_data = {"status": "success", "Is_billing_cycle": 0, "message": "超出流量费用争议诊断账期范围，请核实。"}
            return response_data

    except Exception as e:
        logging2.error(f"账期1+1接口处理请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500

# 查询流量溢出费用账单服务
@billing_bp.route('/query/flux_overflow_fee_service', methods=['POST'])
def flux_overflow_fee_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details = []
        
        for row in json_dict.get('input_details'):
            disputeType = row.get('disputeType')
            acct_id = row.get('ACCT_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            ycharge = row.get('charge')

            # disputeType=1 流量费用争议逻辑
            if str(disputeType) == '11111':
                # 校验必要参数
                if not acct_id or not billing_cycle or not latn_id:
                    logging2.warning("缺少必要参数 ACCT_ID, billing_cycle 或 latn_id")
                    input_details.append({"status": "success", "message": "缺少必要参数 ACCT_ID, billing_cycle 或 latn_id", "Is_flux_fee": 0})

                # 提取月份 MM
                if len(billing_cycle) != 6:
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    response_data = {"status": "success", "message": "无效的 billing_cycle 格式", "Is_flux_fee": 0}
                    return response_data
                month_mm = billing_cycle[4:]

                logging2.info(f"查询流量溢出费用: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, latn_id={latn_id}, MM={month_mm}")

                # SQL查询参数和替换值
                replacements = [
                    ['##LATNID##', str(latn_id)],
                    ['##MM##', month_mm]
                ]
                params = [acct_id]

                # 执行 SQL 查询
                results = db_manager_instance.excute_sql(
                    sql_name='QueryFluxOverflowFee',
                    params=params,
                    lst_replace_code_value=replacements
                )

                # 处理查询结果
                if not results:
                    logging2.warning(f"未查询到流量费用记录: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, latn_id={latn_id}")
                    input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "该用户投诉月份在计费系统中未查询到流量费用，请核实。", "Is_flux_fee": 0})

                has_charge = False
                fee_details = []
                for row in results:
                    # 假设查询结果是 (source_inst_id, flux_over, charge)
                    if len(row) == 6:
                        source_inst_id, flux_over, charge, name, ofr_id, offer_name = row
                        fee_details.append({
                            "offer_inst_id": source_inst_id,
                            "flux_over": flux_over,
                            "charge": charge,
                            "acct_item_name": name,
                            "offer_id": ofr_id,
                            "offer_name": offer_name
                        })
                        if charge and int(charge) > 0:
                            has_charge = True
                    else:
                        logging2.error(f"查询结果格式不正确: {row}")
                input_details.append(fee_details)
                
            # disputeType=2 流量包功能费争议逻辑
            elif str(disputeType) == '11112':
                # 校验必要参数
                if not acct_id or not billing_cycle or not latn_id:
                    logging2.warning("缺少必要参数 ACCT_ID, billing_cycle 或 latn_id")
                    input_details.append({"status": "success", "message": "缺少必要参数 ACCT_ID, billing_cycle 或 latn_id", "Is_flux_fee": 0})

                if len(billing_cycle) != 6:
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    input_details.append({"status": "success", "message": "无效的 billing_cycle 格式", "Is_flux_fee": 0})
                month_mm = billing_cycle[4:]

                # 查询功能费（262100102），需要查出ofr_id字段
                replacements = [
                    ['##LATNID##', str(latn_id)],
                    ['##MM##', month_mm]
                ]
                params = [acct_id]
                try:
                    # 查出source_inst_id, charge, ofr_id
                    results = db_manager_instance.excute_sql(
                        sql_name='QueryFluxFunctionFee',
                        params=params,
                        lst_replace_code_value=replacements
                    )
                except Exception as e:
                    logging2.error(f"功能费争议SQL执行异常: {e}")
                    input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "查询计费系统流量包功能费异常", "Is_flux_fee": 0})

                if not results:
                    input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})

                # 检查charge字段，收集offer_inst_id, charge, ofr_id
                offer_inst_id_list = []
                charge_list = []
                ofr_id_list = []
                for row in results:
                    if len(row) >= 3:
                        offer_inst_id, charge, ofr_id = row[0], row[1], row[2]
                    elif len(row) == 2:
                        offer_inst_id, charge = row[0], row[1]
                        ofr_id = None
                    else:
                        continue
                    offer_inst_id_list.append(offer_inst_id)
                    charge_list.append(charge)
                    ofr_id_list.append(ofr_id)

                has_positive = any(charge is not None and float(charge) > 0 for charge in charge_list)
                has_negative = all(charge is not None and float(charge) <= 0 for charge in charge_list)

                if has_negative:
                    input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})

                if has_positive:
                    # 查询订单号 offer_inst_id -> last_order_item_id
                    offer_inst_id_set = set(offer_inst_id_list)
                    offer_inst_id_list_unique = list(offer_inst_id_set)
                    if not offer_inst_id_list_unique:
                        input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "未获取到有效的流量包实例ID", "Is_flux_fee": 0})
                    
                    # 查询订单号
                    replacements2 = [['##LATNID##', str(latn_id)]]
                    params2 = offer_inst_id_list_unique
                    try:
                        order_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferInstOrderList',
                            params=params2,
                            lst_replace_code_value=replacements2
                        )
                    except Exception as e:
                        logging2.error(f"查询订单号SQL异常: {e}")
                        input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "查询订单号异常", "Is_flux_fee": 0})

                    # 查询OFFER_ID和OFFER_NAME
                    # 先收集所有ofr_id（OFFER_ID）
                    offer_id_set = set()
                    offer_instid_to_offerid = {}
                    for idx, offer_inst_id in enumerate(offer_inst_id_list):
                        ofr_id = ofr_id_list[idx]
                        if ofr_id:
                            offer_id_set.add(ofr_id)
                            offer_instid_to_offerid[offer_inst_id] = ofr_id

                    offer_id_to_name = {}
                    if offer_id_set:
                        # 查询销售品名称
                        try:
                            offer_id_list = list(offer_id_set)
                            offer_name_rows = db_manager_instance.excute_sql(
                                sql_name='QueryOfferNameByIds',
                                params=offer_id_list
                            )
                            for row in offer_name_rows:
                                if len(row) >= 2:
                                    offer_id_to_name[str(row[0])] = row[1]
                        except Exception as e:
                            logging2.error(f"查询OFFER_NAME异常: {e}")

                    # 组装fluxOfrList
                    flux_ofr_list = []
                    # 订单号映射
                    offer_instid_to_orderid = {}
                    for row in order_rows:
                        if len(row) >= 2:
                            offer_instid_to_orderid[str(row[0])] = row[1]

                    for idx, offer_inst_id in enumerate(offer_inst_id_list):
                        ofr_id = ofr_id_list[idx]
                        offer_id = ofr_id if ofr_id else offer_instid_to_offerid.get(offer_inst_id)
                        offer_name = offer_id_to_name.get(str(offer_id), None) if offer_id else None
                        last_order_item_id = offer_instid_to_orderid.get(str(offer_inst_id), None)
                        flux_ofr_list.append({
                            "OFFER_ID": offer_id,
                            "OFFER_NAME": offer_name,
                            "offerInstId": offer_inst_id,
                            "lastOrderItemId": last_order_item_id
                        })

                    input_details.append({
                        "status": "success",
                        "message": "用户有订购流量包，订单号列表见详情。",
                        "Is_flux_fee": 0,
                        "fluxOfrList": flux_ofr_list,
                        "hasFluxOfrList": 1 if flux_ofr_list else 0,
                        "PROD_INST_ID": prod_inst_id,
                        "latn_id": latn_id,
                        "billing_cycle": billing_cycle,
                        "charge": ycharge,
                        "billing_nb": billing_nb
                    })
                else:
                    input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "该用户在计费系统没有查到相关流量清单以及流量包功能费，请核实。", "Is_flux_fee": 0})
                    
            # disputeType=3 国际漫游流量费用争议
            elif str(disputeType) == '3':
                input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge,
                    "status": "success",
                    "message": "国际漫游由于是集团批价，省内计费只是负责代收，具体资费以及国际漫游包的介绍，请参考manyou.189.cn网站查询。如网站查询后还无法核实问题，可转人工处理",
                    "Is_flux_fee": 0
                })

            else:
                logging2.warning(f"不支持的 disputeType: {disputeType}")
                input_details.append({"billing_nb": billing_nb, "billing_cycle": billing_cycle, "latn_id": latn_id, "acct_id": acct_id, "charge": ycharge, "status": "success", "message": "现仅支持流量费用争议、流量包功能费争议和国际漫游流量费用争议问题查询，暂时不支持其他问题查询。", "Is_flux_fee": 0})
                
        logging2.info(f"流量溢出费用查询响应报文input_details={input_details}")
        return {"status": "success", "Is_flux_fee": 1, "input_details": input_details}
        
    except Exception as e:
        logging2.error(f"处理流量溢出费用查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500

# 查询收费清单服务
@billing_bp.route('/query/charge_detail_service', methods=['POST'])
def charge_detail_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('latn_id')
        
        logging2.debug(f"billing_cycle={billing_cycle}")
        endDate = UnitTool.get_last_day_of_month(billing_cycle)
        logging2.debug(f"endDate={endDate}")
        startDate = f"{billing_cycle}01"
        selected_details = []
        accNun_detail = []
        select_accNun = []
        
        for row in json_dict.get('valid_pkgs'):
            accu_type_id = row.get('ACCU_TYPE_ID')
            offer_id = row.get('offer_id')
            offer_inst_id = row.get('offer_inst_id')
            pricingType = ""
            replacements = [['##LATNID##', str(latn_id)]]
            
            if not accu_type_id or not offer_id:
                pricingTypeRow = db_manager_instance.excute_sql(
                    sql_name='QueryOfferPricingRelPricingType', params=[offer_id], lst_replace_code_value=[]
                )
                for rows in pricingTypeRow:
                    pricingType = rows[0]
                    
            logging2.info(f"pricingType={pricingType}")
            
            if pricingType == "80C":
                prodInstList = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelProdInst', params=[offer_inst_id], lst_replace_code_value=replacements
                )
                if prodInstList:
                    select_accNun = prodInstList[0]
                    
        unique_accNun = set(select_accNun)
        logging2.debug(f"unique_accNun={unique_accNun}")
        
        if len(unique_accNun) > 0:
            for prodInstId in unique_accNun:
                accNumList = db_manager_instance.excute_sql(
                    sql_name='QueryProdInst', params=[prodInstId], lst_replace_code_value=replacements
                )
                logging2.info(f"accNumList={accNumList}")
                
                for accNumrows in accNumList:
                    accNum = accNumrows[0]
                    # 使用字典构造代替字符串拼接
                    replacements = [
                        ['##LATNID##', str(latn_id)],
                        ['##BILLINGCYCLE##', billing_cycle]
                    ]
                    params = [accNum]
                    try:
                        # 查出source_inst_id, charge, ofr_id
                        results = db_manager_instance.excute_sql(
                            sql_name='QueryOfferFlowCharge',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for result in results:
                            accNun_detail.append({
                                "billing_nb": accNum,
                                "offer_id": result[0],
                                "sum_flow": result[1],
                                "sum_fee": result[2],
                                "price": result[3]
                            })
                    except Exception as e:
                        logging2.error(f"功能费争议SQL执行异常: {e}")

            selected_details.append({
                "accNun_detail": accNun_detail
            })
        else:
            replacements = [
                ['##LATNID##', str(latn_id)],
                ['##BILLINGCYCLE##', billing_cycle]
            ]
            params = [billing_nb]
            try:
                # 查出source_inst_id, charge, ofr_id
                results = db_manager_instance.excute_sql(
                    sql_name='QueryOfferFlowCharge',
                    params=params,
                    lst_replace_code_value=replacements
                )
                for result in results:
                    accNun_detail.append({
                        "billing_nb": billing_nb,
                        "offer_id": result[0],
                        "sum_flow": result[1],
                        "sum_fee": result[2],
                        "price": result[3]
                    })
            except Exception as e:
                logging2.error(f"功能费争议SQL执行异常: {e}")
                
            selected_details.append({
                "accNun_detail": accNun_detail
            })
            
        logging2.info(f"清单返回详细信息selected_details={selected_details}")
        response_data = {
            "message": "清单返回详细信息",
            "BillItemsDetail": selected_details
        }
        return response_data
        
    except Exception as e:
        logging2.error(f"处理收费清单查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": f"内部服务器错误: {str(e)}"}), 500

# 查询账单费用接口
@billing_bp.route('/query/qryJzrzDayFeeBill', methods=['POST'])
def qry_jzrz_day_fee_bill():
    """
    查询账单费用接口
    调用OPENAPI的查询账单费用接口，根据ACCT_TYPE过滤数据，并验证主副卡关系
    """
    try:
        # 解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"查询账单费用接口请求参数: {data}")
        json_dict = json.loads(data)

        # 获取请求参数
        acct_id = json_dict.get('ACCT_ID')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('LATN_ID')
        acct_type = json_dict.get('ACCT_TYPE')
        prod_inst_id = json_dict.get('PROD_INST_ID')

        # 基础参数校验
        if not acct_id or not billing_cycle or not latn_id or not acct_type or not prod_inst_id:
            return jsonify({
                "status": "error",
                "message": "缺少必要参数 ACCT_ID、billing_cycle、LATN_ID、ACCT_TYPE 或 PROD_INST_ID"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "error",
                "message": "无效的 billing_cycle 格式，应为YYYYMM格式"
            })

        # 验证ACCT_TYPE参数
        if acct_type not in ['1', '2', '3', '4', '5']:
            return jsonify({
                "status": "error",
                "message": "无效的 ACCT_TYPE，应为1-5之间的数字"
            })

        logging2.info(f"开始查询账单费用: ACCT_ID={acct_id}, billing_cycle={billing_cycle}, LATN_ID={latn_id}, ACCT_TYPE={acct_type}, PROD_INST_ID={prod_inst_id}")

        # 构建OPENAPI请求参数
        request_payload = {
            "ACCT_ID": str(acct_id),
            "BILLING_CYCLE_ID": str(billing_cycle),
            "LATN_ID": str(latn_id),
            "QRY_TYPE": "0",
            "QUWERY_KIND_TYPE": "1",
            "DHZ": "",
            "IS_GROUP": 0,
            "QUERY_TYPE": "0",
            "ACCT_LATN_ID": str(latn_id),
            "QRYZERO": 0,
            "QRY_INVALID_FLAG": "0"
        }

        logging2.info(f"调用OPENAPI查询账单费用接口，请求参数: {request_payload}")

        # 调用OPENAPI接口
        config_path = get_config_path()
        if not config_path:
            return jsonify({
                "status": "error",
                "message": "找不到配置文件config.json"
            })
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("qryJzrzDayFeeBill")
        if not url:
            return jsonify({
                "status": "error",
                "message": "配置中未找到qryJzrzDayFeeBill接口URL"
            })

        try:
            result = UnitTool.send_post_request(url, request_payload)
            logging2.info(f"OPENAPI接口返回结果: {result}")

            if not result:
                return jsonify({
                    "status": "error",
                    "message": "调用OPENAPI接口失败"
                })

            # 检查返回结果
            if result.get("resultCode") != "0":
                return jsonify({
                    "status": "error",
                    "message": f"OPENAPI接口返回错误: {result.get('resultMsg', '未知错误')}"
                })

            # 处理返回数据，传入PROD_INST_ID参数
            response_data = _process_fee_bill_data(result, acct_type, latn_id, prod_inst_id)

            # 检查是否有feeInfoList数据，如果有则添加可视化
            fee_info_list = response_data.get("feeInfoList", [])
            if fee_info_list:
                logging2.info("检测到账单费用数据，添加可视化渲染")
                response_data = add_visualization_data("qryJzrzDayFeeBill", response_data, "账单费用查询", "账单费用详细信息")

            return jsonify(response_data)

        except Exception as api_ex:
            logging2.error(f"调用OPENAPI接口时出错: {str(api_ex)}")
            return jsonify({
                "status": "error",
                "message": f"调用OPENAPI接口异常: {str(api_ex)}"
            })

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询账单费用请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500



def _process_fee_bill_data(api_result, acct_type, latn_id, prod_inst_id):
    """
    处理账单费用数据
    根据ACCT_TYPE过滤数据，并验证主副卡关系
    """
    try:
        global db_manager_instance
        
        # 获取原始数据
        acct_name = api_result.get("ACCT_NAME", "")
        fee_info_list = api_result.get("feeInfoList", [])
        
        if not fee_info_list:
            return {
                "status": "success",
                "message": "未查询到账单费用数据",
                "ACCT_NAME": acct_name,
                "feeInfoList": [],
                "totalCount": "0"
            }

        # 首先根据ACCT_TYPE进行数据过滤
        filtered_fee_list = []
        
        for fee_item in fee_info_list:
            acct_item_type_name = fee_item.get("ACCT_ITEM_TYPE_NAME", "")

            # 先过滤AMOUNT为0的记录
            amount = fee_item.get("AMOUNT", "0")
            # 使用字符串比较判断AMOUNT为0的情况，包括"0"和"0.00"
            if amount in ["0", "0.0", "0.00"] or not amount or amount.strip() == "":
                continue  # 跳过AMOUNT为0的记录

            # 根据ACCT_TYPE进行过滤
            should_include = False

            if acct_type == "1":  # 流量
                if "流量" in acct_item_type_name:
                    should_include = True
            elif acct_type == "2":  # 语音
                if "语音" in acct_item_type_name:
                    should_include = True
            elif acct_type == "3":  # 短信
                if "短信" in acct_item_type_name:
                    should_include = True
            elif acct_type == "4":  # 功能费
                try:
                    # 使用配置的CHARGE_TYPE_ID进行过滤
                    charge_type_ids = get_charge_type_id_list()
                    if charge_type_ids:
                        # 从fee_item中获取CHARGE_TYPE_ID字段
                        charge_type_id = fee_item.get("CHARGE_TYPE_ID")
                        if charge_type_id:
                            charge_type_id_int = int(charge_type_id)
                            if charge_type_id_int in charge_type_ids:
                                should_include = True
                    # 如果没有配置CHARGE_TYPE_ID，则全部过滤，不包含任何功能费记录
                except (ValueError, TypeError):
                    # 如果CHARGE_TYPE_ID不能转换为整数，则过滤掉
                    should_include = False
            elif acct_type == "5":  # 全部
                should_include = True
            
            if should_include:
                filtered_fee_list.append(fee_item)

        # 查询主副卡关系，收集所有有效的产品实例ID
        valid_prod_inst_ids = set()
        replacements = [['##LATNID##', str(latn_id)]]
        
        try:
            # 首先查询QueryProdInstRel_a_100800获取a_prod_inst_id
            params = [prod_inst_id, prod_inst_id]
            a_rows = db_manager_instance.excute_sql(
                sql_name='QueryProdInstRel_a_100800',
                params=params,
                lst_replace_code_value=replacements
            )
            
            if a_rows:
                # 如果查询到数据，收集所有的a_prod_inst_id
                for row in a_rows:
                    a_prod_inst_id = row[0] if row else None
                    if a_prod_inst_id:
                        valid_prod_inst_ids.add(str(a_prod_inst_id))
                        
                        # 再查询QueryProdInstRel_100800验证主副卡关系
                        params2 = [a_prod_inst_id]
                        a1_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInstRel_100800',
                            params=params2,
                            lst_replace_code_value=replacements
                        )
                        
                        if a1_rows:
                            # 如果QueryProdInstRel_100800有数据，收集所有的a_prod_inst_id和z_prod_inst_id
                            for a1_row in a1_rows:
                                if len(a1_row) >= 2:
                                    main_prod_inst_id = a1_row[0]  # a_prod_inst_id
                                    sub_prod_inst_id = a1_row[1]   # z_prod_inst_id
                                    if main_prod_inst_id:
                                        valid_prod_inst_ids.add(str(main_prod_inst_id))
                                    if sub_prod_inst_id:
                                        valid_prod_inst_ids.add(str(sub_prod_inst_id))
            else:
                # 如果QueryProdInstRel_a_100800查不到数据，则直接使用传入的prod_inst_id
                valid_prod_inst_ids.add(str(prod_inst_id))
                logging2.debug(f"QueryProdInstRel_a_100800查不到数据，直接使用当前PROD_INST_ID: {prod_inst_id}")
        
        except Exception as e:
            logging2.error(f"查询主副卡关系时出错: {str(e)}")
            # 如果查询出错，则只使用传入的prod_inst_id
            valid_prod_inst_ids.add(str(prod_inst_id))

        logging2.info(f"收集到的有效产品实例ID集合: {valid_prod_inst_ids}")

        # 根据主副卡关系过滤数据
        final_filtered_fee_list = []
        for fee_item in filtered_fee_list:
            prod_inst_id_in_fee = fee_item.get("PROD_INST_ID", "")
            
            # 如果没有PROD_INST_ID，则过滤掉这些数据，不添加到结果中
            if not prod_inst_id_in_fee:
                logging2.debug(f"过滤掉没有PROD_INST_ID的数据: {fee_item.get('ACCT_ITEM_TYPE_NAME', '')}")
                continue

            # 检查当前fee_item的PROD_INST_ID是否在有效的产品实例ID集合中
            if str(prod_inst_id_in_fee) in valid_prod_inst_ids:
                final_filtered_fee_list.append(fee_item)
                logging2.debug(f"匹配成功: PROD_INST_ID={prod_inst_id_in_fee} 在有效的产品实例ID集合中")
            else:
                logging2.debug(f"过滤掉: PROD_INST_ID={prod_inst_id_in_fee} 不在有效的产品实例ID集合中")

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "ACCT_NAME": acct_name,
            "feeInfoList": final_filtered_fee_list,
            "totalCount": str(len(final_filtered_fee_list))
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理账单费用数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"处理数据时出错: {str(e)}",
            "ACCT_NAME": "",
            "feeInfoList": [],
            "totalCount": "0"
        }
