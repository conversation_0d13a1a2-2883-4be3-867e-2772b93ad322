#!/usr/bin/env python
#  coding=utf-8

import sys
from Crypto.Cipher import AES
import binascii
 
class ASEUtil(object):
    """
    ase加解密工具类
    """
    @staticmethod
    def encrypt(text):
        """
        加密
        """
        try:
            key = b"TydicTydic300047"
            bs = AES.block_size
            pad = lambda s: s + (bs - len(s) % bs) * bytes([bs - len(s) % bs])
            cipher = AES.new(key, AES.MODE_ECB)  # ECB模式
            encrypted = cipher.encrypt(pad(text.encode())) #加密
            hex_str = binascii.hexlify(encrypted).decode() #转16进制
            return hex_str
        except Exception as err:
            print("encrypt error.", err)
            return None
            
 
    @staticmethod
    def decrypted(cipher_text):
        """
        解密
        """
        try:
            key = b"TydicTydic300047"
            bs = AES.block_size
            unpad = lambda s: s[:-ord(s[len(s)-1:])]
            cipher = AES.new(key, AES.MODE_ECB)  # ECB模式
            encrypted = binascii.unhexlify(cipher_text.encode()) #转二进制
            decrypted = cipher.decrypt(encrypted) #解密
            return unpad(decrypted).decode('utf-8') #解码
        except Exception as err:
            #print("decrypted error.", err)
            return cipher_text
 
 
if __name__ == '__main__':
    if len(sys.argv) != 3:
        print(" e.g.: %s password type(0-encrypt,1-decrypted)" % sys.argv[0])
        sys.exit()
    result = r''
    if sys.argv[2] == '0':
        result = ASEUtil.encrypt(sys.argv[1])
        print("encrypt password: \"%s\"" % result)
    elif sys.argv[2] == '1':
        result = ASEUtil.decrypted(sys.argv[1])
        print("decrypted password: \"%s\"" % result)
    print("---Don't copy quotes!!!")
    