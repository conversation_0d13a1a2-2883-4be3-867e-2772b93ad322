import json
import logging2
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from api_services.common.utils import get_latn_id_if_missing
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data
from httpClient import HTTPClient

# 创建用户服务蓝图
user_bp = Blueprint('user_services', __name__)

# 号段表本地网查询接口
@user_bp.route('/query/latn_id_service', methods=['POST'])
def latn_id_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details = []
        
        for row in json_dict:
            billing_nb = row.get('billing_nb', '')
            billing_cycle = row.get('billing_cycle', '')
            disputeType = row.get('disputeType', '')
            charge = row.get('charge', '')
            
            # 获取当前年月和上一个月
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            
            # 当账期只有月份自动补充当前年
            if len(billing_cycle) == 2:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + billing_cycle

            # 如果 billing_nb 为空，返回错误
            if not billing_nb:
                logging2.warning("缺少必要参数 latn_id 或 billing_nb")
                response_data = {"status": "success", "Is_latn_id": 0, "message": "缺少必要参数 latn_id 或 billing_nb"}
                return response_data

            logging2.info(f"尝试通过 billing_nb 查询 latn_id: {billing_nb}")
            params = [billing_nb]
            result = db_manager_instance.excute_sql(sql_name='QueryLatnIdByBillingNbr', params=params)

            # 处理查询结果
            if result and result[0] and result[0][0]:
                found_latn_id = result[0][0]
                logging2.info(f"查询成功，找到 latn_id: {found_latn_id}")
                response_data = {"status": "success", "Is_latn_id": 1, "latn_id": found_latn_id}
                
                # 添加额外字段
                response_data['billing_nb'] = billing_nb
                if billing_cycle:
                    response_data['billing_cycle'] = billing_cycle
                if disputeType:
                    response_data['disputeType'] = disputeType
                if charge:
                    response_data['charge'] = charge
                input_details.append(response_data)
            else:
                logging2.warning(f"未能通过 billing_nb {billing_nb} 查询到 latn_id")
                response_data = {"status": "success", "Is_latn_id": 0, "message": "本地网查询失败"}
                return response_data
                
        logging2.debug(f"input_details: {input_details}")
        response = {"status": "success", "Is_latn_id": 1, "input_details": input_details}
        return response

    except Exception as e:
        logging2.error(f"号段表本地网查询接口处理请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500

# 查询用户资料服务
@user_bp.route('/query/user_profile_service', methods=['POST'])
def user_profile_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details = []
        
        for row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            charge = row.get('charge')
            
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            
            if len(billing_cycle) == 2:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + billing_cycle
            if len(billing_cycle) == 1:
                current_year = now.strftime('%Y')
                billing_cycle = current_year + "0" + billing_cycle

            if not billing_nb or not latn_id:
                logging2.warning("缺少必要参数 billing_nb 或 latn_id")
                response_data = {"status": "success", "message": "缺少必要参数 billing_nb 或 latn_id", "Is_user_profile": 0}
                return response_data

            logging2.info(f"查询用户资料: billing_nb={billing_nb}, latn_id={latn_id}")

            # 替换本地网
            replacements = [['##LATNID##', str(latn_id)]]
            params = [billing_cycle, billing_nb]

            # 执行SQL查询
            result = db_manager_instance.excute_sql(
                sql_name='QueryUserProfile',
                params=params,
                lst_replace_code_value=replacements
            )

            if not result:
                logging2.warning(f"未找到用户资料: billing_nb={billing_nb}, latn_id={latn_id}")
                input_details.append({"status": "success", "message": "未找到用户资料", "Is_user_profile": 0})

            # 假设查询结果是 [(prod_inst_id, acct_id, prod_id)]
            if len(result) > 0 and len(result[0]) == 3:
                prod_inst_id, acct_id, prod_id = result[0]
                logging2.info(f"查询成功: PROD_INST_ID={prod_inst_id}, ACCT_ID={acct_id}, PROD_ID={prod_id}")

                # 成功响应
                response_data = {
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": prod_id,
                    "charge": charge,
                    "Is_user_profile": 1
                }
                input_details.append(response_data)
            else:
                logging2.error(f"查询结果格式不正确: {result}")
                input_details.append({"status": "success", "message": "查询用户资料失败，结果格式不正确", "Is_user_profile": 0})
                
        return jsonify(add_visualization_data("user_profile_service", {"input_details": input_details}, "查询用户资料", ""))
        
    except Exception as e:
        logging2.error(f"处理用户资料查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500

# 用户信息查询
@user_bp.route('/query/user_info', methods=['POST'])
def user_info():
    try:
        # 获取并解析请求数据
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)

        # 结果存储
        input_details = []
        prod_inst_id_set = set()  # 使用集合替代列表，提高查找效率

        # 遍历输入明细
        for row in json_dict.get('input_details', []):
            # 提取公共参数
            acct_id = row.get('ACCT_ID')
            billing_cycle = row.get('billing_cycle')
            latn_id = row.get('latn_id')
            prod_inst_id = row.get('PROD_INST_ID')
            billing_nb = row.get('billing_nb')
            charge = row.get('charge')

            # 查询相关产品实例
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, prod_inst_id]
            a_rows = db_manager_instance.excute_sql(
                sql_name='QueryProdInstRel_a_100800',
                params=params,
                lst_replace_code_value=replacements
            )

            # 处理查询结果
            for a_prod_inst_id in a_rows:
                # 去重逻辑优化
                if a_prod_inst_id in prod_inst_id_set:
                    continue
                # 构建响应数据
                response_data = {
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": row.get('PROD_ID'),
                    "charge": charge,
                    "Is_user_profile": 1
                }
                # 添加到结果集
                input_details.append(response_data)
                prod_inst_id_set.add(a_prod_inst_id)
            
            logging2.info(f"prod_inst_id_set:{prod_inst_id_set.__len__()}")
            if prod_inst_id_set.__len__() == 0:
                # 构建响应数据
                response_data = {
                    "message": "用户资料信息",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "latn_id": latn_id,
                    "PROD_INST_ID": prod_inst_id,
                    "ACCT_ID": acct_id,
                    "PROD_ID": row.get('PROD_ID'),
                    "charge": charge,
                    "Is_user_profile": 1
                }
                # 添加到结果集
                input_details.append(response_data)

        logging2.info(f"user_info:{input_details}")
        return {"status": "success", "input_details": input_details}
    
    except Exception as e:
        logging2.error(f"处理用户资料查询请求时出错: {str(e)}")
        logging2.error(f'{traceback.format_exc()}')
        return jsonify({"status": "error", "message": str(e)}), 500

# 查询主副卡关系
@user_bp.route('/query/userrel_100800_service', methods=['POST'])
def userrel_100800_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        accNum_detail = []
        
        for row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge = row.get('charge')

            # 校验必要参数
            if not prod_inst_id or not latn_id or not billing_cycle or len(billing_cycle) != 6:
                logging2.warning("缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle")
            else:
                # 查询A1单产品套餐信息
                replacements = [['##LATNID##', str(latn_id)]]
                params = [prod_inst_id, prod_inst_id]
                a_rows = db_manager_instance.excute_sql(
                    sql_name='QueryProdInstRel_a_100800',
                    params=params,
                    lst_replace_code_value=replacements
                )
                
                for row in a_rows:
                    a_prod_inst_id = row
                    params = [a_prod_inst_id]
                    a1_rows = db_manager_instance.excute_sql(
                        sql_name='QueryProdInstRel_100800',
                        params=params,
                        lst_replace_code_value=replacements
                    )
                    
                    for arow in a1_rows:
                        a_accNum = ''
                        z_accNum = ''
                        a_prod_inst_id, z_prod_inst_id, eff_date, exp_date = arow
                        
                        params = [a_prod_inst_id]
                        a2_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInst',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for brow in a2_rows:
                            a_accNum = brow
                            
                        params = [z_prod_inst_id]
                        a3_rows = db_manager_instance.excute_sql(
                            sql_name='QueryProdInst',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        for crow in a3_rows:
                            z_accNum = crow
                            
                        accNum_detail.append({
                            "主卡号码": a_accNum,
                            "副卡号码": z_accNum,
                            "生效时间": eff_date,
                            "失效时间": exp_date
                        })
        
        return jsonify(
            add_visualization_data("userrel_100800_service", {"accNum_detail_10080": accNum_detail}, "主副卡关系", ""))
    
    except Exception as e:
        logging2.error(f"处理主副卡关系查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500

