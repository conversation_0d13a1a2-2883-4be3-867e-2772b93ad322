import json
import os


class ConfigLoader:
    """配置加载器，负责读取和解析配置文件"""

    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> dict:
        """加载配置文件内容"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            print(f"配置文件不存在: {self.config_path}")
            return {}
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return {}

    def get_url(self, key: str = "default_url") -> str:
        """获取配置中的URL"""
        return self.config.get(key, "")

    def reload(self) -> None:
        """重新加载配置"""
        self.config = self._load_config()