<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强数据可视化卡片生成器（支持堆叠柱状图）</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f0f4f8 0%, #d9e6f2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(90deg, #3498db, #2c3e50);
            color: white;
            padding: 30px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            transform: rotate(30deg);
        }
        
        h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            position: relative;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.92;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.7;
            position: relative;
        }
        
        .content {
            display: flex;
            flex-wrap: wrap;
            min-height: 80vh;
        }
        
        .input-section, .output-section {
            padding: 30px;
            flex: 1;
            min-width: 300px;
        }
        
        .input-section {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
        }
        
        .output-section {
            background: #ffffff;
            display: flex;
            flex-direction: column;
        }
        
        .section-title {
            font-size: 1.5rem;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
            color: #2c3e50;
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .section-title::after {
            content: "";
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 80px;
            height: 3px;
            background: #2ecc71;
        }
        
        .section-title i {
            margin-right: 12px;
            color: #3498db;
            font-size: 1.8rem;
        }
        
        .json-input {
            width: 100%;
            height: 350px;
            padding: 18px;
            border: 2px solid #cbd5e0;
            border-radius: 10px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 15px;
            resize: vertical;
            transition: all 0.3s;
            background: #2d3748;
            color: #e2e8f0;
            box-shadow: inset 0 2px 6px rgba(0,0,0,0.15);
        }
        
        .json-input:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.25), inset 0 2px 6px rgba(0,0,0,0.15);
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            margin: 25px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .render-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            flex: 1;
        }
        
        .render-btn:hover {
            background: linear-gradient(135deg, #2980b9, #2573a7);
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(52, 152, 219, 0.3);
        }
        
        .example-btn {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }
        
        .example-btn:hover {
            background: linear-gradient(135deg, #27ae60, #219653);
            transform: translateY(-3px);
        }
        
        .clear-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .clear-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-3px);
        }
        
        .example-type-btn {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            flex: 1;
            min-width: 130px;
        }
        
        .example-type-btn:hover {
            background: linear-gradient(135deg, #8e44ad, #7d3c98);
            transform: translateY(-3px);
        }
        
        .output-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            padding: 10px;
        }
        
        .card {
            background: white;
            border-radius: 14px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
            animation: fadeIn 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .card-title {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 2px solid #edf2f7;
        }
        
        .card-title i {
            margin-right: 15px;
            color: #3498db;
            font-size: 1.8rem;
            background: #ebf5fb;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
        }
        
        .chart-container {
            position: relative;
            height: 320px;
            margin-top: 20px;
            margin-bottom: 25px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        
        th {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
            text-align: left;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        td {
            padding: 14px 20px;
            border-bottom: 1px solid #edf2f7;
        }
        
        tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        tr:hover {
            background-color: #ebf5fb;
        }
        
        .chart-description {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
            font-size: 16px;
            line-height: 1.7;
            color: #4a5568;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
        }
        
        .error-message {
            background: #fef2f2;
            color: #ef4444;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #ef4444;
            margin-top: 20px;
            font-family: monospace;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .text-content {
            line-height: 1.8;
            font-size: 17px;
            padding: 15px 0;
            color: #4a5568;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: #718096;
        }
        
        .empty-state i {
            font-size: 5rem;
            margin-bottom: 25px;
            color: #cbd5e0;
            opacity: 0.7;
        }
        
        .empty-state h3 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .empty-state p {
            font-size: 1.1rem;
            max-width: 500px;
            line-height: 1.6;
        }
        
        .instructions {
            background: #fff9ed;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid #ffecb3;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .instructions ul {
            margin-left: 25px;
            margin-top: 15px;
            line-height: 1.9;
        }
        
        .instructions li {
            margin-bottom: 12px;
            font-size: 15px;
        }
        
        .instructions code {
            background: #f1f5f9;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: monospace;
            color: #2d3748;
            border: 1px solid #e2e8f0;
        }
        
        .chart-type-badge {
            display: inline-block;
            background: #3498db;
            color: white;
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 12px;
            margin-left: 10px;
            vertical-align: middle;
        }
        
        footer {
            text-align: center;
            padding: 25px;
            background: #2c3e50;
            color: #e2e8f0;
            font-size: 1rem;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .content {
                flex-direction: column;
            }
            
            .input-section {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .json-input {
                height: 280px;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            h1 {
                font-size: 2.2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>增强数据可视化卡片生成器</h1>
            <p class="subtitle">支持柱状图、堆叠柱状图、趋势图、饼图、表格和文本卡片渲染，每个图表下方可添加描述性文本</p>
        </header>
        
        <div class="content">
            <div class="input-section">
                <h2 class="section-title">
                    <i class="fas fa-code"></i> JSON 数据输入
                </h2>
                <textarea id="jsonInput" class="json-input" placeholder='在此粘贴JSON数据...&#10;例如：&#10;{&#10;  "chart_type": "bar",&#10;  "title": "月度销售额统计",&#10;  "description": "此图表展示了2023年各月销售额情况...",&#10;  "data": { ... }&#10;}'></textarea>
                
                <div class="btn-group">
                    <button id="renderBtn" class="render-btn">
                        <i class="fas fa-rocket"></i> 渲染数据
                    </button>
                    <button id="exampleBtn" class="example-btn">
                        <i class="fas fa-magic"></i> 随机示例
                    </button>
                    <button id="clearBtn" class="clear-btn">
                        <i class="fas fa-broom"></i> 清空
                    </button>
                </div>
                
                <h3 style="margin-top: 25px; margin-bottom: 15px; color: #2c3e50; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-chart-line"></i> 加载特定示例：
                </h3>
                <div class="btn-group">
                    <button id="barExampleBtn" class="example-type-btn">
                        <i class="fas fa-chart-bar"></i> 柱状图
                    </button>
                    <button id="stackedBarExampleBtn" class="example-type-btn">
                        <i class="fas fa-layer-group"></i> 堆叠柱状图
                    </button>
                    <button id="lineExampleBtn" class="example-type-btn">
                        <i class="fas fa-chart-line"></i> 趋势图
                    </button>
                    <button id="pieExampleBtn" class="example-type-btn">
                        <i class="fas fa-chart-pie"></i> 饼图
                    </button>
                    <button id="tableExampleBtn" class="example-type-btn">
                        <i class="fas fa-table"></i> 表格
                    </button>
                    <button id="textExampleBtn" class="example-type-btn">
                        <i class="fas fa-font"></i> 文本
                    </button>
                </div>
                
                <div class="instructions">
                    <h3><i class="fas fa-info-circle"></i> JSON格式说明：</h3>
                    <ul>
                        <li><code>chart_type</code>: bar（柱状图）, stacked_bar（堆叠柱状图）, line（趋势图）, pie（饼图）, table（表格）, text（文本）</li>
                        <li><code>title</code>: 卡片标题</li>
                        <li><code>description</code>: 图表描述文本（可选）</li>
                        <li><code>data</code>: 图表/表格/文本数据</li>
                        <li>柱状图/堆叠柱状图/趋势图数据格式: <code>labels</code> 和 <code>datasets</code></li>
                        <li>堆叠柱状图需添加<code>"stacked": true</code></li>
                        <li>表格数据格式: <code>headers</code> 和 <code>rows</code></li>
                        <li>文本数据格式: <code>content</code> 字段</li>
                    </ul>
                </div>
            </div>
            
            <div class="output-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-area"></i> 可视化输出
                </h2>
                <div id="outputContainer" class="output-container">
                    <div class="empty-state">
                        <i class="fas fa-database"></i>
                        <h3>等待数据输入</h3>
                        <p>请在左侧输入或粘贴JSON数据，然后点击"渲染数据"按钮</p>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>© 2023 增强数据可视化卡片生成器 | 支持堆叠柱状图 | 基于Chart.js开发</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const jsonInput = document.getElementById('jsonInput');
            const renderBtn = document.getElementById('renderBtn');
            const exampleBtn = document.getElementById('exampleBtn');
            const clearBtn = document.getElementById('clearBtn');
            const outputContainer = document.getElementById('outputContainer');
            const barExampleBtn = document.getElementById('barExampleBtn');
            const stackedBarExampleBtn = document.getElementById('stackedBarExampleBtn');
            const lineExampleBtn = document.getElementById('lineExampleBtn');
            const pieExampleBtn = document.getElementById('pieExampleBtn');
            const tableExampleBtn = document.getElementById('tableExampleBtn');
            const textExampleBtn = document.getElementById('textExampleBtn');
            
            // 渲染柱状图
            function renderBarChart(data, card) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const container = document.createElement('div');
                container.className = 'chart-container';
                container.appendChild(canvas);
                card.appendChild(container);
                
                const datasets = data.datasets.map(dataset => {
                    return {
                        label: dataset.label,
                        data: dataset.data,
                        backgroundColor: dataset.backgroundColor,
                        borderColor: dataset.backgroundColor.replace(')', ', 0.8)').replace('rgb', 'rgba'),
                        borderWidth: 1
                    };
                });
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: data.show_legend !== false,
                                position: 'top'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: !!data.x_axis_title,
                                    text: data.x_axis_title || 'X轴'
                                },
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                title: {
                                    display: !!data.y_axis_title,
                                    text: data.y_axis_title || 'Y轴'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // 渲染堆叠柱状图
            function renderStackedBarChart(data, card) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const container = document.createElement('div');
                container.className = 'chart-container';
                container.appendChild(canvas);
                card.appendChild(container);
                
                const datasets = data.datasets.map(dataset => {
                    return {
                        label: dataset.label,
                        data: dataset.data,
                        backgroundColor: dataset.backgroundColor,
                        borderColor: dataset.backgroundColor.replace(')', ', 0.8)').replace('rgb', 'rgba'),
                        borderWidth: 1
                    };
                });
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: data.show_legend !== false,
                                position: 'top'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        },
                        scales: {
                            x: {
                                stacked: true,
                                title: {
                                    display: !!data.x_axis_title,
                                    text: data.x_axis_title || 'X轴'
                                },
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                stacked: true,
                                title: {
                                    display: !!data.y_axis_title,
                                    text: data.y_axis_title || 'Y轴'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // 渲染折线图（趋势图）
            function renderLineChart(data, card) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const container = document.createElement('div');
                container.className = 'chart-container';
                container.appendChild(canvas);
                card.appendChild(container);
                
                const datasets = data.datasets.map(dataset => {
                    return {
                        label: dataset.label,
                        data: dataset.data,
                        borderColor: dataset.backgroundColor,
                        backgroundColor: dataset.backgroundColor.replace(')', ', 0.1)').replace('rgb', 'rgba'),
                        borderWidth: 3,
                        pointBackgroundColor: dataset.backgroundColor,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        tension: 0.3,
                        fill: true
                    };
                });
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: data.show_legend !== false,
                                position: 'top'
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: !!data.x_axis_title,
                                    text: data.x_axis_title || 'X轴'
                                }
                            },
                            y: {
                                title: {
                                    display: !!data.y_axis_title,
                                    text: data.y_axis_title || 'Y轴'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // 渲染饼图
            function renderPieChart(data, card) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const container = document.createElement('div');
                container.className = 'chart-container';
                container.appendChild(canvas);
                card.appendChild(container);
                
                // 饼图通常只有一个数据集
                const dataset = data.datasets[0];
                
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: dataset.data,
                            backgroundColor: dataset.backgroundColor,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: data.show_legend !== false,
                                position: 'right'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
            
            // 渲染表格
            function renderTable(data, card) {
                const table = document.createElement('table');
                
                // 创建表头
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                data.headers.forEach(header => {
                    const th = document.createElement('th');
                    th.textContent = header;
                    headerRow.appendChild(th);
                });
                thead.appendChild(headerRow);
                table.appendChild(thead);
                
                // 创建表格内容
                const tbody = document.createElement('tbody');
                data.rows.forEach(rowData => {
                    const row = document.createElement('tr');
                    rowData.forEach(cellData => {
                        const td = document.createElement('td');
                        td.textContent = cellData;
                        row.appendChild(td);
                    });
                    tbody.appendChild(row);
                });
                table.appendChild(tbody);
                
                card.appendChild(table);
            }
            
            // 渲染文本卡片
            function renderText(data, card) {
                const contentDiv = document.createElement('div');
                contentDiv.className = 'text-content';
                
                // 支持多行文本（使用数组）或单行文本（字符串）
                if (Array.isArray(data.content)) {
                    data.content.forEach(paragraph => {
                        const p = document.createElement('p');
                        p.textContent = paragraph;
                        contentDiv.appendChild(p);
                    });
                } else {
                    contentDiv.textContent = data.content;
                }
                
                card.appendChild(contentDiv);
            }
            
            // 渲染可视化卡片
            function renderVisualization(jsonData) {
                outputContainer.innerHTML = '';
                
                try {
                    if (!jsonData) {
                        throw new Error('没有输入数据');
                    }
                    
                    const card = document.createElement('div');
                    card.className = 'card';
                    
                    const title = document.createElement('h3');
                    title.className = 'card-title';
                    
                    // 根据图表类型设置图标
                    let iconClass, typeText;
                    switch (jsonData.chart_type) {
                        case 'bar':
                            iconClass = 'fas fa-chart-bar';
                            typeText = '柱状图';
                            break;
                        case 'stacked_bar':
                            iconClass = 'fas fa-layer-group';
                            typeText = '堆叠柱状图';
                            break;
                        case 'line':
                            iconClass = 'fas fa-chart-line';
                            typeText = '趋势图';
                            break;
                        case 'pie':
                            iconClass = 'fas fa-chart-pie';
                            typeText = '饼图';
                            break;
                        case 'table':
                            iconClass = 'fas fa-table';
                            typeText = '表格';
                            break;
                        case 'text':
                            iconClass = 'fas fa-file-alt';
                            typeText = '文本';
                            break;
                        default:
                            iconClass = 'fas fa-chart-area';
                            typeText = '图表';
                    }
                    
                    title.innerHTML = `<i class="${iconClass}"></i> ${jsonData.title || '数据卡片'} <span class="chart-type-badge">${typeText}</span>`;
                    card.appendChild(title);
                    
                    // 渲染图表主体
                    switch (jsonData.chart_type) {
                        case 'bar':
                            renderBarChart(jsonData.data, card);
                            break;
                        case 'stacked_bar':
                            renderStackedBarChart(jsonData.data, card);
                            break;
                        case 'line':
                            renderLineChart(jsonData.data, card);
                            break;
                        case 'pie':
                            renderPieChart(jsonData.data, card);
                            break;
                        case 'table':
                            renderTable(jsonData.data, card);
                            break;
                        case 'text':
                            renderText(jsonData.data, card);
                            break;
                        default:
                            throw new Error(`不支持的图表类型: ${jsonData.chart_type}`);
                    }
                    
                    // 添加描述区域
                    if (jsonData.description) {
                        const desc = document.createElement('div');
                        desc.className = 'chart-description';
                        desc.innerHTML = `<strong><i class="fas fa-comment-alt"></i> 描述：</strong> ${jsonData.description}`;
                        card.appendChild(desc);
                    }
                    
                    outputContainer.appendChild(card);
                } catch (error) {
                    outputContainer.innerHTML = `
                        <div class="error-message">
                            <strong><i class="fas fa-exclamation-triangle"></i> 渲染错误:</strong> ${error.message}
                            <div style="margin-top: 15px; font-size: 0.95em; color: #777;">
                                请检查JSON格式是否正确，并确保包含必要的字段。
                            </div>
                        </div>
                    `;
                    console.error('渲染错误:', error);
                }
            }
            
            // 示例数据集合（带描述）
            const examples = {
                bar: {
                    chart_type: "bar",
                    title: "2023年季度销售额统计",
                    description: "此图表展示了2023年四个季度中不同产品的销售额情况。从数据可以看出，产品A在第三季度达到峰值，而产品C呈现稳定增长趋势。",
                    data: {
                        labels: ["第一季度", "第二季度", "第三季度", "第四季度"],
                        datasets: [
                            {
                                label: "产品A",
                                data: [250, 320, 410, 380],
                                backgroundColor: "#3498db"
                            },
                            {
                                label: "产品B",
                                data: [180, 240, 320, 290],
                                backgroundColor: "#2ecc71"
                            },
                            {
                                label: "产品C",
                                data: [120, 180, 220, 260],
                                backgroundColor: "#e74c3c"
                            }
                        ],
                        x_axis_title: "季度",
                        y_axis_title: "销售额（万元）",
                        show_legend: true
                    }
                },
                stacked_bar: {
                    chart_type: "stacked_bar",
                    title: "2023年部门成本构成",
                    description: "此堆叠柱状图展示了2023年各季度不同部门的成本构成情况。可以看出人力成本是主要支出，研发成本在第三季度有显著增加。",
                    data: {
                        labels: ["第一季度", "第二季度", "第三季度", "第四季度"],
                        datasets: [
                            {
                                label: "人力成本",
                                data: [45, 48, 52, 50],
                                backgroundColor: "#3498db"
                            },
                            {
                                label: "研发成本",
                                data: [20, 22, 35, 30],
                                backgroundColor: "#2ecc71"
                            },
                            {
                                label: "营销成本",
                                data: [15, 18, 20, 25],
                                backgroundColor: "#e74c3c"
                            },
                            {
                                label: "行政成本",
                                data: [10, 12, 11, 13],
                                backgroundColor: "#f39c12"
                            }
                        ],
                        x_axis_title: "季度",
                        y_axis_title: "成本（万元）",
                        show_legend: true
                    }
                },
                line: {
                    chart_type: "line",
                    title: "网站月度访问量趋势",
                    description: "此趋势图对比了2022年与2023年的月度访问量数据。可以看出2023年访问量整体高于去年，尤其是在下半年增长更为显著，11月和12月达到年度峰值。",
                    data: {
                        labels: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
                        datasets: [
                            {
                                label: "2023年",
                                data: [8500, 9200, 10500, 9800, 11200, 12500, 13200, 12800, 14500, 13800, 15200, 16800],
                                backgroundColor: "#9b59b6"
                            },
                            {
                                label: "2022年",
                                data: [7200, 7800, 8500, 8200, 9100, 9800, 10500, 10200, 11200, 10800, 12000, 13200],
                                backgroundColor: "#e67e22"
                            }
                        ],
                        x_axis_title: "月份",
                        y_axis_title: "访问量",
                        show_legend: true
                    }
                },
                pie: {
                    chart_type: "pie",
                    title: "市场份额分布",
                    description: "饼图展示了2023年主要科技公司在全球市场的份额分布情况。谷歌以38.5%的份额领先，其次是苹果(23.8%)和微软(14.6%)，其他公司合计占据13.9%的市场份额。",
                    data: {
                        labels: ["谷歌", "苹果", "微软", "亚马逊", "其他"],
                        datasets: [
                            {
                                data: [38.5, 23.8, 14.6, 9.2, 13.9],
                                backgroundColor: [
                                    "#3498db",
                                    "#2ecc71",
                                    "#e74c3c",
                                    "#f39c12",
                                    "#9b59b6"
                                ]
                            }
                        ],
                        show_legend: true
                    }
                },
                table: {
                    chart_type: "table",
                    title: "员工绩效数据",
                    description: "此表格展示了2023年第三季度各部门员工的绩效完成情况。销售部的张三超额完成目标，技术部的李四也表现出色，而市场部的王五未达到预期目标。",
                    data: {
                        headers: ["姓名", "部门", "季度目标", "实际完成", "完成率"],
                        rows: [
                            ["张三", "销售部", "120万", "135万", "112.5%"],
                            ["李四", "技术部", "3个项目", "4个项目", "133.3%"],
                            ["王五", "市场部", "5000线索", "4800线索", "96%"],
                            ["赵六", "客服部", "98%满意度", "99%满意度", "101%"],
                            ["钱七", "财务部", "0差错", "0差错", "100%"]
                        ]
                    }
                },
                text: {
                    chart_type: "text",
                    title: "2023年年度总结报告",
                    description: "此报告总结了公司2023年的整体业绩和发展情况，并提出了2024年的战略目标和计划。",
                    data: {
                        content: [
                            "2023年，公司整体业绩取得了显著增长，总销售额达到1.25亿元，同比增长28%。",
                            "在产品方面，我们成功推出了三款新产品，其中智能家居解决方案在市场上获得了热烈反响，占据了15%的新市场份额。",
                            "在团队建设方面，我们扩大了研发团队规模，引进了5名高级技术专家，进一步增强了公司的技术创新能力。",
                            "展望2024年，公司计划：",
                            "1. 拓展海外市场，特别是东南亚地区",
                            "2. 加强AI技术在产品中的应用",
                            "3. 提升客户服务质量，目标客户满意度达到98%",
                            "4. 实现全年销售额增长30%的目标"
                        ]
                    }
                }
            };
            
            // 随机选择一个示例
            function getRandomExample() {
                const keys = Object.keys(examples);
                const randomKey = keys[Math.floor(Math.random() * keys.length)];
                return examples[randomKey];
            }
            
            // 事件监听
            renderBtn.addEventListener('click', function() {
                try {
                    const jsonData = JSON.parse(jsonInput.value);
                    renderVisualization(jsonData);
                } catch (error) {
                    outputContainer.innerHTML = `
                        <div class="error-message">
                            <strong><i class="fas fa-exclamation-circle"></i> JSON解析错误:</strong> ${error.message}
                            <div style="margin-top: 15px; font-size: 0.95em; color: #777;">
                                请确保输入的是有效的JSON格式。
                            </div>
                        </div>
                    `;
                }
            });
            
            exampleBtn.addEventListener('click', function() {
                const example = getRandomExample();
                jsonInput.value = JSON.stringify(example, null, 2);
                renderVisualization(example);
            });
            
            clearBtn.addEventListener('click', function() {
                jsonInput.value = '';
                outputContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-database"></i>
                        <h3>数据已清空</h3>
                        <p>请在左侧输入或粘贴JSON数据，然后点击"渲染数据"按钮</p>
                    </div>
                `;
            });
            
            // 特定示例按钮
            barExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.bar, null, 2);
                renderVisualization(examples.bar);
            });
            
            stackedBarExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.stacked_bar, null, 2);
                renderVisualization(examples.stacked_bar);
            });
            
            lineExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.line, null, 2);
                renderVisualization(examples.line);
            });
            
            pieExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.pie, null, 2);
                renderVisualization(examples.pie);
            });
            
            tableExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.table, null, 2);
                renderVisualization(examples.table);
            });
            
            textExampleBtn.addEventListener('click', function() {
                jsonInput.value = JSON.stringify(examples.text, null, 2);
                renderVisualization(examples.text);
            });
        });
    </script>
</body>
</html>