import json
import logging2
from collections import defaultdict
from api_services.config.globals import db_manager_instance


def _parse_params_content(params_str, business_id):
    """
    根据business_id解析params参数，生成对应的Content内容
    """
    try:
        if not params_str:
            return "参数为空"
        
        # 解析JSON字符串
        params = json.loads(params_str)
        
        if business_id == 7783:  # 套外15G断网
            used = params.get('used', '')
            balance = params.get('balance', '')
            return f"已使用流量{used}，再使用{balance}流量达到套外15G断网"
            
        elif business_id == 6522:  # 政企断网
            return "政企断网"
            
        elif business_id == 6523:  # 语音卡套外超出1G断网
            used = params.get('used', '')
            balance = params.get('balance', '')
            return f"已使用流量{used}，再使用{balance}流量达到语音卡套外超出1G断网"
            
        elif business_id in [7796, 5425, 5424, 7618]:  # 降速提醒
            used = params.get('used', '')
            netspeed = params.get('netspeed', '')
            return f"降速提醒,已使用流量{used}G，降速的速率为{netspeed}"
            
        elif business_id == 5703:  # 不限量阈值提醒
            used = params.get('used', '')
            first_limit = params.get('firstLimit', '')
            return f"不限量阈值提醒,已使用流量{used}G,首次降速流量使用量阈值{first_limit}G"
            
        elif business_id == 7676:  # 阀值提醒普通流量80%和100%
            used = params.get('used', '')
            first_limit = params.get('firstLimit', '')
            return f"阀值提醒提醒,已使用流量{used}G,首次降速流量使用量阈值{first_limit}G"
            
        elif business_id in [5321, 7799]:  # 费用超量提醒
            card_fee = params.get('card_fee', '')
            cost = params.get('cost', '')
            return f"费用超量提醒,{card_fee}总的超出费用{cost}元"
            
        else:
            return "未知业务类型"
            
    except json.JSONDecodeError:
        logging2.error(f"解析params参数失败: {params_str}")
        return "参数解析失败"
    except Exception as e:
        logging2.error(f"解析params内容时出错: {str(e)}")
        return "解析出错"


def filter_accu_data(json_data, accu_type_id_set):
    """
    解析JSON报文并按offerId分组筛选指定accuTypeId的数据

    参数:
        json_data: JSON格式的字符串数据
        accu_type_id_set: 需要筛选的accuTypeId集合

    返回:
        按offerId分组筛选后的结果列表
    """
    # 解析JSON数据
    data = json.loads(json_data)

    # 用于存储按offerId分组的结果
    offer_results = {}

    # 遍历每个优惠实例信息
    for offer_info in data.get("detailOfferInstInfo", []):
        offer_id = offer_info.get("offerId")
        filtered_users = []

        # 遍历每个用户账户信息
        for user_acc in offer_info.get("accuQryUserList", []):
            # 检查accuTypeId是否在指定集合中
            if user_acc.get("accuTypeId") in accu_type_id_set:
                # 提取符合条件的用户账户信息
                filtered_users.append({
                    "accNbr": user_acc.get("accNbr"),
                    "accuTypeId": user_acc.get("accuTypeId"),
                    "accuTypeAttr": user_acc.get("accuTypeAttr"),
                    "servTypeId": user_acc.get("servTypeId"),
                    "destinationAttr": user_acc.get("destinationAttr"),
                    "usageAmount": user_acc.get("usageAmount"),
                    "unitTypeId": user_acc.get("unitTypeId"),
                    "accuId": user_acc.get("accuId")
                })

        # 如果该offer下有符合条件的用户，添加到结果中
        if filtered_users:
            offer_results[offer_id] = filtered_users

    # 将结果转换为所需的格式
    result_list = [{"offerId": offer_id, "accuQryUserList": users}
                   for offer_id, users in offer_results.items()]

    return result_list


def _process_internet_detail_data(api_result, billing_nb, billing_cycle):
    """
    处理OPENAPI返回的上网详单数据
    1. 按时间排序，取第一条费用>0的记录的关键信息
    2. 按日统计流量和费用
    """
    try:
        data_bill_items = api_result.get("dataBillItems", [])
        
        if not data_bill_items:
            return {
                "status": "success",
                "message": "未查询到上网详单数据",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "startTime": "",
                "callingArea": "",
                "rg": "",
                "bsid": "",
                "Daysum": []
            }

        # 按startTime排序（最早到最晚）
        sorted_items = sorted(data_bill_items, key=lambda x: x.get("startTime", ""))
        
        # 查找第一条费用>0的记录
        first_fee_record = None
        for item in sorted_items:
            try:
                fee_value = int(item.get("fee", 0)) if item.get("fee", 0) != "" else 0
            except (ValueError, TypeError):
                fee_value = 0
            
            if fee_value > 0:
                first_fee_record = {
                    "startTime": item.get("startTime", ""),
                    "callingArea": item.get("callingArea", ""),
                    "rg": item.get("rg", ""),
                    "bsid": item.get("bsid", "")
                }
                break
        
        # 如果没有找到费用>0的记录，使用第一条记录
        if not first_fee_record and sorted_items:
            first_item = sorted_items[0]
            first_fee_record = {
                "startTime": first_item.get("startTime", ""),
                "callingArea": first_item.get("callingArea", ""),
                "rg": first_item.get("rg", ""),
                "bsid": first_item.get("bsid", "")
            }

        # 按日统计流量和费用
        daily_summary = {}
        for item in data_bill_items:
            start_time = item.get("startTime", "")
            if len(start_time) >= 8:
                # 提取日期部分（YYYYMMDD）
                day_key = start_time[:8]
                
                if day_key not in daily_summary:
                    daily_summary[day_key] = {
                        "fee": 0,
                        "volume": 0
                    }
                
                # 获取当前项的费用和流量，转换为数值类型
                try:
                    item_fee = int(item.get("fee", 0)) if item.get("fee", 0) != "" else 0
                except (ValueError, TypeError):
                    item_fee = 0
                
                try:
                    item_volume = int(item.get("volume", 0)) if item.get("volume", 0) != "" else 0
                except (ValueError, TypeError):
                    item_volume = 0
                
                # 累加费用
                daily_summary[day_key]["fee"] += item_fee
                
                # 只有当费用大于0时才累加流量
                if item_fee > 0:
                    daily_summary[day_key]["volume"] += item_volume

        # 转换为列表格式并按日期排序，过滤掉费用为0的日期
        daysum_list = []
        for day_key in sorted(daily_summary.keys()):
            day_fee = daily_summary[day_key]["fee"]
            # 只有费用大于0的日期才添加到结果中
            if day_fee > 0:
                daysum_list.append({
                    "Daytime": day_key,
                    "fee": day_fee,
                    "volume": daily_summary[day_key]["volume"]
                })

        # 构建返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "startTime": first_fee_record.get("startTime", "") if first_fee_record else "",
            "callingArea": first_fee_record.get("callingArea", "") if first_fee_record else "",
            "rg": first_fee_record.get("rg", "") if first_fee_record else "",
            "bsid": first_fee_record.get("bsid", "") if first_fee_record else "",
            "Daysum": daysum_list
        }

        return response_data

    except Exception as e:
        logging2.error(f"处理上网详单数据时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"未查到上网详单数据",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "startTime": "",
            "callingArea": "",
            "rg": "",
            "bsid": "",
            "Daysum": []
        }


def query_remain_accu_info(latn_id, offer_inst_id, accu_type_id, obj_type, obj_id):
    """
    查询量本分表accumulation_sub_[latn_id]_[offer_inst_id%10]，返回量本信息
    """
    global db_manager_instance
    # 动态表名替换
    mod_raw = int(offer_inst_id) % 10
    mod_adjusted = mod_raw if mod_raw != 0 else 10
    mod_val = f'{mod_adjusted:02d}'
    lst_replace_code_value = [['##LATNID##', str(latn_id)], ['##MODVAL##', mod_val]]
    params = [offer_inst_id, accu_type_id, obj_type, obj_id]
    try:
        rows = db_manager_instance.excute_sql('QueryRemainAccuInfo', params=params, lst_replace_code_value=lst_replace_code_value)
        return rows
    except Exception as e:
        logging2.error(f"QueryRemainAccuInfo error: {e}")
        return []
    

def query_accu_type_name(accu_type_id):
    """
    通过accu_type_id查询accu_type表，获取ACCU_TYPE_NAME
    """
    global db_manager_instance
    try:
        rows = db_manager_instance.excute_sql('QueryAccuTypeNameById', params=[accu_type_id])
        if rows and len(rows) > 0:
            return rows[0][0]
        else:
            return None
    except Exception as e:
        logging2.error(f"QueryAccuTypeNameById error: {e}")
        return None


def get_latn_id_if_missing(billing_nb, latn_id):
    """
    获取本地网ID
    """
    if latn_id:
        return latn_id, None

    try:
        result = db_manager_instance.excute_sql(
            sql_name='QueryLatnIdByBillingNbr',
            params=[billing_nb]
        )
        if result and result[0] and result[0][0]:
            return result[0][0], None
        else:
            return None, "本地网查询失败"
    except Exception as e:
        logging2.error(f"查询本地网时出错: {e}")
        return None, "本地网查询异常"