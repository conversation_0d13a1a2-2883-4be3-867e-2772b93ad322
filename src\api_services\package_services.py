import json
import logging2
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify

from api_services.config.globals import db_manager_instance, get_config_path
from api_services.common.utils import get_latn_id_if_missing,filter_accu_data
from UnitTool import UnitTool
from configLoader import ConfigLoader
from Visualization import add_visualization_data

# 创建套餐服务蓝图
package_bp = Blueprint('package_services', __name__)

# 查询用户订购套外资费销售品信息
@package_bp.route('/query/unlimited_package_service', methods=['POST'])
def unlimited_package_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        valid_pkgs = []
        for  row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge=row.get('charge')

            # 校验必要参数
            if not prod_inst_id or not latn_id or not billing_cycle or len(billing_cycle) != 6:
                logging2.warning("缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle")
                input_details.append({"status": "success", "message": "缺少必要参数 PROD_INST_ID, latn_id 或 billing_cycle", "Is_unlimited_package": 0})
            else:
                # 查询A1单产品套餐信息
                replacements = [['##LATNID##', str(latn_id)]]
                params = [prod_inst_id,billing_cycle,billing_cycle]
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )

                cycle_int = int(billing_cycle)
                for row in a1_rows:
                    offer_inst_id, prod_offer_id, eff_date, exp_date, update_date, obj_id, obj_type,ext_offer_inst_id = row
                    try:
                        eff_ym = eff_date.year * 100 + eff_date.month
                        exp_ym = exp_date.year * 100 + exp_date.month
                    except:
                        continue
                    offer_id_to_name=""
                    if eff_ym <= cycle_int <= exp_ym:
                        #查询销售品名称
                        offer_name_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferNameByIds',
                            params=prod_offer_id
                        )
                        logging2.info(f"offer_name_rows={len(offer_name_rows)}")
                        for row in offer_name_rows:
                                offer_id_to_name=row[1]
                        else:
                            logging2.warning(f"No offer found for ID: {prod_offer_id}")
                        #查询不限量
                        grp_rows = db_manager_instance.excute_sql(
                            sql_name='QueryAccuTypeGroupMbr', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        is_unlimited=0
                        if grp_rows:
                            is_unlimited= 1
                        #查询提速包
                        ts_rows = db_manager_instance.excute_sql(
                            sql_name='QueryOfferPricingRelMainTag3', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        is_speedup=0
                        if ts_rows:
                            is_speedup= 1

                        #查询流量销售品
                        flowOffer_rows = db_manager_instance.excute_sql(
                                sql_name='QueryFlowOffer', params=[prod_offer_id], lst_replace_code_value=[]
                        )
                        if flowOffer_rows:
                            priorityOffer = []
                            priorityOffer_rows = db_manager_instance.excute_sql(
                                sql_name='QueryPriorityOffer', params=[prod_offer_id], lst_replace_code_value=[]
                            )
                            for pr_rows in priorityOffer_rows:
                                priorityOffer=pr_rows[0]

                            rows = db_manager_instance.excute_sql(
                                sql_name='QueryOfferTariff',
                                params=[prod_offer_id],
                                lst_replace_code_value=[]
                            )
                            
                            # 如果QueryOfferTariff查询到数据
                            if rows:
                                for row in rows:
                                    offer_id=row[0]
                                    value_string=row[1]
                                    pricing_plan_id=row[2]
                                    pricing_plan_name=row[3]
                                    tariff_unit=row[4]
                                    tariff_mark=row[5]
                                    tariff_desc=""
                                    logging2.info(f"unlimited_package_service.tariff_unit:{tariff_unit}")
                                    if tariff_unit=='1':
                                        tariff_desc=value_string+"分/G"
                                    else:
                                        if value_string:
                                            tariff_desc=str(float(value_string)/10)+"分/KB"
                                    
                                    # 查询是否为5G主套餐，判断流量费用封顶
                                    cap_limit_desc = "450元封顶"  # 默认非5G主套餐
                                    try:
                                        five_g_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryOffer5GMainPackage', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if five_g_rows:
                                            cap_limit_desc = "600元封顶"  # 5G主套餐
                                            logging2.info(f"offer_id {prod_offer_id} 是5G主套餐，使用600元封顶")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 是非5G主套餐，使用450元封顶")
                                    except Exception as cap_e:
                                        logging2.warning(f"查询5G主套餐状态时出错: {str(cap_e)}")

                                    # 查询是否为日累积租费
                                    daily_cumulative_rent_fee = ""  # 默认不是日累积租费
                                    try:
                                        daily_rent_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryDailyCumulativeRentFee', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if daily_rent_rows:
                                            daily_cumulative_rent_fee = "日累积租费"  # 是日累积租费
                                            logging2.info(f"offer_id {prod_offer_id} 是日累积租费")
                                        else:
                                            logging2.info(f"offer_id {prod_offer_id} 不是日累积租费")
                                    except Exception as rent_e:
                                        logging2.warning(f"查询日累积租费状态时出错: {str(rent_e)}")
                                    
                                    valid_pkgs.append({
                                        "offer_id": prod_offer_id,
                                        "offer_inst_id": offer_inst_id,
                                        "ext_offer_inst_id":ext_offer_inst_id,
                                        "offer_name":offer_id_to_name,
                                        "offer_priority":priorityOffer,
                                        "tariff_desc":tariff_desc,
                                        "tariff_mark":tariff_mark,
                                        "is_unlimited":is_unlimited,
                                        "is_speedup":is_speedup,
                                        "cap_limit_desc":cap_limit_desc,  # 流量费用封顶字段
                                        "daily_cumulative_rent_fee":daily_cumulative_rent_fee,  # 日累积租费字段
                                        "EFF_DATE": eff_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        "EXP_DATE": exp_date.strftime('%Y-%m-%d %H:%M:%S')
                                    })
                            else:
                                # 如果QueryOfferTariff查询不到数据，使用备用查询
                                logging2.info(f"QueryOfferTariff查询不到数据，使用QueryAlternativeOfferTariff查询: offer_id={prod_offer_id}")
                                alt_rows = db_manager_instance.excute_sql(
                                    sql_name='QueryAlternativeOfferTariff',
                                    params=[prod_offer_id],
                                    lst_replace_code_value=[]
                                )
                                
                                for alt_row in alt_rows:
                                    offer_id = alt_row[0]
                                    pricing_plan_id = alt_row[1]
                                    pricing_plan_name = alt_row[2]
                                    tariff_desc = ""
                                    tariff_mark = ""
                                    
                                    # 查询是否为5G主套餐，判断流量费用封顶
                                    cap_limit_desc = "450元封顶"  # 默认非5G主套餐
                                    try:
                                        five_g_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryOffer5GMainPackage', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if five_g_rows:
                                            cap_limit_desc = "600元封顶"  # 5G主套餐
                                    except Exception as cap_e:
                                        logging2.warning(f"查询5G主套餐状态时出错: {str(cap_e)}")

                                    # 查询是否为日累积租费
                                    daily_cumulative_rent_fee = ""
                                    try:
                                        daily_rent_rows = db_manager_instance.excute_sql(
                                            sql_name='QueryDailyCumulativeRentFee', 
                                            params=[prod_offer_id], 
                                            lst_replace_code_value=[]
                                        )
                                        if daily_rent_rows:
                                            daily_cumulative_rent_fee = "日累积租费"
                                    except Exception as rent_e:
                                        logging2.warning(f"查询日累积租费状态时出错: {str(rent_e)}")
                                    
                                    valid_pkgs.append({
                                        "offer_id": prod_offer_id,
                                        "offer_inst_id": offer_inst_id,
                                        "ext_offer_inst_id":ext_offer_inst_id,
                                        "offer_name":offer_id_to_name,
                                        "offer_priority":priorityOffer,
                                        "tariff_desc":tariff_desc,
                                        "tariff_mark":tariff_mark,
                                        "is_unlimited":is_unlimited,
                                        "is_speedup":is_speedup,
                                        "cap_limit_desc":cap_limit_desc,
                                        "daily_cumulative_rent_fee":daily_cumulative_rent_fee,
                                        "EFF_DATE": eff_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        "EXP_DATE": exp_date.strftime('%Y-%m-%d %H:%M:%S')
                                    })
        logging2.info(f"查询用户订购套餐明细务返回报文valid_pkgs={valid_pkgs}")
        return jsonify(
            add_visualization_data("unlimited_package_service", {
                "message": "用户订购套餐明细",
                "PROD_INST_ID": prod_inst_id, 
                "latn_id": latn_id, 
                "billing_cycle": billing_cycle,
                "valid_pkgs": valid_pkgs,
                **({"billing_nb": billing_nb} if billing_nb is not None else {})
            }, "用户订购套餐明细", ""))
    except Exception as e:
        logging2.error(f"处理不量套餐查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500

# 查询是否有剩余量本服务
@package_bp.route('/query/remain_accu_service', methods=['POST'])
def remain_accu_service():
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"请求参数: {data}")
        json_dict = json.loads(data)
        input_details=[]
        for  row in json_dict.get('input_details'):
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')
            ycharge=row.get('charge')
        if not latn_id or not billing_cycle:
            return jsonify({"status": "success", "message": "缺少必要参数latn_id、billing_cycle或offerAccuDetail", "Is_remain_accu": 0})
        replacements = [['##LATNID##', str(latn_id)]]
        params = [prod_inst_id,billing_cycle,billing_cycle]
        a1_rows = db_manager_instance.excute_sql(
            sql_name='QueryOfferProdInstRelHisA1Info',
            params=params,
            lst_replace_code_value=replacements
        )
        offer_detail=[]
        cycle_int = int(billing_cycle)
        last_cycle_int=cycle_int-1
        for row in a1_rows:
            offer_inst_id, prod_offer_id, eff_date, exp_date, update_date, obj_id, obj_type = row
            mod_raw = int(offer_inst_id) % 10
            mod_adjusted = mod_raw if mod_raw != 0 else 10
            mod_val = f'{mod_adjusted:02d}'
            lst_replace_code_value = [['##LATNID##', str(latn_id)], ['##MODVAL##', mod_val]]
            rows = db_manager_instance.excute_sql(
                sql_name='QueryRemainAccuInfo', params=[offer_inst_id,cycle_int,last_cycle_int,cycle_int],
                lst_replace_code_value=lst_replace_code_value
            )
            for row in rows:
                # row: offer_inst_id, accu_type_id, INIT_VAL, ACCU_USED_VAL, ACCU_VAL, EFF_DATE, EXP_DATE
                eff_date_str = row[5].strftime('%Y-%m-%d %H:%M:%S') if row[5] else None
                exp_date_str = row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None
                accu_type_id = row[1]
                accu_type_name=""
                logging2.info(f"accu_type_id={accu_type_id}")
                accu_type_rows = db_manager_instance.excute_sql(
                    sql_name='QueryAccuTypeNameById', params=[accu_type_id], lst_replace_code_value=[]
                )
                logging2.info(f"accu_type_rows={len(accu_type_rows)}")
                for rowname in accu_type_rows:
                    accu_type_name = rowname[0]
                logging2.info(f"accu_type_name={accu_type_name}")
                offer_detail.append({
                    "offer_inst_id":offer_inst_id,
                    "offer_id":prod_offer_id,
                    "accu_type_id": accu_type_id,
                    "accuTypeName": accu_type_name,
                    "INIT_VAL": row[2],
                    "ACCU_USED_VAL": row[3],
                    "ACCU_VAL": row[4],
                    "ACCU_EFF_DATE": eff_date_str,
                    "ACCU_EXP_DATE": exp_date_str
                })
        logging2.info(f"查询是否有剩余量本服务返回报文offer_detail={offer_detail}")
        return jsonify(
            add_visualization_data("remain_accu_service",
                                   {
                                       "message": "用户使用剩余量本明细",
                                       "offerAccuDetail": offer_detail
                                   },
                                   "用户使用剩余量本明细", ""))
    except Exception as e:
        logging2.error(f"处理剩余量本查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500

#量本使用量明细接口
@package_bp.route('/query/userResourceQueryDetailBon', methods=['POST'])
def userResourceQueryDetailBon():
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"量本使用量明细接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取关键参数
        accNbr = json_dict.get('accNbr')
        billingCycle = json_dict.get('billingCycle')

        # 收集套餐实例ID和累积类型ID
        offer_inst_ids = set()
        accu_type_ids = set()

        for offer in json_dict.get("offerInfo", []):
            offer_inst_id = offer.get("offerInstId")
            if offer_inst_id is not None:
                offer_inst_ids.add(offer_inst_id)

            # 修复空键问题，假设正确键名为'accuRecords'
            for accuRecord in offer.get("accuRecords", []):
                accuTypeId = accuRecord.get("accuTypeId")
                if accuTypeId is not None:
                    accu_type_ids.add(accuTypeId)

        logging2.info(f"userResourceQueryDetailBon accuTypeId_set:{accu_type_ids}")

        # 准备查询请求
        config_path = get_config_path()
        if not config_path:
            return jsonify({"status": "error", "message": "找不到配置文件config.json"})
            
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("AccuUseQryCenter")
        if not url:
            return jsonify({"status": "error", "message": "配置中未找到AccuUseQryCenter接口URL"})
        user_resourcedetails = []

        # 分批处理套餐实例ID查询
        batch_size = 10  # 每批处理的数量
        offer_batches = [list(offer_inst_ids)[i:i + batch_size] for i in range(0, len(offer_inst_ids), batch_size)]

        for batch in offer_batches:
            for offer_inst_id in batch:
                # 构建请求数据
                request_payload = {
                    "operAttrStruct": {
                        "lanId": 0,
                        "operOrgId": 0,
                        "operPost": 0,
                        "operServiceId": "string",
                        "operTime": "string",
                        "staffId": 11111111111
                    },
                    "svcObjectStruct": {
                        "objType": "3",
                        "objValue": accNbr,
                        "objAttr": "2",
                        "dataArea": "2"
                    },
                    "queryFlag": "3",
                    "billingCycle": int(billingCycle),
                    "offerInstId": offer_inst_id
                }

                logging2.info(f"userResourceQueryDetailBon data:{request_payload}")

                # 发送请求并处理结果
                try:
                    result = UnitTool.send_post_request(url, request_payload)
                    logging2.info(f"userResourceQueryDetailBon result:{result}")

                    if result:
                        filtered_data = filter_accu_data(result, accu_type_ids)
                        user_resourcedetails.append(filtered_data)
                except Exception as req_ex:
                    logging2.error(f"请求套餐实例ID {offer_inst_id} 时出错: {str(req_ex)}")
                    response_data = {
                        "status": "error",
                        "message": "查询失败",
                        "billing_nb": accNbr,  # 修正变量名
                        "billing_cycle": billingCycle,
                        "userResourcedetails": user_resourcedetails
                    }
                    return jsonify(response_data)
                    # 可以选择继续处理其他批次，或者根据业务需求决定是否终止

        logging2.info(f"userResourceQueryDetailBon userResourcedetails:{user_resourcedetails}")

        # 返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": accNbr,  # 修正变量名
            "billing_cycle": billingCycle,
            "userResourcedetails": user_resourcedetails
        }
        return jsonify(response_data)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({"status": "error", "message": "无效的请求格式"}), 400
    except Exception as e:
        logging2.error(f"量本使用量明细接口请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": "服务器内部错误"}), 500

# 查实时费用表、账单表判断资费销售品接口
@package_bp.route('/query/billing_offer_ids_service', methods=['POST'])
def billing_offer_ids_service():
    """
    查实时费用表、账单表判断资费销售品接口
    根据账期类型（1+1账期或其他月份）查询不同的表
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询资费销售品接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            prod_inst_id = row.get('PROD_INST_ID')
            billing_cycle_str = row.get('billing_cycle')
            latn_id = row.get('latn_id')  # 需要latn_id来替换SQL中的##LATNID##

            if not prod_inst_id or not billing_cycle_str:
                logging2.warning("缺少必要参数 PROD_INST_ID 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 PROD_INST_ID 或 billing_cycle",
                    "PROD_INST_ID": prod_inst_id
                })
                continue

            if not latn_id:
                logging2.warning("缺少必要参数 latn_id")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 latn_id",
                    "PROD_INST_ID": prod_inst_id
                })
                continue

            logging2.info(f"开始查询资费销售品: PROD_INST_ID={prod_inst_id}, billing_cycle={billing_cycle_str}, latn_id={latn_id}")

            # 解析billing_cycle，支持多个账期用逗号分隔
            billing_cycles = [cycle.strip() for cycle in billing_cycle_str.split(',')]
            
            # 获取当前年月和上一个月（1+1账期）
            now = datetime.now()
            current_ym = now.strftime('%Y%m')
            if now.month == 1:
                prev_ym = f"{now.year-1}12"
            else:
                prev_ym = f"{now.year}{now.month-1:02d}"
            
            # 定义1+1账期集合
            one_plus_one_cycles = {current_ym, prev_ym}
            
            logging2.info(f"1+1账期: {one_plus_one_cycles}, 查询账期: {billing_cycles}")

            offer_id_list = []
            
            # 处理每个账期
            for billing_cycle in billing_cycles:
                # 验证账期格式
                if len(billing_cycle) != 6 or not billing_cycle.isdigit():
                    logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                    continue
                
                logging2.info(f"处理账期: {billing_cycle}")
                
                try:
                    if billing_cycle in one_plus_one_cycles:
                        # 1+1账期：查询实时费用表
                        logging2.info(f"账期 {billing_cycle} 属于1+1账期，查询实时费用表")
                        month_mm = billing_cycle[4:]
                        replacements = [
                            ['##LATNID##', str(latn_id)],
                            ['##MM##', month_mm]
                        ]
                        params = [prod_inst_id]
                        
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QueryRealFeeOfferIds',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                    else:
                        # 其他月份：查询账单表
                        logging2.info(f"账期 {billing_cycle} 不属于1+1账期，查询账单表")
                        replacements = [
                            ['##LATNID##', str(latn_id)],
                            ['##BILLINGCYCLE##', billing_cycle]
                        ]
                        params = [prod_inst_id]
                        
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QueryAcctItemOfferIds',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                    
                    # 处理查询结果
                    for result_row in result_rows:
                        if result_row and result_row[0]:
                            ofr_id = result_row[0]
                            offer_id_list.append({
                                "billing_cycle": int(billing_cycle),
                                "ofrid": ofr_id
                            })
                            logging2.info(f"账期 {billing_cycle} 查询到 ofr_id: {ofr_id}")
                    
                    if not result_rows:
                        logging2.info(f"账期 {billing_cycle} 未查询到相关数据")
                        
                except Exception as db_ex:
                    logging2.error(f"查询账期 {billing_cycle} 时出错: {str(db_ex)}")

            # 添加查询结果
            response_list.append({
                "status": "success",
                "message": "查询成功",
                "PROD_INST_ID": prod_inst_id,
                "latn_id": latn_id,
                "offeridlist": offer_id_list
            })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询资费销售品完成",
            "input_details": response_list
        }
        
        logging2.info(f"查询资费销售品完成，返回结果: {final_response}")
        return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询资费销售品请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500

# 查询号码加入共享流量包的时间接口
@package_bp.route('/query/shared_flow_package_time', methods=['POST'])
def shared_flow_package_time():
    """
    查询号码加入共享流量包的时间接口
    """
    try:
        # 解析请求数据
        request_data = request.data.decode('utf-8')
        logging2.debug(f"查询共享流量包时间接口请求参数: {request_data}")
        json_dict = json.loads(request_data)

        # 获取input_details数组
        input_details = json_dict.get('input_details', [])
        if not input_details:
            return jsonify({
                "status": "error",
                "message": "缺少input_details参数"
            })

        response_list = []
        
        # 处理每个input_details中的项目
        for row in input_details:
            # 校验必要参数
            billing_nb = row.get('billing_nb')
            prod_inst_id = row.get('PROD_INST_ID')
            latn_id = row.get('latn_id')
            billing_cycle = row.get('billing_cycle')

            if not billing_nb or not prod_inst_id or not latn_id or not billing_cycle:
                logging2.warning("缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle")
                response_list.append({
                    "status": "error",
                    "message": "缺少必要参数 billing_nb、PROD_INST_ID、latn_id 或 billing_cycle",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            if len(billing_cycle) != 6:
                logging2.warning(f"无效的 billing_cycle 格式: {billing_cycle}")
                response_list.append({
                    "status": "error",
                    "message": "无效的 billing_cycle 格式，应为YYYYMM格式",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            logging2.info(f"开始查询共享流量包时间: billing_nb={billing_nb}, PROD_INST_ID={prod_inst_id}, latn_id={latn_id}, billing_cycle={billing_cycle}")

            # 调用AccuUseQryCenter接口
            config_path = get_config_path()
            if not config_path:
                response_list.append({
                    "status": "error",
                    "message": "找不到配置文件config.json",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue
                
            config_loader = ConfigLoader(config_path)
            url = config_loader.get_url("AccuUseQryCenter")
            if not url:
                response_list.append({
                    "status": "error",
                    "message": "配置中未找到AccuUseQryCenter接口URL",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })
                continue

            # 构建请求数据
            request_payload = {
                "operAttrStruct": {
                    "lanId": 0,
                    "operOrgId": 0,
                    "operPost": 0,
                    "operServiceId": "string",
                    "operTime": "string",
                    "staffId": 0
                },
                "accNbr": billing_nb,
                "destinationAttr": "2",
                "billingCycle": int(billing_cycle),
                "offerId": 0,
                "qryType": 2,
                "offerInstStr": 0,
                "acctId": 0
            }

            logging2.info(f"调用AccuUseQryCenter接口，请求参数: {request_payload}")

            # 发送请求并处理结果
            try:
                result = UnitTool.send_post_request(url, request_payload)
                logging2.info(f"AccuUseQryCenter接口返回结果: {result}")

                if not result or result.get("resultCode") != "0":
                    response_list.append({
                        "status": "error",
                        "message": f"AccuUseQryCenter接口调用失败: {result.get('resultMsg', '未知错误') if result else '接口无响应'}",
                        "billing_nb": billing_nb,
                        "billing_cycle": billing_cycle
                    })
                    continue

                # 提取共享定价实例的offerInstId集合
                shared_offer_inst_ids = []
                for offer_info in result.get("offerInstInfo", []):
                    offer_inst_id = offer_info.get("offerInstId")
                    accu_qry_list = offer_info.get("accuQryList", [])
                    
                    # 检查是否有ownerType=2的共享定价实例
                    has_shared_pricing = any(
                        accu.get("ownerType") == 2 
                        for accu in accu_qry_list
                    )
                    
                    if has_shared_pricing and offer_inst_id:
                        shared_offer_inst_ids.append(offer_inst_id)

                logging2.info(f"找到共享定价实例ID集合: {shared_offer_inst_ids}")

                # 查询每个共享定价实例的加入时间
                shared_package_times = []
                replacements = [['##LATNID##', str(latn_id)]]
                
                for offer_inst_id in shared_offer_inst_ids:
                    try:
                        params = [offer_inst_id, prod_inst_id]
                        result_rows = db_manager_instance.excute_sql(
                            sql_name='QuerySharedFlowPackageTime',
                            params=params,
                            lst_replace_code_value=replacements
                        )
                        
                        if result_rows and result_rows[0] and result_rows[0][0]:
                            update_date = result_rows[0][0]
                            shared_package_times.append({
                                "offerInstId": offer_inst_id,
                                "update_date": update_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(update_date, 'strftime') else str(update_date)
                            })
                            logging2.info(f"查询到共享流量包时间: offerInstId={offer_inst_id}, update_date={update_date}")
                        else:
                            logging2.warning(f"未查询到共享流量包时间: offerInstId={offer_inst_id}")
                            
                    except Exception as db_ex:
                        logging2.error(f"查询共享流量包时间出错: offerInstId={offer_inst_id}, 错误: {str(db_ex)}")

                # 添加查询结果
                response_list.append({
                    "status": "success",
                    "message": "查询成功",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle,
                    "PROD_INST_ID": prod_inst_id,
                    "latn_id": latn_id,
                    "sharedPackageTimes": shared_package_times
                })

            except Exception as req_ex:
                logging2.error(f"调用AccuUseQryCenter接口时出错: {str(req_ex)}")
                response_list.append({
                    "status": "error",
                    "message": f"调用AccuUseQryCenter接口异常: {str(req_ex)}",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                })

        # 返回结果
        final_response = {
            "status": "success",
            "message": "查询共享流量包时间完成",
            "input_details": response_list
        }
        
        # 检查是否有sharedPackageTimes数据，如果有则添加可视化
        has_shared_data = False
        for item in response_list:
            if isinstance(item, dict) and item.get("sharedPackageTimes"):
                has_shared_data = True
                break
        
        if has_shared_data:
            return jsonify(add_visualization_data("shared_flow_package_time", final_response, "查询共享流量包时间", ""))
        else:
            return jsonify(final_response)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理查询共享流量包时间请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500

#定向-定价协议查询
@package_bp.route('/query/userDirectionalOfferPricing', methods=['POST'])
def userDirectionalOfferPricing():
    """
    查询用户定向销售品定价信息
    """
    try:
        # 解析请求数据
        request_data = json.loads(request.data.decode('utf-8'))
        logging2.debug(f"请求参数: {request_data}")

        # 验证请求数据结构
        if 'input_details' not in request_data or not isinstance(request_data['input_details'], list):
            raise ValueError("请求数据缺少input_details列表")
        
        # 初始化响应列表
        response_list = []

        # 处理每个输入项
        for input_row in request_data['input_details']:
            # 获取必要参数
            billing_nb = input_row.get('billing_nb')
            prod_inst_id = input_row.get('PROD_INST_ID')
            latn_id = input_row.get('latn_id')
            billing_cycle = input_row.get('billing_cycle')

            # 参数验证
            if not all([billing_nb, prod_inst_id, latn_id, billing_cycle]):
                logging2.warning(f"输入项缺少必要参数: {input_row}")
                response_item = {
                    "status": "error",
                    "message": "缺少必要参数",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 初始化销售品详情列表
            offer_detail = []

            # 查询销售品关系历史信息
            replacements = [['##LATNID##', str(latn_id)]]
            params = [prod_inst_id, billing_cycle, billing_cycle]
            try:
                a1_rows = db_manager_instance.excute_sql(
                    sql_name='QueryOfferProdInstRelHisA1Info',
                    params=params,
                    lst_replace_code_value=replacements
                )
                logging2.debug(f"查询销售品关系历史信息成功，结果数: {len(a1_rows)}")
            except Exception as e:
                logging2.error(f"查询销售品关系历史信息失败: {e}, params={params}")
                response_item = {
                    "status": "error",
                    "message": "数据库查询失败",
                    "billing_nb": billing_nb,
                    "billing_cycle": billing_cycle
                }
                response_list.append(response_item)
                continue

            # 处理每个销售品实例
            for offer_row in a1_rows:
                try:
                    (offer_inst_id, prod_offer_id, eff_date,
                     exp_date, update_date, obj_id, obj_type) = offer_row

                    # 查询定向销售品定价
                    pricing_params = [prod_offer_id]
                    a2_rows = db_manager_instance.excute_sql(
                        sql_name='QueryDirectionalOfferPricing',
                        params=pricing_params,
                        lst_replace_code_value=replacements
                    )
                    logging2.debug(f"查询定向销售品定价成功，结果数: {len(a2_rows)}")

                    # 处理每个定价记录
                    for offerPricing_row in a2_rows:
                        offer_id, offer_name, pricing_plan_id, value_string = offerPricing_row

                        # 构建销售品详情（确保日期字段为字符串类型）
                        offer_info = {
                            "offer_inst_id": offer_inst_id,
                            "offer_id": prod_offer_id,
                            "eff_date": str(eff_date),
                            "exp_date": str(exp_date),
                            "offer_rg": value_string
                        }
                        offer_detail.append(offer_info)

                except Exception as e:
                    logging2.error(f"处理销售品实例时发生错误: {e}, prod_offer_id={prod_offer_id}")

            # 构建单条响应数据
            response_item = {
                "status": "success" if offer_detail else "warning",
                "message": "查询成功" if offer_detail else "未找到定价信息",
                "billing_nb": billing_nb,
                "billing_cycle": billing_cycle,
                "offer_detail": offer_detail
            }
            response_list.append(response_item)

        # 返回完整响应
        return jsonify(response_list)

    except json.JSONDecodeError:
        logging2.error("请求数据不是有效的JSON格式")
        return jsonify({
            "status": "error",
            "message": "无效的JSON格式"
        })
    except Exception as e:
        logging2.error(f"处理定向销售品定价查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": "服务器内部错误"
        }), 500

# 套餐使用明细和账单信息查询服务
@package_bp.route('/query/package_usage_billing_service', methods=['POST'])
def package_usage_billing_service():
    """
    套餐使用明细和账单信息查询接口
    """
    try:
        data = request.data.decode('utf-8')
        logging2.debug(f"套餐使用明细和账单信息查询请求参数: {data}")
        json_dict = json.loads(data)

        # 获取请求参数
        billing_nb = json_dict.get('billing_nb')
        billing_cycle = json_dict.get('billing_cycle')
        latn_id = json_dict.get('latn_id')

        # 基础参数校验
        if not billing_nb or not billing_cycle:
            return jsonify({
                "status": "failed", 
                "message": "缺少必要参数 billing_nb 或 billing_cycle"
            })

        if len(billing_cycle) != 6:
            return jsonify({
                "status": "failed", 
                "message": "无效的 billing_cycle 格式"
            })

        # 获取本地网ID（如果缺失则查询）
        latn_id, error_msg = get_latn_id_if_missing(billing_nb, latn_id)
        if not latn_id:
            return jsonify({
                "status": "failed", 
                "message": error_msg
            })

        logging2.info(f"开始查询套餐使用明细和账单信息: billing_nb={billing_nb}, billing_cycle={billing_cycle}, latn_id={latn_id}")

        # 查询用户资料信息
        user_profile_data = query_user_profile_internal(billing_nb, latn_id, billing_cycle)
        if not user_profile_data or user_profile_data.get('Is_user_profile') != 1:
            return jsonify({
                "status": "failed", 
                "message": "未找到用户资料信息"
            })

        prod_inst_id = user_profile_data.get('PROD_INST_ID')
        acct_id = user_profile_data.get('ACCT_ID')

        # 并行查询套餐使用明细和账单信息
        package_usage_data = query_package_usage_internal(prod_inst_id, latn_id, billing_cycle, acct_id, billing_nb)
        billing_data = query_billing_info_internal(acct_id, billing_cycle, latn_id)

        # 返回数据
        response_data = {
            "status": "success",
            "message": "查询成功",
            "billing_nb": billing_nb,
            "billing_cycle": billing_cycle,
            "offerAccuDetail": package_usage_data.get('offerAccuDetail', []), # 套餐使用明细信息
            **{k: v for k, v in billing_data.items() if k != 'Is_charge_detail'} # 账单信息
        }

        return jsonify(response_data)

    except Exception as e:
        logging2.error(f"处理套餐使用明细和账单信息查询请求时出错: {str(e)}")
        logging2.error(f"{traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500
    
def query_user_profile_internal(billing_nb, latn_id, billing_cycle):
    """
    用户资料查询
    """
    try:
        replacements = [['##LATNID##', str(latn_id)]]
        params = [billing_cycle, billing_nb]
        
        result = db_manager_instance.excute_sql(
            sql_name='QueryUserProfile',
            params=params,
            lst_replace_code_value=replacements
        )
        
        if not result or len(result[0]) != 3:
            return {"Is_user_profile": 0}
            
        prod_inst_id, acct_id, prod_id = result[0]
        
        return {
            "Is_user_profile": 1,
            "PROD_INST_ID": prod_inst_id,
            "ACCT_ID": acct_id,
            "PROD_ID": prod_id
        }
    except Exception as e:
        logging2.error(f"用户资料查询出错: {str(e)}")
        return {"Is_user_profile": 0}

def query_billing_info_internal(acct_id, billing_cycle, latn_id):
    """
    账单信息查询方法（查询4种费用类型）
    """
    try:
        if len(billing_cycle) != 6:
            return {
                "Is_charge_detail": 0, 
                "basic_package_fee": 0.00,
                "flux_booster_fee": 0.00, 
                "night_flux_fee": 0.00,
                "overflow_flux_fee": 0.00
            }
        
        # 使用通用费用查询方法
        fee_configs = [
            ('QueryBasicPackageFee', 'basic_package_fee', '套餐基础费用'),
            ('QueryFluxBoosterFee', 'flux_booster_fee', '流量加油包费用'),
            ('QueryNightFluxFee', 'night_flux_fee', '夜间流量包费用'),
            ('QueryOverflowFluxFee', 'overflow_flux_fee', '套餐外流量费用')
        ]
        
        result = {"Is_charge_detail": 0}
        has_any_fee = False
        
        for sql_name, field_name, description in fee_configs:
            fee_amount = query_fee_by_type(acct_id, billing_cycle, latn_id, sql_name, description)
            result[field_name] = fee_amount
            if fee_amount > 0:
                has_any_fee = True
        
        result["Is_charge_detail"] = 1 if has_any_fee else 0
        return result
        
    except Exception as e:
        logging2.error(f"账单信息查询出错: {str(e)}")
        return {
            "Is_charge_detail": 0,
            "basic_package_fee": 0.00,
            "flux_booster_fee": 0.00,
            "night_flux_fee": 0.00,
            "overflow_flux_fee": 0.00
        }
    
def query_fee_by_type(acct_id, billing_cycle, latn_id, sql_name, description="费用"):
    """
    通用费用查询
    """
    try:
        month_mm = billing_cycle[4:]
        replacements = [
            ['##LATNID##', str(latn_id)],
            ['##MM##', month_mm]
        ]
        params = [acct_id]
        
        result = db_manager_instance.excute_sql(
            sql_name=sql_name,
            params=params,
            lst_replace_code_value=replacements
        )
        return round(float(result[0][0] or 0), 2) if result and result[0][0] else 0.00
    except Exception as e:
        logging2.error(f"查询{description}出错: {e}")
        return 0.00

def query_package_usage_internal(prod_inst_id, latn_id, billing_cycle, acct_id, billing_nb):
    """
    套餐使用明细查询
    """
    try:
        result_data = {"offerAccuDetail": []}
        
        # 获取配置文件路径和URL
        config_path = get_config_path()
        if not config_path:
            logging2.error("找不到配置文件config.json")
            return result_data
            
        config_loader = ConfigLoader(config_path)
        url = config_loader.get_url("AccuUseQryCenter")
        if not url:
            logging2.error("配置中未找到AccuUseQryCenter接口URL")
            return result_data

        # 构建openapi请求数据
        request_payload = {
            "operAttrStruct": {
                "lanId": 0,
                "operOrgId": 0,
                "operPost": 0,
                "operServiceId": "string",
                "operTime": "string",
                "staffId": 0
            },
            "accNbr": billing_nb,
            "destinationAttr": "2",
            "billingCycle": int(billing_cycle),
            "offerId": 0,
            "qryType": 2,
            "offerInstStr": 0,
            "acctId": 0
        }

        logging2.info(f"调用AccuUseQryCenter接口，请求参数: {request_payload}")

        # 发送请求并处理结果
        try:
            from UnitTool import UnitTool
            result = UnitTool.send_post_request(url, request_payload)
            logging2.info(f"AccuUseQryCenter接口返回结果: {result}")

            if not result or result.get("resultCode") != "0":
                logging2.error(f"AccuUseQryCenter接口调用失败: {result.get('resultMsg', '未知错误') if result else '接口无响应'}")
                return result_data

            # 处理返回数据
            clean_pkgs = []
            for offer_info in result.get("offerInstInfo", []):
                offer_id = offer_info.get("offerId")
                offer_name = offer_info.get("offerName")
                offer_inst_id = offer_info.get("offerInstId")
                eff_date = offer_info.get("EFF_DATE")
                exp_date = offer_info.get("EXP_DATE")
                accu_qry_list = offer_info.get("accuQryList", [])
                
                # 只处理unitTypeId为3的数据（流量数据）
                filtered_accu_list = [accu for accu in accu_qry_list if str(accu.get("unitTypeId", "")) == "3"]
                
                if not filtered_accu_list:
                    continue  # 如果没有流量数据，跳过这个套餐
                
                # 判断是否为共享套餐：ownerType为2代表共享
                shared_owner_ids = set()
                is_shared = False
                total_init_val = 0
                total_used_val = 0
                total_remain_val = 0
                
                for accu in filtered_accu_list:
                    owner_type = accu.get("ownerType")
                    owner_id = accu.get("ownerId")
                    init_val = accu.get("initVal", 0)
                    usage_val = accu.get("usageVal", 0)
                    accu_val = accu.get("accuVal", 0)
                    
                    if owner_type == 2:  # 共享类型
                        is_shared = True
                        if owner_id:
                            shared_owner_ids.add(owner_id)
                    
                    # 累加流量数据
                    total_init_val += init_val
                    total_used_val += usage_val
                    total_remain_val += accu_val
                
                # 格式化日期
                try:
                    if eff_date and len(str(eff_date)) == 14:  # YYYYMMDDHHMMSS格式
                        eff_date_formatted = datetime.strptime(str(eff_date), '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        eff_date_formatted = str(eff_date) if eff_date else ""
                        
                    if exp_date and len(str(exp_date)) == 14:  # YYYYMMDDHHMMSS格式
                        exp_date_formatted = datetime.strptime(str(exp_date), '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        exp_date_formatted = str(exp_date) if exp_date else ""
                except:
                    eff_date_formatted = str(eff_date) if eff_date else ""
                    exp_date_formatted = str(exp_date) if exp_date else ""
                
                # 构建套餐数据
                clean_pkg = {
                    "offer_inst_id": offer_inst_id,
                    "offer_id": offer_id,
                    "offerName": offer_name,
                    "EFF_DATE": eff_date_formatted,
                    "EXP_DATE": exp_date_formatted,
                    "shared_flag": 1 if is_shared else 0,
                    "shared_obj_ids": list(shared_owner_ids),
                    "total_traffic": round(total_init_val / 1024 / 1024, 2),  # 转换为GB
                    "used_traffic": round(total_used_val / 1024 / 1024, 2),   # 转换为GB
                    "remain_traffic": round(total_remain_val / 1024 / 1024, 2), # 转换为GB
                    "usage_rate": round((total_used_val / total_init_val) * 100, 1) if total_init_val > 0 else 0
                }
                clean_pkgs.append(clean_pkg)
            
            result_data["offerAccuDetail"] = clean_pkgs
            return result_data

        except Exception as req_ex:
            logging2.error(f"调用AccuUseQryCenter接口时出错: {str(req_ex)}")
            return result_data
        
    except Exception as e:
        logging2.error(f"套餐使用明细查询出错: {str(e)}")
        return {"offerAccuDetail": []}
