# 导入数据库连接库
import pymysql,psycopg2,cx_Oracle
import time
import traceback
import xml.etree.ElementTree as ET
from desTool import ASEUtil
import logging2
from queue import Queue

class DBPyMgr:
    def __init__(self, cfg_xml_file, db_name=None):  # 初始化数据库管理器
        self.cfg_xml_file = cfg_xml_file
        self.m_db_name = db_name
        self.m_db_info = {}
        self.m_sql_cluster_info = {}
        self.m_conn_pools = {}  # 改为使用连接池字典

    def init(self) -> bool:
        """初始化数据库管理器，步骤包括：
        1. Loading configuration
        2. Validating configuration
        3. Establishing database connections
        
        Returns:
            bool: True if initialization succeeded, False otherwise
        """
        try:
            if not self.init_config():
                raise Exception("Failed to initialize configuration")
            if not self.check_cfg_info():
                raise Exception("Invalid configuration")
            if not self.create_conn():
                raise Exception("Failed to create connections")
            return True
        except Exception as e:
            logging2.debug(f"Initialization failed: {str(e)}")
            logging2.debug(traceback.format_exc())
            return False

    def init_config(self):  # 初始化配置
        try:
            # 解析XML配置文件
            tree = ET.parse(self.cfg_xml_file)
            root = tree.getroot()

            # 获取db_set下所有db的信息
            self.m_db_info = {}
            db_set = root.find('db_set')
            if db_set is not None:
                for db in db_set.findall('db'):
                    db_name = db.get('name')
                    db_type = db.get('type')
                    connect_master_info = {}
                    connect_slave_info = {}
                    connect = db.find('master')
                    if connect is not None:
                        for param in connect.findall('param'):
                            connect_master_info[param.get('name')] = param.text
                    connect = db.find('slave')
                    if connect is not None:
                        for param in connect.findall('param'):
                            connect_slave_info[param.get('name')] = param.text
                    self.m_db_info[db_name] = {'type': db_type,'master': connect_master_info, 'slave': connect_slave_info}

            # 获取sql_set下所有sql_cluster的信息
            self.m_sql_cluster_info = {}
            sql_set = root.find('sql_set')
            if sql_set is not None:
                for sql_cluster in sql_set.findall('sql_cluster'):
                    db_name = sql_cluster.get('db')
                    for sql in sql_cluster.findall('sql'):
                        sql_name = sql.get('name')
                        sql_text = sql.text.strip() if sql.text is not None else ''
                        self.m_sql_cluster_info[sql_name] = {'db_name': db_name, 'sqltext': sql_text}
                        
            logging2.debug('init success')
            return True
        except Exception as e:
            logging2.debug(f'error:{e} {traceback.format_exc()}')
            return False

    def check_cfg_info(self):  # 检查配置文件信息
        try:
            # 解析XML配置文件
            tree = ET.parse(self.cfg_xml_file)
            root = tree.getroot()

            db_names = []
            sql_names = []
            db_duplicates = []
            sql_duplicates = []

            # 检查db_set中db的name是否重复
            db_set = root.find('db_set')
            if db_set is not None:
                for db in db_set.findall('db'):
                    db_name = db.get('name')
                    if db_name in db_names:
                        db_duplicates.append(db_name)
                    else:
                        db_names.append(db_name)

            # 检查sql_set中sql的name是否重复
            sql_set = root.find('sql_set')
            if sql_set is not None:
                for sql_cluster in sql_set.findall('sql_cluster'):
                    sqls = sql_cluster.findall('sql')
                    for sql in sqls:
                        sql_name = sql.get('name')
                        if sql_name in sql_names:
                            sql_duplicates.append(sql_name)
                        else:
                            sql_names.append(sql_name)

            if db_duplicates:
                logging2.debug("发现重复的db name，重复的名称如下:")
                for dup in db_duplicates:
                    logging2.debug(dup)
                return False
            else:
                logging2.debug("没有发现重复的db name")

            if sql_duplicates:
                logging2.debug("发现重复的sql name，重复的名称如下:")
                for dup in sql_duplicates:
                    logging2.debug(dup)
                return False
            else:
                logging2.debug("没有发现重复的sql name")
            return True
        except Exception as e:
            logging2.debug(f'error:{e} {traceback.format_exc()}')
            return False

    def create_connection(self, db_info):  # 创建数据库连接
        if db_info['type'] == 'mysql':
            return pymysql.connect(
                host=db_info['master']['host'],
                port=int(db_info['master']['port']),
                user=db_info['master']['user'],
                password=ASEUtil.decrypted(db_info['master']['password']),
                db=db_info['master']['db']
            )
        elif db_info['type'] == 'oracle':
            return cx_Oracle.connect(
                db_info['master']['user'],
                ASEUtil.decrypted(db_info['master']['password']),
                f"{db_info['master']['host']}:{db_info['master']['port']}/{db_info['master']['db']}"
            )
        elif db_info['type'] == 'pgsql':
            return psycopg2.connect(
                host=db_info['master']['host'],
                port=int(db_info['master']['port']),
                user=db_info['master']['user'],
                password=ASEUtil.decrypted(db_info['master']['password']),
                dbname=db_info['master']['db']
            )
        elif db_info['type'] == 'doris':
            return pymysql.connect(
                host=db_info['master']['host'],
                port=int(db_info['master']['port']),
                user=db_info['master']['user'],
                password=ASEUtil.decrypted(db_info['master']['password']),
                db=db_info['master']['db']
            )
        else:
            logging2.debug(f'error: db type {db_info["type"]} not support')
            return None

    def connect_to_db(self, db_name):  # 连接到指定数据库
        try:
            if self.m_db_info.get(db_name) is None:
                logging2.debug(f'{db_name} not found')
                return False

            db_info = self.m_db_info[db_name]
            conn_num = int(db_info['master']['connNum'])
            
            # 为每个数据库创建连接池
            self.m_conn_pools[db_name] = Queue(maxsize=conn_num*10)
            
            for _ in range(conn_num):
                conn = self.create_connection(db_info)
                if conn:
                    self.m_conn_pools[db_name].put(conn, block=True, timeout=5)
                    logging2.debug(f"{db_info['type']} {db_name} connect success")
                else:
                    return False
            return True
        except Exception as e:
            logging2.error(f'error:{e} {traceback.format_exc()}')
            return False

    def add_connection(self, db_name):  # 添加数据库连接
        try:
            if self.m_db_info.get(db_name) is None:
                logging2.debug(f'{db_name} not found')
                return None

            db_info = self.m_db_info[db_name]
            conn = self.create_connection(db_info)
            if conn:
                self.m_conn_pools[db_name].put(conn, block=True, timeout=5)
                logging2.debug(f"{db_info['type']} {db_name} connect success")
                return conn
            else:
                logging2.error(f"{db_info['type']} {db_name} connect fail")
                return None
        except Exception as e:
            logging2.error(f'error:{e} {traceback.format_exc()}')
            return None

    def create_conn(self):  # 创建数据库连接池
        try:
            self.m_conn_pools = {}
            
            if self.m_db_name is None:
                for db_name in self.m_db_info:
                    if not self.connect_to_db(db_name):
                        return False
            else:
                if not self.connect_to_db(self.m_db_name):
                    return False
                    
            logging2.debug('create conn success')
            return True
        except Exception as e:
            logging2.debug(f'error:{e} {traceback.format_exc()}')
            return False


    def check_conn(self, conn, db_name):  # 检查数据库连接是否有效
        try:
            db_info = self.m_db_info[db_name]
            if db_info['type'] == 'oracle':
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                cursor.close()
                return True
            elif db_info['type'] == 'mysql':
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                return True
            elif db_info['type'] == 'pgsql':
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                return True
            elif db_info['type'] == 'doris':
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                return True
            else:
                logging2.error(f'error: db type {db_info["type"]} not support')
                return False
        except Exception as e:
            logging2.error(f'Connection check failed: {str(e)}')
            return False


    def get_conn(self, db_name, timeout=5):  # 获取数据库连接
        try:
            while True:
                if self.m_conn_pools.get(db_name) is None or self.m_conn_pools[db_name].empty():
                    self.add_connection(db_name)
                conn = self.m_conn_pools[db_name].get(timeout=timeout)
                if self.check_conn(conn, db_name):
                    return conn
                else:
                    try:
                        conn.close()
                    except:
                        pass
                    logging2.error(f'Connection {db_name} is invalid, reconnecting...')
                    self.add_connection(db_name)
        except Exception as e:
            logging2.error(f'Get connection from {db_name} failed: {str(e)}')
            return None

    def release_conn(self, db_name, conn):  # 回收数据库连接
        try:
            self.m_conn_pools[db_name].put(conn, block=True, timeout=5)
        except Exception as e:
            logging2.error(f'Release connection to {db_name} failed: {str(e)}')
            try:
                conn.close()
            except:
                pass

    def excute_sql(self, sql_name, params=None, lst_replace_code_value=[], retry=1):  # 执行SQL语句
        if self.m_sql_cluster_info.get(sql_name) is None:
            logging2.error(f'error:sql {sql_name} not found')
            return []

        db_name = self.m_sql_cluster_info[sql_name]['db_name']
        if self.m_conn_pools.get(db_name) is None:
            logging2.error(f'error:db {db_name} not connect')
            return []
        if self.m_db_info.get(db_name) is None:
            logging2.error(f'error:db {db_name} not found')
            return []
        if self.m_db_info[db_name].get('type') is None:
            logging2.error(f'error:db {db_name} type not found')
            return []
        if self.m_db_info[db_name]['type'] not in ['mysql', 'oracle', 'pgsql', 'doris']:
            logging2.error(f'error:db {db_name} type not support')
            return []
        db_type = self.m_db_info[db_name]['type']
        try:
            conn = self.get_conn(db_name)
            if conn is None:
                logging2.error(f'Get connection for {db_name} failed')
                return []
            cursor = conn.cursor()
            sqlText = self.m_sql_cluster_info[sql_name]['sqltext']
            sqlText = self.sql_text_replace(sqlText, lst_replace_code_value)
            logging2.debug(f'excute sql db_name: {db_name} sql_name:{sql_name} sqlText:{sqlText} params:{params}')

            try:
                if db_type == 'pgsql':
                    if params:
                        for value in params:
                            sqlText = sqlText.replace(":v", f"'{value}'" if isinstance(value, str) else str(value), 1)
                    cursor.execute(sqlText)
                else:
                    if params:
                        sqlText = sqlText.replace(':v', '%s')
                        cursor.execute(sqlText, params)
                    else:
                        cursor.execute(sqlText)

                if sqlText.lower().startswith('select'):
                    result = cursor.fetchall()
                    self.release_conn(db_name, conn)
                    logging2.debug(f'excute sql result:{result}')
                    return result
                else:
                    result = cursor.rowcount
                    conn.commit()
                    self.release_conn(db_name, conn)
                    logging2.debug(f'excute sql result:{result}')
                    return result
            except Exception as e:
                logging2.error(f'error:{e} {traceback.format_exc()}')
                logging2.error(f'{sql_name} {sqlText} excute fail')
                try:
                    conn.close()
                except:
                    pass
                if retry:
                    logging2.info(f'Retrying {retry} times...')
                    return self.excute_sql(sql_name, params, lst_replace_code_value, retry - 1)
                else:
                    return []
        except Exception as e:
            logging2.error(f'Get connection for {sql_name} failed: {str(e)}')
            return []

    def sql_text_replace(self, sqlText, lst_replace_code_value):  # 替换SQL文本中的占位符
        for lst_value in lst_replace_code_value:
            if len(lst_value) != 2:
                logging2.warn(f'lst_replace_code_value len is not 2')
                continue
            sqlText = sqlText.replace(lst_value[0], lst_value[1])
        return sqlText

    def excute_sqltext(self, db_name, sql_text, params=None, retry=1):  # 执行原始SQL文本
        try:
            conn = self.get_conn(db_name)
            if conn is None:
                logging2.error(f'Get connection for {db_name} failed')
                return []
            cursor = conn.cursor()
            sqlText = sql_text
            logging2.debug(f'excute sql sqlText:{sqlText} params:{params}')

            try:
                if params:
                    sqlText = sqlText.replace(':v', '%s')
                    cursor.execute(sqlText, params)
                else:
                    cursor.execute(sqlText)

                if sqlText.lower().startswith('select'):
                    result = cursor.fetchall()
                    self.release_conn(db_name, conn)
                    return result
                else:
                    result = cursor.rowcount
                    conn.commit()
                    self.release_conn(db_name, conn)
                    return result
            except Exception as e:
                logging2.debug(f'{sqlText} excute fail')
                logging2.debug(f'error:{e} {traceback.format_exc()}')
                try:
                    conn.close()
                except:
                    pass
                if retry:
                    logging2.info(f'Retrying {retry} times...')
                    return self.excute_sqltext(db_name, sql_text, params, retry - 1)
                else:
                    return []
        except Exception as e:
            logging2.error(f'Get connection for {db_name} failed: {str(e)}')
            return []

    def stop(self):  # 停止数据库管理器并关闭所有连接
        try:
            for db_name, pool in self.m_conn_pools.items():
                while not pool.empty():
                    try:
                        conn = pool.get_nowait()
                        conn.close()
                        logging2.debug(f'{db_name} connection closed')
                    except:
                        pass
        except Exception as e:
            logging2.debug(f'error:{e} {traceback.format_exc()}')
